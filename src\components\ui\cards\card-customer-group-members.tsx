import React from 'react'
import { But<PERSON>, Checkbox, Flex, Grid, Text } from '@chakra-ui/react'
import CardContainer from './card-container'
import FlagIcon from '@/assets/icons/flag-icon'
import TrashIcon from '@/assets/icons/trash'
import { GroupMember } from '@/types/GroupMembersTypes'

type CardCustomerGroupMembersProps = {
    members: GroupMember[]
}

export default function CardCustomerGroupMembers({ members }: CardCustomerGroupMembersProps) {
  return (
    <CardContainer variant={'bordered'}>
        <Flex w={'100%'} gap={'.5rem'}>
        <Flex height={'20px'}>
            <Checkbox borderColor={'black'} size={'sm'} />
        </Flex>
        <Flex gap={'1rem'} flexDirection={'column'}>
            <Text fontSize={'md'} fontWeight={'bold'}>
            お客様グループメンバー
            </Text>
            <Text fontSize={'sm'} fontWeight={'medium'}>
            4名様 / 最大5名
            </Text>
        </Flex>
        </Flex>

        <Grid w={'100%'} px={'1rem'} templateColumns="1fr 1fr" gap={2}>
            {members.map((member, index) => (
                <Flex key={index} w={'100%'} justifyContent={'space-between'} alignItems={'center'}>
                    <Text fontSize={'sm'}>{member.name}</Text>
                    {member.isFlagged ? (
                        <Button
                            variant={'ghost'}
                            p={0}
                            pl={'.4rem'}
                            m={0}
                            gap={2}
                            h={'auto'}
                            _hover={{ bg: 'transparent' }}
                        >
                            <FlagIcon width={15} height={15} />
                        </Button>
                    ) : (
                        <Button
                            variant={'ghost'}
                            p={0}
                            m={0}
                            gap={2}
                            h={'auto'}
                            _hover={{ bg: 'transparent' }}
                        >
                            <TrashIcon width={20} height={20} />
                        </Button>
                    )}
                </Flex>
            ))}
        </Grid>

        <Flex w={'100%'} gap={'1rem'}>
        <Button variant={'rounded'} py={'1.3rem'} bg={'borderGray'}>
            <Text fontWeight={'normal'}>最大人数変更</Text>
        </Button>
        <Button variant={'roundedBlue'} py={'1.3rem'}>
            <Text color={'white'} fontWeight={'normal'}>
            メンバー追加
            </Text>
        </Button>
        </Flex>
    </CardContainer>
  )
}
