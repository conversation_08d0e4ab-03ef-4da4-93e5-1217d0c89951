import type { NextApiRequest, NextApiResponse } from 'next';

type InvitationRequest = {
  emails: string[];
  inviterName: string;
};

type InvitationResponse = {
  success: boolean;
  message: string;
  sentEmails?: string[];
  failedEmails?: string[];
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<InvitationResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed',
    });
  }

  try {
    const { emails, inviterName }: InvitationRequest = req.body;

    // Validate request data
    if (!emails || !Array.isArray(emails) || emails.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Email addresses are required',
      });
    }

    if (!inviterName) {
      return res.status(400).json({
        success: false,
        message: 'Inviter name is required',
      });
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const validEmails = emails.filter(email => emailRegex.test(email));

    if (validEmails.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No valid email addresses provided',
      });
    }

    // Simulate email sending process
    // In a real implementation, you would integrate with an email service like:
    // - SendGrid
    // - AWS SES
    // - Nodemailer with SMTP
    // - Resend
    // - etc.

    const sentEmails: string[] = [];
    const failedEmails: string[] = [];

    for (const email of validEmails) {
      try {
        // Simulate email sending with random success/failure for demo
        const shouldSucceed = Math.random() > 0.1; // 90% success rate for demo

        if (shouldSucceed) {
          // Here you would call your email service
          // await emailService.send({
          //   to: email,
          //   subject: 'Invitation to Join JUSTYOYAKU',
          //   template: 'invitation',
          //   data: { inviterName }
          // });

          sentEmails.push(email);
        } else {
          failedEmails.push(email);
        }
      } catch (error) {
        console.error(`Failed to send email to ${email}:`, error);
        failedEmails.push(email);
      }
    }

    // Determine overall success
    const success = sentEmails.length > 0;

    return res.status(success ? 200 : 500).json({
      success,
      message: success
        ? `Successfully sent ${sentEmails.length} invitation(s)`
        : 'Failed to send any invitations',
      sentEmails,
      failedEmails: failedEmails.length > 0 ? failedEmails : undefined,
    });
  } catch (error) {
    console.error('Error in invitation API:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
}
