import { isFormValid } from '@/utils/isMyPageFormValid';
import {
  Button,
  Flex,
  FormControl,
  FormLabel,
  Input,
  Link,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  Modal<PERSON>ooter,
  ModalHeader,
  ModalOverlay,
  Text,
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';

export default function ViewUserInformationModal({
  isOpen,
  errors,
  data,
  controlData,
  onClose,
  setData,
  setErrors,
  handleSave,
}: {
  isOpen: boolean;
  errors: any;
  data: any;
  controlData: any;
  onClose: () => void;
  setData: (value: any) => void;
  setErrors: (value: any) => void;
  handleSave: any;
}) {
  const { t } = useTranslation('profile');
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <form>
        <ModalOverlay />
        <ModalContent bg={'mainBackGroundColor'} pt={'2rem'}>
          <ModalHeader display={'flex'} alignItems={'center'} h={'5rem'}>
            <Text fontSize={'2rem'} fontWeight={'bold'}>
              {t('members.editMemberInfo')}
            </Text>
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody fontSize={'sm'}>
            <Flex
              bg={'white'}
              p={'1rem'}
              borderRadius={'1rem'}
              flexDirection={'column'}
              gap={'1rem'}
            >
              <Text mb={4} fontSize={'md'} fontWeight={'bold'}>
                {t('navigation.userInfo')}
              </Text>
              <FormControl isInvalid={!!errors.fullName}>
                <FormLabel color={'gray.600'}>{t('members.reservationHolderName')}</FormLabel>
                <Input
                  value={data.clientData.fullName}
                  onChange={e =>
                    setData({
                      ...data,
                      clientData: {
                        ...data.clientData,
                        fullName: e.target.value,
                      },
                    })
                  }
                />
                {errors.fullName && (
                  <Text color="red.500" fontSize="sm">
                    {errors.fullName}
                  </Text>
                )}
              </FormControl>

              <FormControl isInvalid={!!errors?.kana}>
                <FormLabel color={'gray.600'}>{t('members.fullNameKana')}</FormLabel>
                <Input
                  value={data.clientData.kana}
                  onChange={e =>
                    setData({
                      ...data,
                      clientData: {
                        ...data.clientData,
                        kana: e.target.value,
                      },
                    })
                  }
                />
                {errors.kana && (
                  <Text color="red.500" fontSize="sm">
                    {errors.kana}
                  </Text>
                )}
              </FormControl>

              <FormControl isInvalid={!!errors?.phoneNumber}>
                <FormLabel color={'gray.600'}>{t('members.phoneNumber')}</FormLabel>
                <Input
                  type={'number'}
                  value={data.clientData.phoneNumber}
                  onChange={e =>
                    setData({
                      ...data,
                      clientData: {
                        ...data.clientData,
                        phoneNumber: e.target.value,
                      },
                    })
                  }
                />
                {errors.phoneNumber && (
                  <Text color="red.500" fontSize="sm">
                    {errors.phoneNumber}
                  </Text>
                )}
              </FormControl>

              <FormControl isInvalid={!!errors?.email}>
                <FormLabel color={'gray.600'}>{t('members.emailAddress')}</FormLabel>
                <Input
                  value={data.clientData.email}
                  onChange={e =>
                    setData({
                      ...data,
                      clientData: {
                        ...data.clientData,
                        email: e.target.value,
                      },
                    })
                  }
                />
                {errors.email && (
                  <Text color="red.500" fontSize="sm">
                    {errors.email}
                  </Text>
                )}
              </FormControl>
            </Flex>
          </ModalBody>

          <Flex justifyContent={'flex-end'} px={'1.5rem'} pt={'1rem'}>
            <Text>
              {t('withdrawal.withdrawalMessage')}
              <Link href={'/client/my-page/withdrawal'}>{t('withdrawal.here')}</Link>
              {t('withdrawal.fromHere')}
            </Text>
          </Flex>
          <ModalFooter pt={0} gap={4} justifyContent={'center'}>
            <Button
              px={'3rem'}
              w={'100%'}
              variant="rounded"
              borderColor={'black'}
              onClick={onClose}
            >
              <span>{t('actions.back')}</span>
            </Button>
            <Button
              px={'3rem'}
              w={'100%'}
              variant="roundedBlue"
              isDisabled={!isFormValid({ data, controlData })}
              onClick={handleSave}
            >
              <span>{t('actions.save')}</span>
            </Button>
          </ModalFooter>
        </ModalContent>
      </form>
    </Modal>
  );
}
