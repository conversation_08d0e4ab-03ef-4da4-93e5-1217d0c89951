import BackIcon from '@/assets/icons/back';
import ConfirmationModal from '@/components/modals/comfirmation-modal';
import TextPair from '@/components/ui/TextPair';
import ImageInput from '@/components/ui/formfields/ImageInput';
import UnderlinedInputField from '@/components/ui/formfields/UnderlinedInputField';
import StoreBanner from '@/components/ui/store-banner';
import { Button, Flex, Text, useDisclosure } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Link from 'next/link';
import { useState } from 'react';

export default function StoreDetailsAddPage() {
  const { t } = useTranslation('admin');
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [modalStep, setModalStep] = useState(1);
  const [formData, setFormData] = useState({});

  const handleSubmit = () => {
    console.log('Form submitted:', formData);
    setModalStep(2);
  };

  const handleClose = () => {
    setModalStep(1);
    onClose();
  };

  return (
    <Flex
      w={'100%'}
      flexDirection={'column'}
      pr={{ base: 0, md: 6 }}
      pl={{ base: 0, md: 6, lg: 0 }}
    >
      <Flex
        bg={{ base: 'white', md: 'unset' }}
        px={{ base: 5, lg: 0 }}
        alignItems={'center'}
        height={'5rem'}
        w={'100%'}
        gap={'1rem'}
      >
        <Link href={'/admin/store-details'}>
          <BackIcon width={30} height={30} color="black" className="my-icon-class" />
        </Link>
        <Text fontSize={'lg'} fontWeight={'bold'}>
          {t('storeDetailsEdit.header.title')}
        </Text>
      </Flex>

      <Flex flexDirection={'column'} bg={'white'} gap={4} p={5} borderRadius={'1rem'}>
        <Flex justifyContent={{ base: 'center', sm: 'space-between' }} pt={'2rem'} pb={'1rem'}>
          <StoreBanner />
        </Flex>

        <Flex px={'1rem'} flexDirection={'column'}>
          <ImageInput
            src={['/imgs/icons/Placeholder.svg']}
            label={t('storeDetailsEdit.fields.storePhoto')}
          />

          <UnderlinedInputField
            variant={'prefixed'}
            label={t('storeDetailsEdit.fields.storeId')}
            required
            w="50%"
            name={'storeOverview'}
            placeholder={t('storeDetailsEdit.placeholders.storeId')}
            value={''}
            onChange={() => {}}
          />

          <UnderlinedInputField
            label={t('storeDetailsEdit.fields.storeName')}
            name={'storeOverview'}
            placeholder={t('storeDetailsEdit.placeholders.storeName')}
            value={''}
            onChange={() => {}}
          />

          <UnderlinedInputField
            label={t('storeDetailsEdit.fields.storeAddress')}
            name={'storeOverview'}
            placeholder={t('storeDetailsEdit.placeholders.storeAddress')}
            value={''}
            onChange={() => {}}
          />

          <UnderlinedInputField
            label={t('storeDetailsEdit.fields.contact')}
            name={'storeOverview'}
            placeholder={t('storeDetailsEdit.placeholders.contact')}
            value={''}
            onChange={() => {}}
          />

          <TextPair
            header={t('storeDetailsEdit.fields.businessHours')}
            subtext={[
              t('storeDetails.businessHours.monday'),
              t('storeDetails.businessHours.tuesday'),
              t('storeDetails.businessHours.wednesday'),
              t('storeDetails.businessHours.thursday'),
              t('storeDetails.businessHours.friday'),
              t('storeDetails.businessHours.saturday'),
              t('storeDetails.businessHours.sunday'),
            ]}
          />

          <UnderlinedInputField
            label={t('storeDetailsEdit.fields.website')}
            name={'storeOverview'}
            placeholder={t('storeDetailsEdit.placeholders.website')}
            value={''}
            onChange={() => {}}
          />

          <UnderlinedInputField
            variant="textArea"
            label={t('storeDetailsEdit.fields.notes')}
            name={'storeOverview'}
            placeholder={t('storeDetailsEdit.placeholders.notes')}
            value={''}
            onChange={() => {}}
          />

          <Flex justifyContent={'center'}>
            <Button variant={'roundedBlue'} px={'3rem'} w={'auto'} onClick={onOpen}>
              {t('storeDetailsEdit.buttons.complete')}
            </Button>
          </Flex>

          <ConfirmationModal
            isOpen={isOpen}
            onClose={onClose}
            modalStep={modalStep}
            setModalStep={setModalStep}
            handleClose={handleClose}
            handleSubmit={handleSubmit}
          />
        </Flex>
      </Flex>
    </Flex>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'admin'])),
      layoutType: 'withSidebar',
    },
  };
}

export async function getStaticPaths() {
  return {
    paths: [],
    fallback: 'blocking',
  };
}
