import React, { useEffect, useState } from 'react';
import {
  Flex,
  Text,
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Box,
  HStack,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  <PERSON>dal<PERSON>ooter,
  ModalCloseButton,
  useDisclosure,
  Select,
  SelectField,
  Input,
  Link,
  UnorderedList,
} from '@chakra-ui/react';
import { ChevronLeftIcon, ChevronRightIcon } from '@chakra-ui/icons';
import CircleIcon from '@/assets/icons/circle';
import { constants } from '@/constants/constants';
import CheckCircleIcon from '@/assets/icons/check-circle';
import XIcon from '@/assets/icons/xIcon';
import TriangleIcon from '@/assets/icons/triangle-icon';

function CardTimeDateSelect() {
  const [hours, setHours] = React.useState(constants.WORKING_HOURS_HALVES);
  const [days, setDays] = React.useState(constants.DAY_NAMES_SUN_START);
  const [dates, setDates] = React.useState<string[]>([]);
  const [selectedCells, setSelectedCells] = React.useState<{ [key: string]: boolean }>({});
  const [currentMonth, setCurrentMonth] = React.useState(new Date());
  const [selectedTime, setSelectedTime] = React.useState<{
    day: string;
    date: string;
    hour: string;
    fullDate: Date;
    key: string;
  } | null>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [numberOfPeople, setNumberOfPeople] = React.useState<number | string>(1);
  const MAX_PEOPLE = constants.MAX_PEOPLE;

  // const steps = [
  //     { title: '利用', description: '' },
  //     { title: '予約変更完了', description: '' },
  // ];

  const [activeCalendar, setActiveCalendar] = useState('week');

  const [activeStep, setActiveStep] = useState(0);

  const incrementHour = (hourString: string, increment: number) => {
    const [hourStr] = hourString.split(':');
    let hour = parseInt(hourStr, 10);
    hour = (hour + increment) % 24;
    return `${hour.toString().padStart(2, '0')}:00`;
  };

  useEffect(() => {
    const newDates: string[] = [];
    const dayNames = constants.DAY_NAMES;
    const newDayNames: string[] = [];

    const now = new Date(currentMonth);

    const dayOfWeek = now.getDay();
    const diff = now.getDate() - dayOfWeek;
    const startOfWeek = new Date(now);
    startOfWeek.setDate(diff);

    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);

      const month = date.getMonth() + 1;
      const day = date.getDate();
      newDates.push(`${month}/${day}`);

      newDayNames.push(dayNames[date.getDay()]);
    }

    setDates(newDates);
    setDays(newDayNames);
  }, [currentMonth]);

  const toggleCell = (day: string, hour: string, date: string, index: number) => {
    const key = `${day}-${hour}`;

    const now = new Date(currentMonth);
    const dayOfWeek = now.getDay();
    const diff = now.getDate() - dayOfWeek;
    const startOfWeek = new Date(now);
    startOfWeek.setDate(diff);

    const selectedDate = new Date(startOfWeek);
    selectedDate.setDate(startOfWeek.getDate() + index);

    const [hourStr, minuteStr] = hour.split(':');
    selectedDate.setHours(parseInt(hourStr, 10), parseInt(minuteStr || '0', 10));

    setSelectedTime({
      day,
      date,
      hour,
      fullDate: selectedDate,
      key,
    });

    onOpen();
  };

  const prevWeek = () => {
    const newDate = new Date(currentMonth);
    newDate.setDate(newDate.getDate() - 7);
    setCurrentMonth(newDate);
  };

  const nextWeek = () => {
    const newDate = new Date(currentMonth);
    newDate.setDate(newDate.getDate() + 7);
    setCurrentMonth(newDate);
  };

  const formatMonth = (date: Date) => {
    return `${date.getFullYear()}年${date.getMonth() + 1}月`;
  };

  const formatFullDate = (date: Date) => {
    return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
  };

  const confirmReservation = () => {
    if (selectedTime) {
      setSelectedCells(prev => ({
        ...prev,
        [selectedTime.key]: !prev[selectedTime.key],
      }));
    }
    setActiveStep(0);
    onClose();
  };

  const generateMonthCalendar = () => {
    const today = new Date();
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();

    const firstDay = new Date(year, month, 1);

    const lastDay = new Date(year, month + 1, 0);

    const firstDayOfWeek = firstDay.getDay();

    const daysFromPrevMonth = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1;

    const lastDayOfPrevMonth = new Date(year, month, 0).getDate();

    const calendar = [];
    let week = [];

    for (let i = daysFromPrevMonth; i > 0; i--) {
      week.push({
        date: lastDayOfPrevMonth - i + 1,
        isCurrentMonth: false,
        isToday: false,
        isAvailable: true,
        status: 'unavailable',
      });
    }

    for (let i = 1; i <= lastDay.getDate(); i++) {
      const isToday =
        today.getDate() === i && today.getMonth() === month && today.getFullYear() === year;

      let status = 'available';
      if (i === 17) status = 'unavailable';
      if (i === 16) status = 'selected';

      week.push({
        date: i,
        isCurrentMonth: true,
        isToday,
        isAvailable: true,
        status,
      });

      if (week.length === 7) {
        calendar.push(week);
        week = [];
      }
    }

    if (week.length > 0) {
      const daysToAdd = 7 - week.length;
      for (let i = 1; i <= daysToAdd; i++) {
        week.push({
          date: i,
          isCurrentMonth: false,
          isToday: false,
          isAvailable: true,
          status: 'unavailable',
        });
      }
      calendar.push(week);
    }

    return calendar;
  };

  const formatDayOfWeek = (date: Date) => {
    const days = ['日', '月', '火', '水', '木', '金', '土'];
    return days[date.getDay()];
  };

  const formatDate = (date: Date) => {
    return `${date.getMonth() + 1}/${date.getDate()}`;
  };

  return (
    <>
      <Flex
        bg={'white'}
        flexDirection={'column'}
        gap={'1rem'}
        p={{ base: '1rem', md: '2rem' }}
        borderRadius={'1rem'}
      >
        <Text variant={'subHeaderMedium'} fontWeight={'bold'}>
          予約日時の選択
        </Text>
        <Flex justifyContent={'space-between'} alignItems={'center'} mb={4}>
          <Button variant="ghost" onClick={prevWeek} leftIcon={<ChevronLeftIcon />}>
            前週
          </Button>
          <Text fontWeight={'bold'}>{formatMonth(currentMonth)}</Text>
          <Button variant="ghost" onClick={nextWeek} rightIcon={<ChevronRightIcon />}>
            翌週
          </Button>
        </Flex>
        <Flex justifyContent={'flex-end'} alignItems={'center'} mb={4}>
          <Flex
            border={'1px solid'}
            p={'.3rem'}
            borderColor={'gray.200'}
            borderRadius={'5px'}
            gap={2}
          >
            <Button
              px={'1rem'}
              h={'auto'}
              py={'.4rem'}
              variant={'ghost'}
              fontWeight={'bold'}
              color={activeCalendar === 'day' ? 'white' : 'mainColor'}
              bg={activeCalendar === 'day' ? 'mainColor' : 'transparent'}
              onClick={() => setActiveCalendar('day')}
            >
              日
            </Button>
            <Button
              px={'1rem'}
              h={'auto'}
              py={'.4rem'}
              variant={'ghost'}
              fontWeight={'bold'}
              color={activeCalendar === 'week' ? 'white' : 'mainColor'}
              bg={activeCalendar === 'week' ? 'mainColor' : 'transparent'}
              onClick={() => setActiveCalendar('week')}
            >
              週
            </Button>
            <Button
              px={'1rem'}
              h={'auto'}
              py={'.4rem'}
              variant={'ghost'}
              fontWeight={'bold'}
              color={activeCalendar === 'month' ? 'white' : 'mainColor'}
              bg={activeCalendar === 'month' ? 'mainColor' : 'transparent'}
              onClick={() => setActiveCalendar('month')}
            >
              月
            </Button>
          </Flex>
        </Flex>

        {activeCalendar === 'day' && (
          <Box>
            <Flex justifyContent="center" mb={4}>
              <Button
                variant="ghost"
                onClick={() => {
                  const newDate = new Date(currentMonth);
                  newDate.setDate(newDate.getDate() - 1);
                  setCurrentMonth(newDate);
                }}
                leftIcon={<ChevronLeftIcon />}
              >
                前日
              </Button>
              <Text fontWeight="bold" mx={4}>
                {formatFullDate(currentMonth)}
              </Text>
              <Button
                variant="ghost"
                onClick={() => {
                  const newDate = new Date(currentMonth);
                  newDate.setDate(newDate.getDate() + 1);
                  setCurrentMonth(newDate);
                }}
                rightIcon={<ChevronRightIcon />}
              >
                翌日
              </Button>
            </Flex>

            <Table variant="simple" size="sm" borderWidth="1px" borderColor="gray.200">
              <Thead>
                <Tr>
                  <Th width="100px" borderWidth="1px" borderColor="gray.200">
                    時間
                  </Th>
                  <Th textAlign="center" borderWidth="1px" borderColor="gray.200">
                    予約状況
                  </Th>
                </Tr>
              </Thead>
              <Tbody>
                {constants.WORKING_HOURS_HALVES.map((hour, index) => {
                  const isAvailable = index % 3 !== 0;
                  const isSelected = selectedCells[`${formatDayOfWeek(currentMonth)}-${hour}`];

                  return (
                    <Tr key={`day-hour-${index}`}>
                      <Td fontWeight="normal" borderWidth="1px" borderColor="gray.200">
                        {hour}
                      </Td>
                      <Td textAlign="center" borderWidth="1px" borderColor="gray.200">
                        <Box
                          as="button"
                          display="flex"
                          justifyContent="center"
                          alignItems="center"
                          width="100%"
                          py={2}
                          // bg={isSelected ? "gray.50" : isAvailable ? "green.50" : "gray.50"}
                          color={isSelected ? 'gray.300' : isAvailable ? 'green.500' : 'gray.300'}
                          borderRadius="md"
                          onClick={() => {
                            if (isAvailable && !isSelected) {
                              const day = formatDayOfWeek(currentMonth);
                              const date = formatDate(currentMonth);
                              toggleCell(day, hour, date, 0);
                            }
                          }}
                          _hover={isAvailable && !isSelected ? { borderColor: 'green.500' } : {}}
                        >
                          {isSelected ? (
                            <XIcon color="#607D8B" />
                          ) : isAvailable ? (
                            <CircleIcon color="#3D5AFE" />
                          ) : (
                            <TriangleIcon color="#607D8B" />
                          )}
                          {/* {isSelected ? "予約済み" : isAvailable ? "予約可能" : "予約不可"} */}
                        </Box>
                      </Td>
                    </Tr>
                  );
                })}
              </Tbody>
            </Table>
          </Box>
        )}

        {activeCalendar === 'week' && (
          <Table
            variant="simple"
            size="sm"
            cellSpacing="0"
            borderWidth="1px"
            borderColor="gray.200"
          >
            <Thead>
              <Tr>
                <Th p={1} borderWidth="1px" borderColor="gray.200"></Th>
                {days.map((day, index) => (
                  <Th
                    fontWeight={'normal'}
                    key={`day-${index}`}
                    textAlign="center"
                    p={1}
                    borderWidth="1px"
                    borderColor="gray.200"
                  >
                    <Text>{day}</Text>
                    <Text>{dates[index]}</Text>
                  </Th>
                ))}
              </Tr>
            </Thead>
            <Tbody>
              {hours.map((hour, hourIndex) => (
                <Tr key={`hour-${hourIndex}`}>
                  <Td fontWeight="medium" p={1} borderWidth="1px" borderColor="gray.200">
                    <Text fontWeight={'normal'}>{hour}</Text>
                  </Td>
                  {days.map((day, dayIndex) => {
                    const key = `${day}-${hour}`;
                    const isSelected = selectedCells[key];

                    return (
                      <Td
                        key={`${day}-${hour}`}
                        p={'2px'}
                        textAlign="center"
                        borderWidth="1px"
                        borderColor="gray.200"
                      >
                        <Box
                          display={'flex'}
                          justifyContent="center"
                          alignItems="center"
                          as="button"
                          w="35px"
                          h="35px"
                          // bg={isSelected ? "gray.50" : "green.50" }
                          color={isSelected ? 'gray.300' : 'green.500'}
                          fontWeight="1000"
                          onClick={() =>
                            isSelected ? null : toggleCell(day, hour, dates[dayIndex], dayIndex)
                          }
                          _hover={{ borderColor: 'green.500' }}
                        >
                          {isSelected ? <XIcon color="#607D8B" /> : <CircleIcon color="#3D5AFE" />}
                        </Box>
                      </Td>
                    );
                  })}
                </Tr>
              ))}
            </Tbody>
          </Table>
        )}

        {activeCalendar === 'month' && (
          <Box>
            <Table
              variant="simple"
              size="sm"
              cellSpacing="0"
              borderWidth="1px"
              borderColor="gray.200"
            >
              <Thead>
                <Tr>
                  {constants.DAY_NAMES_SUN_START.map((day, index) => (
                    <Th
                      fontWeight={'normal'}
                      key={`month-day-${index}`}
                      textAlign="center"
                      p={1}
                      borderWidth="1px"
                      borderColor="gray.200"
                    >
                      <Text>{day}</Text>
                    </Th>
                  ))}
                </Tr>
              </Thead>
              <Tbody>
                {generateMonthCalendar().map((week, weekIndex) => (
                  <Tr key={`week-${weekIndex}`}>
                    {week.map((day, dayIndex) => (
                      <Td
                        border={'1px solid'}
                        borderColor={'gray.200'}
                        key={`month-day-${weekIndex}-${dayIndex}`}
                        p={'2px'}
                        textAlign="center"
                        height="60px"
                        verticalAlign="top"
                      >
                        <Text
                          color={day.isCurrentMonth ? 'black' : 'gray.400'}
                          fontWeight={day.isToday ? 'bold' : 'normal'}
                        >
                          {day.date}
                        </Text>
                        {day.isAvailable && (
                          <Box
                            display="flex"
                            justifyContent="center"
                            alignItems="center"
                            w={'100%'}
                            mt={1}
                            as="button"
                            onClick={() => {
                              if (day.isCurrentMonth && day.status !== 'unavailable') {
                                const selectedDate = new Date(
                                  currentMonth.getFullYear(),
                                  currentMonth.getMonth(),
                                  day.date
                                );
                                const dayName = formatDayOfWeek(selectedDate);
                                const dateStr = formatDate(selectedDate);
                                toggleCell(dayName, '10:00', dateStr, 0); // Default to 10:00 for month view
                              }
                            }}
                          >
                            {day.status === 'available' && (
                              <Box color="blue.500">
                                <CircleIcon color="#3D5AFE" />
                              </Box>
                            )}
                            {day.status === 'unavailable' && (
                              <Box color="gray.400">
                                <TriangleIcon color="#607D8B" />
                              </Box>
                            )}
                            {day.status === 'selected' && (
                              <Box color="blue.600" fontWeight="bold">
                                <XIcon color="#607D8B" />
                              </Box>
                            )}
                          </Box>
                        )}
                      </Td>
                    ))}
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </Box>
        )}
      </Flex>

      <Modal isOpen={isOpen} onClose={onClose} isCentered>
        <ModalOverlay />
        <ModalContent borderRadius="1rem" maxW="90%" w="400px">
          {activeStep === 0 && (
            <ModalHeader borderBottom="1px solid" borderColor="gray.200" p={4}>
              {selectedTime && formatFullDate(selectedTime.fullDate)}
            </ModalHeader>
          )}
          <ModalCloseButton onClick={() => setActiveStep(0)} />

          {activeStep === 0 && (
            <ModalBody p={6}>
              {selectedTime && (
                <Flex direction="column" gap={4}>
                  <Text fontWeight="bold" fontSize="md"></Text>
                  <Flex alignItems={'center'} gap={2}>
                    <SelectField
                      fontSize={'sm'}
                      borderRadius={'10px'}
                      border={'1px solid #E5EAF1'}
                      w={'auto'}
                      px={3}
                      py={3}
                    >
                      <option value="option1">{selectedTime.hour}</option>
                    </SelectField>
                    <Text>:</Text>
                    <SelectField
                      fontSize={'sm'}
                      borderRadius={'10px'}
                      border={'1px solid #E5EAF1'}
                      w={'auto'}
                      px={3}
                      py={3}
                    >
                      <option value="option2">{incrementHour(selectedTime.hour, 1)}</option>
                    </SelectField>
                  </Flex>
                  <Text fontSize={'lg'} fontWeight={'bold'}>
                    Meeting room ①
                  </Text>

                  <Flex alignItems={'center'} gap={'1.5rem'}>
                    <UnorderedList pl={'.5rem'} fontSize={'sm'}>
                      <li>7日前から予約可</li>
                      <li>1日1回（60分）まで</li>
                      <li>当日分に限り追加で予約可</li>
                    </UnorderedList>
                  </Flex>
                </Flex>
              )}
            </ModalBody>
          )}

          <ModalFooter p={4}>
            <Flex w="100%" alignItems={'center'} gap={4}>
              <Button variant="rounded" flex={1} onClick={onClose}>
                戻る
              </Button>
              <Button variant="roundedBlue" flex={1} onClick={() => confirmReservation()}>
                変更する
              </Button>
            </Flex>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
}

export default CardTimeDateSelect;
