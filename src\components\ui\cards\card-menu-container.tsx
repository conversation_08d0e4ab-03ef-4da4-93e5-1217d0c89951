import { Button, Flex, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import CardMenu from './card-menu';

interface BusinessHour {
  day: string;
  hours?: string;
  isClosed?: boolean;
}

interface CardMenuContainerProps {
  variant: string | undefined;
  menuItems?: Array<{
    coursePrice: string;
    courseImage: string;
  }>;
  businessHours?: BusinessHour[];
  websiteUrl?: string;
  address?: string;
}

function CardMenuContainer({
  variant,
  menuItems = [
    { coursePrice: '12,000', courseImage: '/imgs/icons/Placeholder.svg' },
    { coursePrice: '15,000', courseImage: '/imgs/icons/Placeholder.svg' },
    { coursePrice: '18,000', courseImage: '/imgs/icons/Placeholder.svg' },
    { coursePrice: '20,000', courseImage: '/imgs/icons/Placeholder.svg' },
  ],
  businessHours = [
    { day: 'monday', hours: '09:00 - 18:00' },
    { day: 'tuesday', hours: '09:00 - 18:00' },
    { day: 'wednesday', hours: '09:00 - 20:00' },
    { day: 'thursday', isClosed: true },
    { day: 'friday', hours: '09:00 - 20:00' },
    { day: 'saturday', hours: '09:00 - 20:00' },
    { day: 'sunday', hours: '09:00 - 18:00' },
  ],
  websiteUrl = 'sharespace.jp',
  address = '東京都 千代田区 丸の内 1-10-5 プロトビルディング',
}: CardMenuContainerProps) {
  const { t } = useTranslation('common');

  return variant === 'menu' ? (
    <Flex
      className="CardMenuContainer"
      borderRadius={'15px'}
      overflow={'hidden'}
      flexDirection={'column'}
      gap={'2px'}
      bg={'transparent'}
    >
      {menuItems.map((item, index) => (
        <CardMenu key={index} coursePrice={item.coursePrice} courseImage={item.courseImage} />
      ))}

      <Flex height={'5rem'} bg={'white'} justifyContent={'center'} alignItems={'center'}>
        <Button
          w={'100%'}
          variant={'ghost'}
          fontSize={'sm'}
          fontWeight={'bold'}
          color={'mainColor'}
        >
          {t('reservations.viewAllReservationMenus')}
        </Button>
      </Flex>
    </Flex>
  ) : variant === 'share-space' ? (
    <Flex
      className="CardMenuContainer"
      px={'1.5rem'}
      bg={'white'}
      borderRadius={'15px'}
      overflow={'hidden'}
      flexDirection={'column'}
    >
      <Flex flexDirection={'column'} gap={'2px'} bg={'mainBackGroundColor'}>
        <Flex flexDirection={'column'} py={'1.5rem'} bg={'white'} gap={'.5rem'}>
          <Text fontSize={'sm'} fontWeight={'bold'}>
            {t('store.shareSpaceInfo')}
          </Text>
          <Text fontSize={'sm'} fontWeight={'bold'}>
            {t('store.address')}
          </Text>
          <Text fontSize={'xs'}>{address}</Text>
        </Flex>

        <Flex flexDirection={'column'} py={'1.5rem'} bg={'white'} gap={'.5rem'}>
          <Text fontSize={'sm'} fontWeight={'bold'}>
            {t('store.businessHours')}
          </Text>

          <Flex flexDirection={'column'}>
            {businessHours.map((hour, index) => (
              <Text fontSize={'xs'} key={index}>
                {t(`calendar.${hour.day}`)}
                {hour.isClosed ? `　${t('calendar.closedDay')}` : `　${hour.hours}`}
              </Text>
            ))}
          </Flex>
        </Flex>

        <Flex flexDirection={'column'} py={'1.5rem'} bg={'white'} gap={'.5rem'}>
          <Text fontSize={'sm'} fontWeight={'bold'}>
            {t('store.officialWebsite')}
          </Text>
          <Text fontSize={'md'}>{websiteUrl}</Text>
        </Flex>

        <Flex flexDirection={'column'} py={'1.5rem'} bg={'white'} gap={'.5rem'}></Flex>
      </Flex>
    </Flex>
  ) : null;
}

export default CardMenuContainer;
