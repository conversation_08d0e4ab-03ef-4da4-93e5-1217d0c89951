
type MyPageData = {
  clientData: {
    fullName: string;
    kana: string;
    phoneNumber: string;
    address: string;
    email: string;
  };
};



export const isFormValid = ({data, controlData } : {data: MyPageData, controlData: React.MutableRefObject<MyPageData> }) => {
    return (
      data.clientData.fullName.trim() !== controlData.current.clientData.fullName ||
      data.clientData.kana.trim() !== controlData.current.clientData.kana ||
      data.clientData.phoneNumber.trim() !== controlData.current.clientData.phoneNumber ||
      data.clientData.email.trim() !== controlData.current.clientData.email
    );
  };