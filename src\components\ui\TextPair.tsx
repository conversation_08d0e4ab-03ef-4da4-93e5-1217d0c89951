import React, { ReactNode } from 'react';
import { Flex, Text, TextProps } from '@chakra-ui/react'

interface TextPairProps extends Omit<TextProps, 'children'>{
    variant?: 'card-element' | string;
    header: string | string[];
    subtext: string[];
    children?: ReactNode
}

export default function TextPair({
    variant,
    header,
    subtext,
    ...rest
}: TextPairProps) {
  return (
    variant === 'card-element' ? (
        <Flex
          pb={'1rem'}
          gap={'.5rem'}
          w={'100%'}
          justifyContent={'space-between'}
          borderBottom={'1px solid'}
          borderColor={'borderGray'}
        >
          <Flex flexDirection={'column'} gap={'.5rem'}>
            <Text textAlign={'left'} fontWeight={'bold'} variant={'subHeaderMedium'}>
              {header}
            </Text>

            {subtext?.map((text, index) => (
                <Text key={index}>{text}</Text>
            ))}

            
          </Flex>

          <Flex justifyContent={'space-between'}>
            <Flex alignItems={'flex-end'} gap={'.5rem'}></Flex>
          </Flex>
        </Flex>
    ): (
        <Flex
            flexDirection={'column'}
            gap={2}
            borderBottom={'1px solid #E5EAF1'}
            pb={'1rem'}
            mb={'1rem'}
            {...rest}
            >
              
            {Array.isArray(header) ?
            <>
              <Text 
              fontSize={'md'} 
              fontWeight={'semibold'} 
              color={'mainColor'}>
                  {header[0]}
              </Text>
              
              <Text 
              fontSize={'sm'} 
              fontWeight={'bold'}>
                  {header[1]}
              </Text>
            </>
            : 
            <Text 
            fontSize={'sm'} 
            fontWeight={'bold'}>
              {header}
            </Text>}

            {subtext?.map((text, index) => (
                <Text 
                fontSize={'sm'} 
                color={'black600'} 
                key={index} 
                lineHeight={'.8'}
                {...rest}
                >
                    {text}
                </Text>
            ))}
        </Flex>)
  )
}
