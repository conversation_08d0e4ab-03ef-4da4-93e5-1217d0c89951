import { ChevronLeftIcon, ChevronRightIcon } from '@chakra-ui/icons';
import { Box, Button, Flex, HStack, IconButton, Text, useDisclosure } from '@chakra-ui/react';
import moment from 'moment';
import { useState } from 'react';
import { Calendar, momentLocalizer, View, Views } from 'react-big-calendar';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { ReservationEvent } from '@/types/ReservationEventType';
import { sampleReservations } from '@/.temp/sampleCalendarReservations';
import { sampleRooms } from '@/.temp/sampleRooms';
import { getRoomColor } from '@/utils/getRoomColor';
import { getRoomsForDate } from '@/utils/getRoomsForDate';
import { goToNext, goToPrevious } from '@/utils/calendarNav';
import { eventStyleGetter } from '@/utils/eventsStyleGetter';
import { CalendarDayView } from './CalendarDayView';
import { messages } from '@/constants/messages';
import { formatMonthYear } from '@/utils/formatDates';
import { CalendarReservationModal } from '@/components/modals/CalendarReservationModal';

const localizer = momentLocalizer(moment);

export default function ReactBigCalendarComponent() {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [view, setView] = useState<View>(Views.MONTH);
  const [selectedEvent, setSelectedEvent] = useState<ReservationEvent | null>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();

  const handleSelectEvent = (event: ReservationEvent) => {
    setSelectedEvent(event);
    onOpen();
  };

  const handleNavigate = (newDate: Date) => {
    setCurrentDate(newDate);
  };

  const handleViewChange = (newView: View) => {
    setView(newView);
  };

  return (
    <Flex direction="column" h="100%" w="100%">
      <Flex flexDirection="column" align="center" mb={4}>
        {view !== Views.DAY && (
          <Flex w="100%" justifyContent="center">
            <HStack>
              <IconButton
                aria-label="Previous"
                icon={<ChevronLeftIcon height={25} width={25} />}
                onClick={() => goToPrevious({ currentDate, view, Views, setCurrentDate })}
                size="xs"
                borderRadius="50rem"
                bg="mainColor"
                color="white"
              />
              <Text fontWeight="bold" fontSize="xl">
                {formatMonthYear(currentDate)}
              </Text>
              <IconButton
                aria-label="Next"
                icon={<ChevronRightIcon height={25} width={25} />}
                onClick={() => goToNext({ currentDate, view, Views, setCurrentDate })}
                size="xs"
                borderRadius="50rem"
                bg="mainColor"
                color="white"
              />
            </HStack>
          </Flex>
        )}

        <Flex w="100%" alignItems="flex-end" justifyContent="space-between">
          {view === Views.WEEK || view === Views.MONTH ? (
            <HStack alignItems="baseline" gap="1rem" p={1}>
              {sampleRooms.map(room => (
                <Flex key={room.id} alignItems="center">
                  <Flex bg={room.color} w="10px" h="10px" borderRadius="full" mr={1}></Flex>
                  <Text fontWeight="medium" fontSize="sm">
                    {room.name}
                  </Text>
                </Flex>
              ))}
            </HStack>
          ) : (
            <HStack alignItems="baseline" gap="1rem" p={1}>
              {getRoomsForDate({
                date: currentDate,
                reservations: sampleReservations,
              }).map(room => (
                <Flex key={room} alignItems="center">
                  <Flex
                    bg={getRoomColor({ roomName: room, rooms: sampleRooms })}
                    w="10px"
                    h="10px"
                    borderRadius="full"
                    mr={1}
                  />
                  <Text fontWeight="medium" fontSize="sm">
                    {room}
                  </Text>
                </Flex>
              ))}
            </HStack>
          )}

          <HStack border="1px solid" borderColor="mainColor" gap={1} borderRadius="md" p={1}>
            <Button
              size="sm"
              fontWeight="normal"
              variant={view === Views.DAY ? 'blue' : 'ghost'}
              onClick={() => handleViewChange(Views.DAY)}
            >
              日
            </Button>
            <Button
              size="sm"
              fontWeight="normal"
              variant={view === Views.WEEK ? 'blue' : 'ghost'}
              onClick={() => handleViewChange(Views.WEEK)}
            >
              週
            </Button>
            <Button
              size="sm"
              fontWeight="normal"
              variant={view === Views.MONTH ? 'blue' : 'ghost'}
              onClick={() => handleViewChange(Views.MONTH)}
            >
              月
            </Button>
          </HStack>
        </Flex>
      </Flex>

      {view === Views.DAY ? (
        <CalendarDayView
          view={view}
          handleViewChange={handleViewChange}
          handleNavigate={handleNavigate}
          handleSelectEvent={handleSelectEvent}
          messages={messages}
        />
      ) : (
        /* Month and Week Views - Full Width */
        <Box
          border={view === Views.MONTH ? '1px solid' : 'none'}
          borderColor={'gray.200'}
          borderRadius="md"
          overflow="hidden"
          minH="600px"
        >
          {/* @ts-ignore - react-big-calendar type compatibility issue */}
          <Calendar
            localizer={localizer}
            events={sampleReservations}
            startAccessor="start"
            endAccessor="end"
            style={{ height: '100%' }}
            view={view}
            onView={handleViewChange}
            date={currentDate}
            onNavigate={handleNavigate}
            onSelectEvent={handleSelectEvent}
            onSelectSlot={e => {
              console.log('e.start', e.start);
              console.log('e.end', e.end);
              console.log('e', e);
              console.log('slot selected');
            }}
            eventPropGetter={event => eventStyleGetter({ event, rooms: sampleRooms })}
            messages={messages}
            views={[Views.MONTH, Views.WEEK, Views.DAY]}
            step={30}
            timeslots={2}
            showMultiDayTimes
            popup
            toolbar={false}
          />
        </Box>
      )}

      {/* Event Details Modal - matching original design */}
      <CalendarReservationModal isOpen={isOpen} onClose={onClose} selectedEvent={selectedEvent} />
    </Flex>
  );
}
