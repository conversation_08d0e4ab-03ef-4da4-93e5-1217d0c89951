import Calendar from '@/components/ui/calendar/calendar';
import { Button, Flex, Text, VStack } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';

function CardCalendar() {
  const { t } = useTranslation('common');
  // const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  const availableDates = [
    new Date(2023, 5, 10),
    new Date(2023, 5, 15),
    new Date(2023, 5, 20),
    new Date(2023, 5, 25),
  ];

  const minDate = new Date();

  const maxDate = new Date();
  maxDate.setMonth(maxDate.getMonth() + 3);

  const handleDateSelect = (date: Date) => {
    // setSelectedDate(date);
    console.log(`Selected date: ${date.toLocaleDateString()}`);
  };

  return (
    <Flex flexDirection={'column'} gap={'1rem'}>
      <Flex
        flexDir={'column'}
        bg="white"
        p={{ base: '1rem', md: '2rem' }}
        gap={'1rem'}
        borderRadius="1rem"
      >
        <Flex justifyContent={'space-between'} alignItems={'center'}>
          <Text fontSize={'sm'} fontWeight={'bold'}>
            {t('calendar.dateSelection')}
          </Text>
          <Flex
            border={'1px solid'}
            p={'.2rem'}
            borderColor={'borderGray'}
            borderRadius={'5px'}
            gap={2}
          >
            <Button px={'1.5rem'} variant={'ghost'} fontWeight={'bold'} color={'mainColor'}>
              {t('navigation.weekView')}
            </Button>
            <Button
              px={'1.5rem'}
              variant={'ghost'}
              fontWeight={'bold'}
              color={'mainColor'}
              bg={'colorBox'}
            >
              {t('navigation.monthView')}
            </Button>
          </Flex>
        </Flex>
        <VStack spacing={4} align="stretch">
          <Calendar
            onDateSelect={handleDateSelect}
            highlightedDates={availableDates}
            minDate={minDate}
            maxDate={maxDate}
          />
        </VStack>
      </Flex>
    </Flex>
  );
}

export default CardCalendar;
