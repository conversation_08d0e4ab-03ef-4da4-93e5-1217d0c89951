import LanguageSwitcher from '@/components/ui/language-switcher';
import { Box, Heading, Text, VStack } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

export default function ExamplePage() {
  const { t } = useTranslation('common');

  return (
    <Box p={8} maxW="800px" mx="auto">
      <VStack spacing={6} align="center">
        <Heading as="h1" size="xl">
          {t('store.shareSpaceInfo')}
        </Heading>

        <LanguageSwitcher />

        <Text fontSize="lg">{t('courses.courseDescription')}</Text>

        <Box p={4} borderWidth={1} borderRadius="md" width="100%">
          <Heading as="h2" size="md" mb={4}>
            {t('store.businessHours')}
          </Heading>
          <Text>{t('calendar.monday')} 09:00 - 18:00</Text>
          <Text>{t('calendar.tuesday')} 09:00 - 18:00</Text>
          <Text>{t('calendar.wednesday')} 09:00 - 20:00</Text>
          <Text>
            {t('calendar.thursday')} {t('calendar.closedDay')}
          </Text>
          <Text>{t('calendar.friday')} 09:00 - 20:00</Text>
          <Text>{t('calendar.saturday')} 09:00 - 20:00</Text>
          <Text>{t('calendar.sunday')} 09:00 - 18:00</Text>
        </Box>
      </VStack>
    </Box>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  };
}
