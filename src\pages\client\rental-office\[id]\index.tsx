import ConfirmReservationDetails from '@/components/modals/confirm-reservation-details';
import CardContainer from '@/components/ui/cards/card-container';
import CardImageContainer from '@/components/ui/cards/card-image-container';
import CardMenuContainer from '@/components/ui/cards/card-menu-container';
import CardText from '@/components/ui/cards/card-text';
import CardTtimeDateSelect from '@/components/ui/cards/card-time-date-select';
import { Button, Flex, Text, UnorderedList } from '@chakra-ui/react';
import React from 'react';

function RentalOffice() {
  const [isOpen, setIsOpen] = React.useState(false);

  return (
    <Flex flexDirection={'column'} gap={'1rem'}>
      <CardImageContainer src={'/office placeholder.png'} />
      <CardText text="Share Space（シェアスペース）**は、ビジネスに最適な柔軟性と快適さを兼ね備えたシェアオフィスです。スタートアップ企業、フリーランス、リモートワーカー、中小企業まで、さまざまな働き方に対応できるプロフェッショナルな環境を提供しています。" />

      <CardContainer>
        <Flex gap={'3rem'}>
          <Flex>
            <Button
              fontWeight={'normal'}
              variant={'RoundedOutline'}
              fontSize={'md'}
              borderColor={'black'}
            >
              戻る
            </Button>
          </Flex>
          <Flex gap={'.5rem'} flexDirection={'column'}>
            <Text fontSize={'lg'} fontWeight={'bold'}>
              Meeting room ①
            </Text>
            <UnorderedList pl={'.5rem'} fontSize={'sm'}>
              <li>7日前から予約可</li>
              <li>1日1回（60分）まで</li>
              <li>当日分に限り追加で予約可</li>
            </UnorderedList>
          </Flex>
        </Flex>
      </CardContainer>

      <CardTtimeDateSelect />

      {/* <Calendar /> */}
      <CardMenuContainer variant={'share-space'} />

      <ConfirmReservationDetails isOpen={isOpen} onClose={() => setIsOpen(false)} />

      <Flex py={'1rem'}>
        <Button
          fontWeight={'normal'}
          variant={'roundedBlue'}
          fontSize={'md'}
          borderColor={'black'}
          onClick={() => setIsOpen(true)}
        >
          予約内容の確認
        </Button>
      </Flex>
    </Flex>
  );
}

RentalOffice.getInitialProps = async () => {
  return { layoutType: 'client' };
};

// export async function getStaticProps({ locale }: { locale: string }) {
//   return {
//     props: {
//       ...(await serverSideTranslations(locale, ["common", "login"])),
//       layoutType: "client",
//     },
//   };
// }

export default RentalOffice;
