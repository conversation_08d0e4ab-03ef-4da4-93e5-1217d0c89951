import LanguageSwitcher from '@/components/ui/language-switcher';
import UserInfo<PERSON><PERSON><PERSON>ield from '@/pageContainer/admin/enter-user-information/UserInfoFormField';
import { Flex } from '@chakra-ui/react';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

function Index() {
  return (
    <>
      <Flex
        justifyContent="flex-end"
        width="100%"
        position="absolute"
        top={4}
        right={4}
        zIndex={10}
      >
        <LanguageSwitcher />
      </Flex>
      <UserInfoFormField />
    </>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'admin'])),
    },
  };
}

export default Index;
