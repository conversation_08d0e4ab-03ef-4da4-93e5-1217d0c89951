import React from 'react'
import { Flex, Grid, Text } from '@chakra-ui/react'
import { ReservationHistoryType } from '@/types/reservationHistoryType'

export default function ReservationsHistoryTable({
  title, 
  data }:{
    title : string
    data : ReservationHistoryType[]
  }) {
  return (
    <Flex
      pb={'1rem'}
      flexDirection={'column'}
      gap={'.5rem'}
      w={'100%'}
      justifyContent={'space-between'}
    >
      <Flex flexDirection={'column'} gap={'.5rem'}>
        <Text textAlign={'left'} fontWeight={'bold'} variant={'subHeaderMedium'}>
          {title}
        </Text>
      </Flex> 

      {data?.map(item =>(
        <Grid
        key={item.id}
        borderBottom={'1px solid'}
        py={'1rem'}
        borderColor={'borderGray'}
        w={'100%'}
        templateColumns="1fr 7rem 3.5rem"
        gap={2}
      >
        <Text fontSize={'sm'}>{item.dateTime}</Text>
        <Text fontSize={'sm'}>{item.optionName}</Text>
        <Text fontSize={'sm'}>¥ {item.cost}</Text>
      </Grid>
      ))}

      <Flex borderBottom={'1px solid'} py={'1rem'} borderColor={'borderGray'}>
        <Text>※施術に関するお問合わせやご要望等</Text>
      </Flex>
    </Flex>
  )
}
