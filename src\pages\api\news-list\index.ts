import type { NextApiRequest, NextApiResponse } from 'next';
import { newsList, NewsList } from './newsList';

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<NewsList[] | NewsList | { message: string }>
) {
  // Get all news items
  if (req.method === 'GET') {
    // Check if an ID is provided in the query parameters
    const { id } = req.query;

    if (id) {
      // Convert the ID to a number
      const newsId = parseInt(id as string, 10);

      // Find the news item with the matching ID
      const newsItem = newsList.find(item => item.id === newsId);

      if (!newsItem) {
        return res.status(404).json({ message: `News item with ID ${id} not found` });
      }

      return res.status(200).json(newsItem);
    }

    // If no ID is provided, return all news items
    return res.status(200).json(newsList);
  } else if (req.method === 'POST') {
    const { id, category, title, date, image, details } = req.body;

    if (!id || typeof id !== 'number') {
      return res.status(400).json({ message: 'User ID is required and must be a number' });
    }

    const newsListIndex = newsList.findIndex(newsList => newsList.id === id);

    if (newsListIndex === -1) {
      return res.status(404).json({ message: `NewsList with ID ${id} not found` });
    }

    newsList[newsListIndex] = {
      ...newsList[newsListIndex],
      id,
      category: category || newsList[newsListIndex].category,
      title: title || newsList[newsListIndex].title,
      date: date || newsList[newsListIndex].date,
      image: image || newsList[newsListIndex].image,
      details: details || newsList[newsListIndex].details,
    };

    return res.status(200).json(newsList);
  } else {
    return res.status(405).json({ message: 'Method not allowed' });
  }
}
