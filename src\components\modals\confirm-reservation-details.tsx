import React from 'react';
import {
  Flex,
  Text,
  But<PERSON>,
  <PERSON>,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Box,
  HStack,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  Modal<PERSON>ooter,
  ModalCloseButton,
  useDisclosure,
  Select,
  SelectField,
  Input,
  Link,
} from '@chakra-ui/react';
import { constants } from '@/constants/constants';
import CheckCircleIcon from '@/assets/icons/check-circle';

export default function ConfirmReservationDetails({
  isOpen,
  onClose,
  setActiveStep,
  activeStep,
  confirmReservation,
}: any) {
  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered>
      <ModalOverlay />
      <ModalContent borderRadius="1rem" maxW="90%" w="400px">
        <ModalCloseButton />

        <ModalBody
          display={'flex'}
          flexDirection={'column'}
          justifyContent={'center'}
          alignItems={'center'}
          gap={'1rem'}
          p={'1.5rem'}
          mt={'2rem'}
        >
          <Flex
            justifyContent={'center'}
            alignItems={'center'}
            textAlign={'center'}
            flexDirection={'column'}
            width="250px"
          >
            <Text fontWeight={700} variant={'brand'} mb={4}>
              {constants.COMPANY_NAME}
            </Text>
            <Text fontWeight={700} variant={'subHeaderXL'} mb={4}>
              予約変更完了
            </Text>
            <CheckCircleIcon />
          </Flex>

          <Flex fontSize={'sm'} gap={'1rem'}>
            <Text variant={'category'}>リクエスト予約</Text>
            <Text letterSpacing={'.5px'} fontWeight={'bold'}>
              000000000000
            </Text>
          </Flex>

          <Flex fontSize={'sm'}>
            <Text>ご予約が完了しました。</Text>
            <Link variant={'underline'}>予約確認はこちら</Link>
          </Flex>

          <Flex w={'100%'} gap={'1rem'} bg={'gray.50'} px={'1rem'} borderRadius={'1rem'}>
            <Flex w={'100%'} flexDirection={'column'} gap={'1px'} bg={'gray.200'}>
              <Flex bg={'gray.50'} py={'1rem'} flexDirection={'column'}>
                <Text fontWeight={'medium'}>注意事項</Text>
                <Text fontWeight={'medium'}>予約に関する注意事項</Text>

                <Flex mt={'.5rem'} fontSize={'sm'} flexDirection={'column'}>
                  <Text fontSize={'sm'}>予約確認のためご連絡することがございます。</Text>
                  <Text fontSize={'sm'}>繋がりやすい連絡先を登録ください。</Text>
                </Flex>
              </Flex>
              <Flex bg={'gray.50'} gap={'1rem'} py={'1rem'} flexDirection={'column'}>
                <Text fontWeight={'medium'}>キャンセルポリシー</Text>
                <Text fontSize={'sm'}>
                  キャンセル料なしキャンセルはご予約前日までにご対応ください。当日キャンセルも可能ではございますが、何度も当日キャンセルをする方は予約の制限をかけさせていただく場合がございます。予めご注意ください。
                </Text>
              </Flex>
            </Flex>
          </Flex>
        </ModalBody>

        <ModalFooter p={4}>
          <Flex w="100%" alignItems={'center'} gap={4}>
            <Button
              border={'none'}
              bg={'gray.50'}
              variant="rounded"
              flex={1}
              onClick={confirmReservation}
            >
              戻る
            </Button>
          </Flex>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
