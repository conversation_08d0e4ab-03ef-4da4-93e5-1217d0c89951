import { Button, Flex, FormControl, FormLabel, Input, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

function SearchPasswordEmail() {
  const { t } = useTranslation('auth');
  return (
    <form>
      <Flex
        justifyContent={'center'}
        flexDirection={'column'}
        gap={'1rem'}
        bg={'white'}
        p={'1rem'}
        borderRadius={'1rem'}
      >
        <Flex justifyContent={'center'}>
          <Text variant={'brand'}>JUSTYOYAKU</Text>
        </Flex>
        <Flex flexDirection={'column'} gap={'.5rem'} justifyContent={'center'}>
          <Text fontSize={'xl'} textAlign={'center'} variant={'subHeader'} fontWeight={'bold'}>
            {t('searchPasswordEmail.title')}
          </Text>
          <Text fontSize={'sm'} textAlign={'center'}>
            {t('searchPasswordEmail.description')}
          </Text>
        </Flex>
        <FormControl>
          <FormLabel fontWeight={'light'} fontSize={'xs'}>
            {t('searchPasswordEmail.form.emailAddress')}
          </FormLabel>
          <Input
            p={'6'}
            name="storeOverview"
            type="text"
            placeholder={t('searchPasswordEmail.form.emailPlaceholder')}
          />
        </FormControl>
        <Flex justifyContent={'center'} mt={'1rem'}>
          <Button variant={'roundedBlue'} type="submit">
            {t('searchPasswordEmail.buttons.send')}
          </Button>
        </Flex>
      </Flex>
    </form>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'auth'])),
      layoutType: 'client',
    },
  };
}

export default SearchPasswordEmail;
