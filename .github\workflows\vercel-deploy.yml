name: <PERSON><PERSON> and Deploy to Vercel

on:
  push:
    branches: [develop, main]
  pull_request:
    branches: [develop, main]
    types: [opened, synchronize, reopened]

jobs:
  build:
    name: Check Build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 20
          cache: 'yarn'

      - name: Cache Next.js build cache
        uses: actions/cache@v3
        with:
          path: .next/cache
          key: ${{ runner.os }}-nextjs-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-nextjs-

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Build application
        run: yarn build
        env:
          NEXT_TELEMETRY_DISABLED: 1
  deploy:
    name: Deploy to Vercel
    runs-on: ubuntu-latest
    if: github.event_name == 'push'
    needs: build
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 20
          cache: 'yarn'

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Build application
        run: yarn build
        env:
          NEXT_TELEMETRY_DISABLED: 1

      - name: Deploy to Vercel
        env:
          VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
        run: |
          npx vercel pull --yes --environment=production --token $VERCEL_TOKEN
          npx vercel build --prod --token $VERCEL_TOKEN
          npx vercel deploy --prebuilt --prod --token $VERCEL_TOKEN
