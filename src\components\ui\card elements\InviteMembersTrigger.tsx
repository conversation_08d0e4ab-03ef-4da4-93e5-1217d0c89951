import React from 'react'
import { Flex, But<PERSON>, Text } from '@chakra-ui/react'
import PlusIcon from '@/assets/icons/plus-icon'

export default function InviteMembersTrigger( { onMemberInvitationOpen }: { onMemberInvitationOpen: () => void } ) {
  return (
    <Flex
        pb={'1rem'}
        gap={'.5rem'}
        w={'100%'}
        justifyContent={'space-between'}
        borderBottom={'1px solid'}
        borderColor={'borderGray'}
    >
        <Flex alignItems={'center'} gap={'.5rem'}>
        <Text textAlign={'left'} fontWeight={'bold'} variant={'subHeaderMedium'}>
            メンバー招待
        </Text>
        </Flex>

        <Flex justifyContent={'space-between'}>
        <Flex alignItems={'flex-end'} gap={'.5rem'}>
            <Button
            w={'auto'}
            variant={'roundedBlue'}
            px={'.6rem'}
            py={'1.5rem'}
            onClick={() => {
                onMemberInvitationOpen();
            }}
            >
            <PlusIcon height={30} width={30} color="white" />
            </Button>
        </Flex>
        </Flex>
    </Flex>
  )
}
