import PenIcon from '@/assets/icons/pen';
import { constants } from '@/constants/constants';
import { ClientDataType } from '@/types/clientDataType';
import { Button, Flex, Grid, GridItem, Image, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';

function CardMyProfileBanner({
  data,
  variant,
  onOpen,
}: {
  data: ClientDataType;
  variant?: string;
  onOpen: () => void;
}) {
  const { t } = useTranslation('profile');
  return variant === 'group' ? (
    <Flex
      bg={'white'}
      flexDirection={'column'}
      alignItems={'center'}
      p={{ base: '1rem', md: '2rem' }}
      gap={'2rem'}
      borderRadius={'1rem'}
    >
      <Grid templateColumns={'1fr 1fr'} gap={'1rem'}>
        <GridItem overflow={'hidden'} borderRadius={'100%'} height={'5rem'} w={'5rem'}>
          <Image src={'/imgs/icons/profileIcon.svg'} alt="option" />
        </GridItem>

        <GridItem overflow={'hidden'} borderRadius={'100%'} height={'5rem'} w={'5rem'}>
          <Image src={'/imgs/icons/profileIcon.svg'} alt="option" />
        </GridItem>

        <GridItem overflow={'hidden'} borderRadius={'100%'} height={'5rem'} w={'5rem'}>
          <Image src={'/imgs/icons/profileIcon.svg'} alt="option" />
        </GridItem>

        <GridItem overflow={'hidden'} borderRadius={'100%'} height={'5rem'} w={'5rem'}>
          <Image src={'/imgs/icons/profileIcon.svg'} alt="option" />
        </GridItem>
      </Grid>

      <Flex alignItems={'center'} gap={2}>
        <Text variant={'subHeaderXL'}>Just Group</Text>
      </Flex>
    </Flex>
  ) : (
    <Flex
      bg={'white'}
      flexDirection={'column'}
      alignItems={'center'}
      p={{ base: '1rem', md: '2rem' }}
      gap={'2rem'}
      borderRadius={'1rem'}
    >
      <Text variant={'brand'}>{constants.COMPANY_NAME}</Text>

      <Flex
        zIndex={'0'}
        bg={'red.100'}
        position={'relative'}
        overflow={'hidden'}
        borderRadius={'100%'}
        height={'5rem'}
        w={'5rem'}
      >
        <Image src={'/imgs/icons/profileIcon.svg'} alt="option" />
        <Flex
          justifyContent={'center'}
          alignItems={'center'}
          bg={'white'}
          borderRadius={'100%'}
          p={'.4rem'}
          zIndex={'1'}
          right={'.1rem'}
          bottom={'.1rem'}
          position={'absolute'}
          boxShadow={'0px 2px 4px rgba(0, 0, 0, 0.1)'}
        >
          <PenIcon />
        </Flex>
      </Flex>

      <Flex alignItems={'center'} gap={2}>
        <Text variant={'subHeaderXL'}>{data?.fullName}</Text>
        <Text>{t('greeting.hello')}</Text>
      </Flex>

      <Button
        onClick={onOpen}
        w={'100%'}
        borderColor={'black'}
        variant={'rounded'}
        fontSize={'sm'}
        fontWeight={'normal'}
        gap={2}
      >
        <span>{t('navigation.viewUserInfo')}</span>
      </Button>
    </Flex>
  );
}

export default CardMyProfileBanner;
