# Zustand 5.x State Management

This directory contains Zustand stores for managing global state in the application.

## What is Zustand?

Zustand is a small, fast, and scalable state management solution for React. It has a simple API that makes it easy to create and use stores. Version 5.x brings improved TypeScript support and performance optimizations.

## Store Structure

Each store follows a similar pattern:

1. Define the state interface (what data the store holds)
2. Define the actions interface (what functions the store provides)
3. Create the store with initial state and actions
4. Export the store hook

## Available Stores

- `userStore.ts`: Manages user authentication state with persistence
- `reservationStore.ts`: Manages reservation data and operations with immer for easier updates

## How to Use

### Basic Usage

```tsx
import { useUserStore } from '@/store/userStore';

function MyComponent() {
  // Get state and actions from the store
  const { user, isAuthenticated, login, logout } = useUserStore();

  return (
    <div>
      {isAuthenticated ? (
        <>
          <p>Welcome, {user?.name}!</p>
          <button onClick={logout}>Logout</button>
        </>
      ) : (
        <button onClick={() => login('<EMAIL>', 'password')}>Login</button>
      )}
    </div>
  );
}
```

### Using Multiple Stores

You can use multiple stores in a single component:

```tsx
import { useUserStore } from '@/store/userStore';
import { useReservationStore } from '@/store/reservationStore';

function ReservationsPage() {
  const { isAuthenticated } = useUserStore();
  const { reservations, fetchReservations } = useReservationStore();

  // Use both stores together
}
```

### Selecting Specific State (Recommended)

For performance optimization, you can select only the state you need:

```tsx
// Only re-render when isAuthenticated changes
const isAuthenticated = useUserStore(state => state.isAuthenticated);

// Only re-render when user changes
const user = useUserStore(state => state.user);
```

## Creating a New Store

To create a new store:

1. Create a new file in the `store` directory (e.g., `myFeatureStore.ts`)
2. Define the state and actions interfaces
3. Create the store with initial state and actions
4. Export the store hook

Example with Zustand 5.x syntax:

```tsx
import { create } from 'zustand';

// Separate state and actions for better TypeScript inference
interface MyFeatureState {
  data: any[];
  isLoading: boolean;
  error: string | null;
}

interface MyFeatureActions {
  fetchData: () => Promise<void>;
  addItem: (item: any) => void;
}

// Combine state and actions in the store type
export const useMyFeatureStore = create<MyFeatureState & MyFeatureActions>()(set => ({
  // Initial state
  data: [],
  isLoading: false,
  error: null,

  // Actions
  fetchData: async () => {
    try {
      set({ isLoading: true, error: null });
      // API call logic here
      const response = await fetch('/api/my-feature');
      const data = await response.json();
      set({ data, isLoading: false });
    } catch (error) {
      set({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  },

  addItem: item => {
    set(state => ({ data: [...state.data, item] }));
  },
}));
```

## Middleware

Zustand 5.x supports powerful middleware to enhance your stores:

### Persistence

For stores that need to persist data (like user authentication):

```tsx
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export const usePersistedStore = create<MyState & MyActions>()(
  persist(
    set => ({
      // state and actions
    }),
    {
      name: 'my-storage-key', // unique name for localStorage
      partialize: state => ({
        // only persist these fields
        someField: state.someField,
      }),
    }
  )
);
```

### Immer Integration

For easier state updates (especially with nested objects):

```tsx
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

export const useImmerStore = create<MyState & MyActions>()(
  immer(set => ({
    nested: { objects: { are: { easy: true } } },

    updateNested: () => {
      // Direct mutation is allowed with immer!
      set(state => {
        state.nested.objects.are.easy = false;
      });
    },
  }))
);
```

## Best Practices

1. Keep stores focused on specific domains (user, reservations, etc.)
2. Separate state and actions interfaces for better TypeScript inference
3. Use selectors for performance optimization
4. Use immer middleware for complex state updates
5. Use persist middleware for data that needs to survive page refreshes
6. Handle loading and error states consistently
7. Keep related actions in the same store as the state they modify
