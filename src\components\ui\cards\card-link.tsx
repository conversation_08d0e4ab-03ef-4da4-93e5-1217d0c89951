import BellIcon from '@/assets/icons/bell';
import BookIcon from '@/assets/icons/book-icon';
import CalendarIcon from '@/assets/icons/calendar';
import CircleQuestionmarkIcon from '@/assets/icons/circle-questionmark';
import { Button, Flex, Input, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import Router from 'next/router';
import React from 'react';
import { ToggleButton } from '../formfields/toggle-button';

export default function CardLink({
  variant,
  header,
  icon,
  text,
  href,
}: {
  variant?: string;
  header?: string;
  icon?: string;
  text: string;
  href?: string;
}) {
  const { t } = useTranslation('profile');
  const [isActive, setIsActive] = React.useState(false);

  return variant === 'button' ? (
    <Button
      fontWeight={'bold'}
      display={'flex'}
      gap={'.5rem'}
      minH={'3.8rem'}
      variant={'card'}
      onClick={() => {
        console.log(href);
        Router.push(`${href}`);
      }}
    >
      {icon === 'faq' ? (
        <CircleQuestionmarkIcon height={25} width={25} />
      ) : icon === 'privacy' ? (
        <BookIcon height={25} width={25} />
      ) : icon === 'calendar' ? (
        <CalendarIcon height={25} width={25} />
      ) : null}
      {text}
    </Button>
  ) : variant === 'toggle' ? (
    <Flex
      bg={'white'}
      borderRadius={'15px'}
      p={'1rem'}
      alignItems={'center'}
      justifyContent={'space-between'}
    >
      <Flex alignItems={'center'} gap={'.5rem'}>
        {icon === 'faq' ? (
          <CircleQuestionmarkIcon height={25} width={25} />
        ) : icon === 'privacy' ? (
          <BookIcon height={25} width={25} />
        ) : icon === 'bell' ? (
          <BellIcon height={25} width={25} />
        ) : null}
        <Text>{text}</Text>
      </Flex>

      <ToggleButton isActive={isActive} onChange={() => setIsActive(!isActive)} />
    </Flex>
  ) : variant === 'input' ? (
    <Flex
      bg={'white'}
      borderRadius={'15px'}
      p={'1rem'}
      alignItems={'center'}
      justifyContent={'space-between'}
    >
      <Input border={'none'} p={0} m={0} height={'2rem'} fontSize={'sm'} value={text} readOnly />
    </Flex>
  ) : null;
}
