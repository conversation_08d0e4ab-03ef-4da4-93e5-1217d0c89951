import CardContainer from '@/components/ui/cards/card-container';
import InputField from '@/components/ui/formfields/InputField';
import { Flex, FormControl, Text, FormLabel, Input, Link, Button } from '@chakra-ui/react';
import React from 'react';

function Withdrawal() {
  return (
    <form>
      <Flex flexDirection={'column'} gap={'2rem'}>
        <Text variant={'header'}>会員情報編集</Text>

        <CardContainer>
          <Text variant={'subHeaderLarge'}>予約者情報</Text>

          <InputField label={'予約者氏名'} type={'text'} placeholder={'斉藤　翼'} />
          <InputField label={'予約者氏名（カナ）'} type={'text'} placeholder={'サイトウ ツバサ'} />
          <InputField label={'電話番号'} type={'number'} placeholder={'08012345678'} />
          <InputField label={'メールアドレス'} type={'text'} placeholder={'<EMAIL>'} />


          <Flex justifyContent={'flex-end'}>
            <Text fontSize={'xs'}>退会をご希望の方は</Text>
            <Link fontSize={'xs'} variant={'underline'} href={'/client/withdrawal'}>
              こちら
            </Link>
            <Text fontSize={'xs'}>からどうぞ。</Text>
          </Flex>

          <Flex gap={'1rem'}>
            <Button borderColor={'black'} variant={'rounded'} w={'100%'}>
              戻る
            </Button>
            <Button variant={'roundedBlue'} w={'100%'}>
              保存
            </Button>
          </Flex>
        </CardContainer>
      </Flex>
    </form>
  );
}

Withdrawal.getInitialProps = async () => {
  return { layoutType: 'client' };
};

export default Withdrawal;
