import React from 'react';

interface ArrowUpIconProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

const ArrowUpIcon: React.FC<ArrowUpIconProps> = ({
  width = 18,
  height = 18,
  color = '#1E293B',
  className,
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask
        id="mask0_4001_17981"
        style={{ maskType: 'alpha' }}
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="24"
        height="24"
      >
        <rect width={width} height={height} fill={color} />
      </mask>
      <g mask="url(#mask0_4001_17981)">
        <path
          d="M11.9998 10.8L8.0998 14.7C7.91647 14.8834 7.68314 14.975 7.3998 14.975C7.11647 14.975 6.88314 14.8834 6.6998 14.7C6.51647 14.5167 6.4248 14.2834 6.4248 14C6.4248 13.7167 6.51647 13.4834 6.6998 13.3L11.2998 8.70002C11.4998 8.50002 11.7331 8.40002 11.9998 8.40002C12.2665 8.40002 12.4998 8.50002 12.6998 8.70002L17.2998 13.3C17.4831 13.4834 17.5748 13.7167 17.5748 14C17.5748 14.2834 17.4831 14.5167 17.2998 14.7C17.1165 14.8834 16.8831 14.975 16.5998 14.975C16.3165 14.975 16.0831 14.8834 15.8998 14.7L11.9998 10.8Z"
          fill={color}
        />
      </g>
    </svg>
  );
};

export default ArrowUpIcon;
