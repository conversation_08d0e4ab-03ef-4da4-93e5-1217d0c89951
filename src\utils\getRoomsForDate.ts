import moment from 'moment';
import { ReservationEvent } from '@/types/ReservationEventType';

type getRoomsForDateProps = {
  date: Date;
  reservations: ReservationEvent[];
};

export const getRoomsForDate = ({ date, reservations }: getRoomsForDateProps) => {
  const targetDate = moment(date).format('YYYY-MM-DD');

  const dayReservations = reservations.filter(reservation => {
    const reservationDate = moment(reservation.start).format('YYYY-MM-DD');
    return reservationDate === targetDate && reservation.status !== 'cancelled';
  });

  const rooms = Array.from(new Set(dayReservations.map(r => r.room))).sort();
  return rooms;
};
