import React from 'react'
import { GridItem } from '@chakra-ui/react'

interface TableCellProps extends Omit<React.ComponentProps<typeof GridItem>, 'children'> {
  textAlign?: 'left' | 'center' | 'right',
  children: React.ReactNode,
}

export default function TableCell({ 
  children, 
  textAlign, 
  ...rest
 } : TableCellProps) {
  return (
    <GridItem
      textAlign={textAlign || 'center'}
      padding={{ base: '.1rem', lg: '1rem' }}
      py={{ base: '.5rem', lg: '1rem' }}
      fontSize={{ base: 'sm', md: 'sm' }}
      fontWeight={{ base: 'normal' }}
      {...rest}
    >
      {children}
    </GridItem>
  )
}
