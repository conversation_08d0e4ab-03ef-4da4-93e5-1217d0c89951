import type { NextApiRequest, NextApiResponse } from 'next';

type Tag = {
  id: number;
  name: string;
  description: string;
};

let tags: Tag[] = [
  {
    id: 1,
    name: 'リクエスト予約',
    description: 'Request reservation tag',
  },
  {
    id: 2,
    name: '会員限定',
    description: 'Members only tag',
  },
];

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<Tag[] | Tag | { message: string }>
) {
  // GET - Return all tags
  if (req.method === 'GET') {
    return res.status(200).json(tags);
  }

  // POST - Create a new tag
  else if (req.method === 'POST') {
    const { name, description } = req.body;

    if (!name) {
      return res.status(400).json({ message: 'Tag name is required' });
    }

    const newId = tags.length > 0 ? Math.max(...tags.map(t => t.id)) + 1 : 1;
    const newTag = {
      id: newId,
      name,
      description: description || '',
    };

    tags.push(newTag);
    return res.status(201).json(newTag);
  }

  // PUT - Update a tag
  else if (req.method === 'PUT') {
    const { id, name, description } = req.body;

    if (!id || typeof id !== 'number') {
      return res.status(400).json({ message: 'Tag ID is required and must be a number' });
    }

    const tagIndex = tags.findIndex(tag => tag.id === id);

    if (tagIndex === -1) {
      return res.status(404).json({ message: `Tag with ID ${id} not found` });
    }

    tags[tagIndex] = {
      ...tags[tagIndex],
      name: name || tags[tagIndex].name,
      description: description || tags[tagIndex].description,
    };

    return res.status(200).json(tags[tagIndex]);
  }

  // DELETE - Remove a tag
  else if (req.method === 'DELETE') {
    const { id } = req.body;

    if (!id || typeof id !== 'number') {
      return res.status(400).json({ message: 'Tag ID is required and must be a number' });
    }

    const tagIndex = tags.findIndex(tag => tag.id === id);

    if (tagIndex === -1) {
      return res.status(404).json({ message: `Tag with ID ${id} not found` });
    }

    tags = tags.filter(tag => tag.id !== id);
    return res.status(200).json({ message: `Tag with ID ${id} deleted successfully` });
  }

  // Method not allowed
  else {
    return res.status(405).json({ message: 'Method not allowed' });
  }
}
