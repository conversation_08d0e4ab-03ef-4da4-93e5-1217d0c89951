import Calendar from '@/components/ui/calendar/calendar';
import LanguageSwitcher from '@/components/ui/language-switcher';
import { Box, HStack, Text, VStack } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import React, { useState } from 'react';

const CalendarExample: React.FC = () => {
  const { t } = useTranslation('common');
  const router = useRouter();
  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date());

  // Get current date for examples
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth();

  // Example of highlighted dates (e.g., dates with events)
  const highlightedDates = [
    new Date(currentYear, currentMonth, 15),
    new Date(currentYear, currentMonth, 20),
    new Date(currentYear, currentMonth + 1, 5),
  ];

  // Example of disabled dates (e.g., unavailable dates)
  const disabledDates = [
    new Date(currentYear, currentMonth, 10),
    new Date(currentYear, currentMonth, 11),
    new Date(currentYear, currentMonth, 12),
  ];

  // Handle date selection
  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
    console.log('Selected date:', date);
  };

  return (
    <VStack spacing={6} align="stretch" p={4}>
      <HStack justifyContent="space-between">
        <Text fontSize="xl" fontWeight="bold">
          {t('dateSelection')}
        </Text>
        <LanguageSwitcher />
      </HStack>

      <Text fontSize="md">{t('courseDescription')}</Text>

      <Calendar
        onDateSelect={handleDateSelect}
        highlightedDates={highlightedDates}
        disabledDates={disabledDates}
        minDate={new Date(currentYear, currentMonth - 1, 1)}
        maxDate={new Date(currentYear, currentMonth + 2, 0)}
        initialDate={selectedDate || undefined}
      />

      <Box p={4} bg="gray.100" borderRadius="md">
        <Text fontWeight="bold" mb={2}>
          {t('selectedDate', 'Selected Date')}:
        </Text>
        <Text>
          {selectedDate
            ? selectedDate.toLocaleDateString(router.locale === 'ja' ? 'ja-JP' : 'en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })
            : t('none', 'None')}
        </Text>
      </Box>
    </VStack>
  );
};

export default CalendarExample;
