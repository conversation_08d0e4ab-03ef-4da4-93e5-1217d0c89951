import { formatReservationDateTime } from '@/utils/dateUtils';
import { Button, Flex, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import Router from 'next/router';

export default function CardInfo({
  header,
  text,
  variant,
  href,
  amount,
}: {
  header?: string;
  text?: string;
  variant?: string;
  href?: string;
  amount?: string;
}) {
  const { t } = useTranslation('profile');

  // Sample reservation data - in a real app, this would come from props or API
  const reservationDate = new Date('2023-10-25T20:00:00');
  const startTime = '20:00';
  const endTime = '22:00';

  return variant === 'credit' || variant === 'payment-info' ? (
    <Flex flexDirection={'column'} gap={'1rem'}>
      <Text fontWeight={'bold'}>
        <span>
          {variant === 'credit'
            ? t('account.creditBalance')
            : variant === 'payment-info'
            ? t('account.registerPaymentInfo')
            : null}
        </span>
      </Text>
      <Flex
        bg={'white'}
        gap={'1rem'}
        p={'1rem'}
        borderRadius={'1rem'}
        justifyContent={'space-between'}
        alignItems={'center'}
      >
        <Flex alignItems={'center'} gap={'.5rem'}>
          {variant === 'credit' ? (
            <Text fontSize={'1.5rem'} fontWeight={'medium'} color={'mainColor'}>
              {amount}
            </Text>
          ) : null}
          <Text fontSize={'sm'}>
            {variant === 'credit' ? text : variant === 'payment-info' ? text : null}
          </Text>
        </Flex>
        <Button
          w={'9rem'}
          variant={'roundedBlue'}
          onClick={() => {
            Router.push(`${href}/test-id`);
          }}
        >
          <span>
            {variant === 'credit'
              ? t('account.charge')
              : variant === 'payment-info'
              ? t('account.registerPaymentInfo')
              : null}
          </span>
        </Button>
      </Flex>
    </Flex>
  ) : variant === 'reservation' ? (
    <Flex
      bg={'white'}
      direction={'column'}
      gap={'.5rem'}
      p={'1rem'}
      borderRadius={'1rem'}
      justifyContent={'space-between'}
    >
      <Flex>
        <Text variant={'subHeaderLarge'}>やきにくや YAKINIKU-YA</Text>
      </Flex>
      <Flex gap={'1rem'}>
        <Text color={'gray.400'}>{t('reservations.reservationNumber')}</Text>
        <Text color={'gray.400'}>************</Text>
      </Flex>

      <Flex flexDirection={'column'} fontWeight={'medium'} fontSize={'sm'} gap={'.5rem'}>
        <Flex>
          <Text w={'5rem'}>{t('reservations.reservationDate')}</Text>
          <Text>{formatReservationDateTime(reservationDate, startTime, endTime, t)}</Text>
        </Flex>

        <Flex>
          <Text w={'5rem'}>{t('reservations.reservationNumber')}</Text>
          <Text>0000-0000-0000</Text>
        </Flex>
      </Flex>

      <Button
        w={'100%'}
        variant={'roundedBlue'}
        fontSize={'sm'}
        fontWeight={'normal'}
        gap={2}
        onClick={() => {
          const id = 1; // TODO: Dynamic ID - can be passed as prop or retrieved from data
          Router.push(`/client/reservation-history/${id}`);
        }}
      >
        <span>{t('reservations.viewReservationInfo')}</span>
      </Button>
    </Flex>
  ) : null;
}
