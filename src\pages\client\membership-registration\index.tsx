import MembershipRegistrationForm from '@/components/forms/membership-registration-form';
import { Flex, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

function MembershipRegistration() {
  const { t } = useTranslation('auth');

  return (
    <Flex flexDirection={'column'} gap={'1rem'}>
      <Text variant={'header'} mb={'1rem'}>
        {t('membershipRegistration.title')}
      </Text>

      <Text variant={'error'}>{t('membershipRegistration.termsAgreement')}</Text>

      <MembershipRegistrationForm />
    </Flex>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'auth'])),
      layoutType: 'client',
    },
  };
}

export default MembershipRegistration;
