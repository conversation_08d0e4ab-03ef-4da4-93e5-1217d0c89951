import React from 'react';
import { Flex, Text, Textarea, Input } from '@chakra-ui/react';

type InputTextEditModeProps = {
  editMode: boolean;
  type?: string;
  label: string;
  bottomText?: string;
  placeholder?: string;
  value: string;
};

export default function InputTextEditMode({
  editMode,
  type,
  label,
  bottomText,
  placeholder,
  value,
}: InputTextEditModeProps) {
  return (
    <Flex
      flexDirection={'column'}
      gap={2}
      borderBottom={'1px solid #E5EAF1'}
      pb={'1rem'}
      mb={'1rem'}
    >
      <Text fontSize={'md'} fontWeight={'semibold'}>
        {label}
      </Text>

      {editMode ? (
        type === 'textarea' ? (
          <Textarea placeholder={placeholder} value={value} height={'6rem'} />
        ) : (
          <Input placeholder={placeholder} fontSize={'sm'} value={value} />
        )
      ) : (
        <Text fontSize={'sm'} color={'black600'}>
          {value}
        </Text>
      )}

      {bottomText && (
        <Text fontSize={'sm'} color={'black600'}>
          {bottomText}
        </Text>
      )}
    </Flex>
  );
}
