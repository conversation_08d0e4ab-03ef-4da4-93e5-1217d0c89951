import React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, Text } from '@chakra-ui/react'
import BackIcon from '@/assets/icons/back';
import router from 'next/router';

export default function AdminPreviewsPage({label}: {label?: string}) {
  return (
    <Flex alignItems={'center'}>
        <Button
        variant={'ghost'}
        w={'auto'}
        p={0}
        m={0}
        onClick={() => {
            router.back();
        }}
        >
        <BackIcon width={30} height={30} color="black" className="my-icon-class" />
        </Button>
        <Text fontSize={'lg'} fontWeight={'bold'}>
        {label ? label : ''}
        </Text>
    </Flex>
  )
}
