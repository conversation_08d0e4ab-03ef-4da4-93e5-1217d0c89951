import CheckCircleIcon from '@/assets/icons/check-circle';
import { ReservationType } from '@/types/reservarionType';
import {
  Box,
  Button,
  Flex,
  Image,
  Link,
  Modal,
  ModalBody,
  ModalContent,
  Modal<PERSON>ooter,
  ModalHeader,
  ModalOverlay,
  Text,
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import React from 'react';

export default function ReservationCancellationModal({
  isOpen,
  onClose,
  onOpen,
  data,
}: {
  isOpen: boolean;
  onClose: () => void;
  onOpen: () => void;
  data: ReservationType;
}) {
  const { t } = useTranslation('common');
  const [modalStep, setModalStep] = React.useState(1);

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          {modalStep === 1 ? (
            t('reservations.confirmCancellation')
          ) : (
            <Text variant={'brand'} textAlign={'center'}>
              JUSTYAKINIKU
            </Text>
          )}
        </ModalHeader>
        {modalStep === 1 ? (
          <ModalBody
            p={{ base: '1rem', md: '1.5rem' }}
            borderTop={'1px solid'}
            borderColor={modalStep === 1 ? 'gray.200' : 'transparent'}
          >
            <Flex
              alignItems={'center'}
              position={'relative'}
              gap={'1rem'}
              borderBottom={'1px dashed'}
              borderColor={'gray.400'}
              pb={'1rem'}
              mb={'1rem'}
            >
              <Flex
                justifyContent={'center'}
                alignItems={'center'}
                position={'relative'}
                overflow={'hidden'}
                borderRadius={'.8rem'}
                height={'7rem'}
                bg={'gray.200'}
              >
                <Image
                  style={{
                    objectFit: 'cover',
                    height: '100%',
                    objectPosition: 'center',
                  }}
                  src={'/imgs/icons/profileIcon.svg'}
                  alt="option"
                />
              </Flex>

              <Flex flexDirection={'column'} gap={'.5rem'}>
                <Text variant={'brand'} fontSize={'1.5rem'}>
                  {data?.storeName}
                </Text>
                <Text fontWeight={'bold'}>{data?.storeKana}</Text>
                <Text fontSize={'xs'}>{data?.location}</Text>
              </Flex>
            </Flex>

            <Flex flexDirection={'column'} gap={'1rem'}>
              <Flex gap={'.5rem'} alignItems={'center'}>
                <Text variant={'category'}>{t('reservations.visitDateTime')}</Text>
                <Text fontSize={'md'} fontWeight={'bold'}>
                  {data?.dateTime}
                </Text>
              </Flex>
              <Flex gap={'.5rem'} alignItems={'center'}>
                <Text variant={'category'}>{t('reservations.numberOfVisitors')}</Text>
                <Text fontSize={'md'} fontWeight={'bold'}>
                  {data?.people}
                  {` ${t('formats.people')}`}
                </Text>
              </Flex>
              <Flex gap={'.5rem'} alignItems={'center'}>
                <Text variant={'category'}>{t('reservations.reservationNumber')}</Text>
                <Text fontSize={'md'} fontWeight={'bold'}>
                  {data?.reservationNumber}
                </Text>
              </Flex>
            </Flex>
          </ModalBody>
        ) : (
          <ModalBody display={'flex'} flexDir={'column'} alignItems={'center'} gap={'1rem'}>
            <Text textAlign={'center'} variant={'subHeaderNormal'}>
              {t('reservations.cancellationComplete')}
            </Text>
            <CheckCircleIcon />
            <Box>
              <Text textAlign={'center'} fontWeight={'normal'} variant={'subHeaderNormal'}>
                {t('reservations.cancellationComplete')}
              </Text>
              <Link href={'#'}>{t('reservations.viewCancellationDetails')}</Link>
            </Box>
          </ModalBody>
        )}
        {modalStep === 1 ? (
          <ModalFooter gap={'1rem'}>
            <Button
              variant={'rounded'}
              w={'100%'}
              borderColor={'black'}
              onClick={() => {
                onClose();
              }}
            >
              {t('buttons.back')}
            </Button>
            <Button
              variant={'roundedBlue'}
              w={'100%'}
              onClick={() => {
                setModalStep(2);
              }}
            >
              {t('buttons.cancelReservation')}
            </Button>
          </ModalFooter>
        ) : (
          <ModalFooter>
            <Button
              variant={'rounded'}
              w={'100%'}
              borderColor={'transparent'}
              bg={'gray.100'}
              onClick={() => {
                setModalStep(1);
                onClose();
              }}
            >
              {t('buttons.back')}
            </Button>
          </ModalFooter>
        )}
      </ModalContent>
    </Modal>
  );
}
