{"addStore": {"modal": {"title": "Add Store", "steps": {"storeInfo": {"title": "Store Information", "description": "Set up information about your store."}, "businessHours": {"title": "Business Hours", "description": ["Set the time range for reservations.", "This setting can be changed and customized later."]}}, "stepIndicator": "Step {{current}}/{{total}}", "form": {"storeIcon": "Store Icon", "storeName": "Store Name", "storeNamePlaceholder": "<PERSON><PERSON>", "storeId": "Store ID", "storeIdPlaceholder": "example-shop", "storeIdHelper": "Store ID will become the URL for your reservation page", "storeOverview": "Store Overview", "storeOverviewPlaceholder": "Yakiniku Restaurant"}, "buttons": {"next": "Next", "back": "Go Back", "complete": "Complete"}}, "timeSelect": {"dayNames": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "closedDay": "Closed", "buttons": {"addTimeSlot": "Add Time Slot", "removeTimeSlot": "Remove Time Slot"}}}, "sidebar": {"navigation": {"reservationsList": "Reservations", "membersList": "Members", "newsList": "News", "optionsList": "Options", "storeDetails": "Store Info", "storeOption": "Store Settings"}, "storeManagement": {"currentStore": "Store Name", "addStore": "+ Add Store", "storeList": "Store List"}, "userMenu": {"username": "Username", "myPage": "My Page", "logout": "Logout"}}, "storeDetails": {"header": {"title": "Store Information"}, "buttons": {"edit": "Edit"}, "sections": {"storePhoto": "Store Photo"}, "fields": {"storeId": "Store ID", "storeName": "Store Name", "storeAddress": "Store Address", "contact": "Contact", "businessHours": "Business Hours", "website": "Official Website", "storeOverview": "Store Overview"}, "businessHours": {"monday": "Mon　08:00 - 22:00", "tuesday": "<PERSON><PERSON>　08:00 - 22:00", "wednesday": "Wed　08:00 - 22:00", "thursday": "Thu　08:00 - 22:00", "friday": "Fri　08:00 - 22:00", "saturday": "Sat　Closed", "sunday": "Sun　Closed"}, "content": {"storeDescription": "Share Space is a shared office that combines flexibility and comfort optimal for business. We provide a professional environment that can accommodate various working styles, from startups, freelancers, remote workers, to small and medium-sized enterprises."}}, "storeDetailsEdit": {"header": {"title": "Edit Store Information"}, "fields": {"storePhoto": "Store Photo", "storeId": "Store ID", "storeName": "Store Name", "storeAddress": "Store Address", "contact": "Contact", "businessHours": "Business Hours", "website": "Official Website", "notes": "Notes"}, "placeholders": {"storeId": "example-shop", "storeName": "Yakiniku-ya YAKINIKU-YA Azabu-Juban Store", "storeAddress": "4F, 2-2-9 <PERSON><PERSON><PERSON><PERSON><PERSON>, Minato-ku, Tokyo", "contact": "080-1234-1234", "website": "sharespace.jp", "notes": "Content"}, "buttons": {"complete": "Complete"}}, "confirmationModal": {"step1": {"message": "Do you want to edit the store information\nwith the entered content?"}, "step2": {"message": "Store information has been registered"}, "buttons": {"back": "Back", "register": "Register", "close": "Close"}}, "storeOption": {"pageTitle": "Store Operation Settings", "businessHours": {"title": "Business Hours", "closedDay": "Closed", "dayNames": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "buttons": {"addTimeSlot": "Add Time Slot", "removeTimeSlot": "Remove Time Slot"}}, "operationMethod": {"title": "Store Operation Method", "description": "Specify the method of store operation.", "staffed": "Always Staffed Operation", "unstaffed": "Unstaffed Operation"}, "paymentMethod": {"title": "Payment Method", "description": "Please specify the payment method at the store.", "creditCharge": "Credit Charge Method", "onlinePayment": "Online Payment"}, "selectionMethod": {"title": "Selection Method", "description": "Specify the time available for reservations.", "optionFirst": "Options First", "calendarFirst": "Calendar First"}, "reservationAcceptance": {"title": "Reservation Acceptance Method", "description": "When there is a reservation request, you can specify whether to confirm and decide to accept it, or to accept it automatically.", "manual": "Confirm and accept each reservation", "automatic": "Automatically accept if slots are available"}, "buttons": {"complete": "Complete"}}, "memberDetails": {"header": {"title": "Member Details"}, "buttons": {"edit": "Edit", "complete": "Complete", "charge": "Charge", "delete": "Delete", "close": "Close"}, "modal": {"editComplete": "Member information editing has been completed"}, "charge": {"modal": {"title": "Credit Charge", "chargeComplete": "Charge has been completed"}, "form": {"remaining": "Remaining", "chargeAmount": "Charge"}, "buttons": {"charge": "Add Charge"}}, "form": {"email": "Email Address", "password": "Password", "contact": "Contact", "name": "Name", "nameKana": "Name (<PERSON>na)", "credit": "Credit", "creditRemaining": "Remaining / Total", "groupMemberCount": "Customer Group Member Count", "notes": "Notes", "notesPlaceholder": "※Inquiries and requests regarding treatments"}, "display": {"memberCount": "{{count}} people", "content": "Content"}, "sns": {"title": "Link with SNS Accounts", "lineRegister": "Register with LINE", "googleRegister": "Register with Google", "completed": "Completed", "link": "Link"}}, "membersList": {"title": "Members List", "buttons": {"addMember": "Add Member"}, "search": {"placeholder": "Filter"}, "filter": {"buttonText": "Filter", "title": "Filter", "keyword": "Keyword", "keywordPlaceholder": "Placeholder", "status": "Status", "statusPlaceholder": "{Status}", "statusOptions": {"pending": "Pending", "active": "Active", "deleted": "Deleted", "cancelled": "Cancelled"}, "groupType": "Group / Individual", "groupTypePlaceholder": "{None}", "applyButton": "Show"}, "table": {"headers": {"status": "Status", "groupName": "Group Name", "time": "Time", "charge": "Charge", "contact": "Contact"}, "actions": {"charge": "Charge", "details": "Details", "delete": "Delete"}}, "addMember": {"title": "Member Invitation", "form": {"email": "Email Address", "emailPlaceholder": "example.justyoyaku.com", "groupName": "Group Name", "groupNamePlaceholder": "justyoyaku team", "reservationAvailableTime": "Reservation available time", "reservationAvailableTimePlaceholder": "09:00 - 18:00", "possibleNumberOfPeople": "Possible number of people to entertain", "possibleNumberOfPeoplePlaceholder": "3", "NumberOfAdvanceReservationsPossible": "Number of advance reservations possible", "NumberOfAdvanceReservationsPossiblePlaceholder": "3", "NumberOfAdvanceReservationsPossibleHelper": "This is the maximum number of advance reservations that can be made on or after the date.", "NumberOfAdvanceReservationsDays": "Number of days available for advance reservation", "NumberOfAdvanceReservationsDaysPlaceholder": "7", "NumberOfAdvanceReservationsDaysHelper": "This is the maximum number of days you can reserve in advance.", "groupSelect": "Group Selection", "groupSelectPlaceholder": "Select Group", "notes": "Notes", "notesPlaceholder": "Please enter content"}, "buttons": {"add": "Add", "back": "Back", "send": "Send", "close": "Close"}, "confirmation": {"title": "Send invitation email?", "message1": "An invitation email will be sent to", "message2": "the email address you entered."}, "success": {"message": "<PERSON><PERSON> has been sent successfully"}}, "deleteConfirmation": {"title": "Delete this post?", "message": "Please note that deleted posts cannot be recovered.", "success": "Post has been deleted", "buttons": {"back": "Back", "delete": "Delete", "close": "Close"}}, "chargeModal": {"title": "Credit Charge", "remaining": "Remaining", "numCredit": "num credit", "charge": "Charge", "placeholder": "Amount", "success": "Charge completed successfully", "buttons": {"cancel": "Cancel", "charge": "Charge", "close": "Close"}}}, "enterUserInformation": {"title": "ユーザー設定", "form": {"profileIcon": "Profile Icon", "changePhoto": "Change Photo", "username": "Username", "groupName": "Group Name", "email": "Email Address", "password": "Password"}, "buttons": {"googleCalendar": "Google Calendar", "tieAString": "Tie a String"}, "steps": {"userInfo": {"title": "User Settings", "description": "Set up user-related information."}, "storeInfo": {"title": "Store Information", "description": "Set up store-related information."}, "externalCalendar": {"title": "External Calendar Connection", "description": "Connect with external calendar services to sync your schedule."}, "businessHours": {"title": "Business Hours", "description": "Set the time range for reservations.", "customizableNote": "This setting can be changed and customized later."}}}, "errors": {"invalidCredentials": "Invalid credentials", "URLInUse": "The URL is already in use"}}