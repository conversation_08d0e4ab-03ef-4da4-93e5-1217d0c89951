import AddStoreModal from '@/components/modals/add-store-modal';
import LanguageSwitcher from '@/components/ui/language-switcher';
import { Flex } from '@chakra-ui/react';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

function AddStore() {
  return (
    <>
      <Flex
        justifyContent="flex-end"
        width="100%"
        position="absolute"
        top={4}
        right={4}
        zIndex={10}
      >
        <LanguageSwitcher />
      </Flex>
      <AddStoreModal />
    </>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'admin'])),
    },
  };
}

export default AddStore;
