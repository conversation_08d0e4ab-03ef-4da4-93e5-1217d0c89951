import { useTempCatStore, useTempTagStore } from '@/store/temp/admin/tempCatTagStore';
import { useTempOptionListStore } from '@/store/temp/admin/tempOptionList';
import { Flex, Grid, SelectField, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import React from 'react';
import OptionManagmentCard from '../ui/option-managment-card';
import StoreBanner from '../ui/store-banner';

function OptionSettings() {
  const { t } = useTranslation('common');
  const optionList = useTempOptionListStore(state => state.optionList);
  const sampleOptionListCategory = useTempCatStore(state => state.categories);
  const sampleOptionListTags = useTempTagStore(state => state.tags);

  return (
    <Flex w={'100%'} flexDirection={'column'}>
      <Flex
        flexDirection={'column'}
        bg={'white'}
        gap={4}
        p={5}
        pt={{ base: '2rem', sm: '3rem' }}
        borderRadius={'1rem'}
      >
        <StoreBanner />

        <Flex gap={4} py={{ base: '2rem', sm: '3rem' }}>
          <SelectField
            fontSize={'sm'}
            borderRadius={'4px'}
            border={'1px solid #E5EAF1'}
            px={3}
            py={1}
            w={{ base: '100%', sm: '9rem' }}
          >
            <option value="">{t('optionslist.filters.category')}</option>
            {sampleOptionListCategory.map(category => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </SelectField>

          <SelectField
            fontSize={'sm'}
            borderRadius={'4px'}
            border={'1px solid #E5EAF1'}
            px={3}
            py={1}
            w={{ base: '100%', sm: '9rem' }}
          >
            <option value="">{t('optionslist.filters.tag')}</option>
            {sampleOptionListTags.map(tag => (
              <option key={tag.id} value={tag.id}>
                {tag.name}
              </option>
            ))}
          </SelectField>
        </Flex>

        {sampleOptionListCategory.map(category => (
          <React.Fragment key={category.id}>
            <Flex borderBottom={'1px solid #E5EAF1'} pb={'1rem'}>
              <Text fontSize={'sm'} fontWeight={'bold'}>
                {category.name}
              </Text>
            </Flex>

            <Grid templateColumns="repeat(2, 1fr)">
              {optionList.map(
                option =>
                  option.category === category.name && (
                    <OptionManagmentCard key={option.id} option={option} />
                  )
              )}
            </Grid>
          </React.Fragment>
        ))}
      </Flex>
    </Flex>
  );
}

export default OptionSettings;
