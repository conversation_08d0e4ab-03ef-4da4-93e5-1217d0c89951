import React from 'react';
import { FormControl, FormLabel, Select } from '@chakra-ui/react';
import DropDownTriangleIcon from '@/assets/icons/DropDownTriangleIcon';

type DropDownProps = {
  label?: string;
  placeholder?: string;
  options: string[];
  width?: string;
  required?: boolean;
};

export default function DropDown({ label, placeholder, options, width, required }: DropDownProps) {
  return (
    <FormControl>
      <FormLabel variant={required ? 'required' : ''}>{label}</FormLabel>
      <Select
        className="border-gray"
        placeholder={placeholder}
        iconSize="24px"
        height={'3rem'}
        icon={<DropDownTriangleIcon width={24} height={24} />}
        borderRadius={'.5rem'}
        w={width ? width : 'auto'}
      >
        {options.map(option => (
          <option key={option} value={option}>
            {option}
          </option>
        ))}
      </Select>
    </FormControl>
  );
}
