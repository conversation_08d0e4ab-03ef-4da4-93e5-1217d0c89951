import { FormDataType } from "@/types/FormDataType";
import { DayKeyType } from "@/types/dayType";

type RemoveTimeSlotProps = {
    day: string;
    index: number;
    formData: FormDataType;
    setFormData: (value: FormDataType) => void;
};

export const removeTimeSlot = ({day, index, formData, setFormData }: RemoveTimeSlotProps) => {
    setFormData({
        ...formData,
        availability: {
        ...formData?.availability,
        timeSlots: {
            ...formData?.availability?.timeSlots,
            [day]: formData?.availability?.timeSlots[day as keyof typeof formData.availability.timeSlots]?.filter((_, i) => i !== index),
        },
        },
    });
};