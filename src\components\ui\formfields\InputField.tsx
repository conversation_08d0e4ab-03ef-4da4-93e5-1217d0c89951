import {
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  Text,
  Textarea,
} from '@chakra-ui/react';
import React from 'react';

type InputFieldProps = {
  variant?: string;
  label: string;
  prefix?: string;
  name?: string;
  type?: string;
  placeholder?: string;
  value?: string | number;
  required?: boolean;
  onChange?: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  errors?: string;
  helperText?: string;
  subHelperText?: string;
};

const InputField = ({
  variant,
  label,
  prefix,
  name,
  type,
  value,
  onChange,
  required,
  placeholder,
  errors,
  helperText,
  subHelperText,
}: InputFieldProps) => {
  return variant === 'refixed' ? (
    <FormControl isInvalid={errors ? !!errors : false}>
      <FormLabel variant={'required'}>{label}</FormLabel>
      <Flex gap={0}>
        <FormLabel
          bg={'#F8FAFC'}
          color={'#94B2D7'}
          fontSize={'sm'}
          m={0}
          px={4}
          borderLeftRadius={'10px'}
          border={'1px solid #E5EAF1'}
          display={'flex'}
          alignItems={'center'}
        >
          {prefix ? prefix : 'xxx.com/'}
        </FormLabel>

        <Input
          p={'6'}
          name={name}
          type="text"
          value={value}
          onChange={e => {
            onChange?.(e);
            // setOptionList({ ...optionListData, menuDetails: e.target.value });
          }}
          placeholder="example-shop"
          borderLeftRadius={0}
        />
      </Flex>
      <FormErrorMessage>{errors}</FormErrorMessage>
      <Text variant={'errorText'}>{helperText}</Text>
    </FormControl>
  ) : variant === 'textArea' ? (
    <FormControl>
      <FormLabel variant={required ? 'required' : ''}>{label}</FormLabel>
      <Textarea
        p={'6'}
        height={'10rem'}
        name={name}
        className="border-gray"
        placeholder={placeholder}
        value={value}
        onChange={e => {
          // setOptionList({ ...optionListData, menuDetails: e.target.value });
          onChange?.(e);
        }}
      />
    </FormControl>
  ) : (
    <FormControl>
      <FormLabel variant={required ? 'required' : ''}>{label}</FormLabel>
      <Input
        p={'6'}
        name={name}
        className="border-gray"
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={e => {
          onChange?.(e);
          // setOptionList({ ...optionListData, menuTitle: e.target.value });
        }}
      />
      <Text fontSize={'xs'} py={'.3rem'}>
        {subHelperText ? subHelperText : ''}
      </Text>
    </FormControl>
  );
};

export default InputField;
