import type { NextApiRequest, NextApiResponse } from 'next';

type Category = {
  id: number;
  name: string;
  description: string;
};

let categories: Category[] = [
  {
    id: 1,
    name: 'important',
    description: 'Something important with red background',
  },
  {
    id: 2,
    name: 'news',
    description: 'Something news with gray background',
  },
];

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<Category[] | Category | { message: string }>
) {
  // GET - Return all categories
  if (req.method === 'GET') {
    return res.status(200).json(categories);
  }

  // POST - Create a new category
  else if (req.method === 'POST') {
    const { name, description } = req.body;

    if (!name) {
      return res.status(400).json({ message: 'Category name is required' });
    }

    const newId = categories.length > 0 ? Math.max(...categories.map(c => c.id)) + 1 : 1;
    const newCategory = {
      id: newId,
      name,
      description: description || '',
    };

    categories.push(newCategory);
    return res.status(201).json(newCategory);
  }

  // PUT - Update a category
  else if (req.method === 'PUT') {
    const { id, name, description } = req.body;

    if (!id || typeof id !== 'number') {
      return res.status(400).json({ message: 'Category ID is required and must be a number' });
    }

    const categoryIndex = categories.findIndex(category => category.id === id);

    if (categoryIndex === -1) {
      return res.status(404).json({ message: `Category with ID ${id} not found` });
    }

    categories[categoryIndex] = {
      ...categories[categoryIndex],
      name: name || categories[categoryIndex].name,
      description: description || categories[categoryIndex].description,
    };

    return res.status(200).json(categories[categoryIndex]);
  }

  // DELETE - Remove a category
  else if (req.method === 'DELETE') {
    const { id } = req.body;

    if (!id || typeof id !== 'number') {
      return res.status(400).json({ message: 'Category ID is required and must be a number' });
    }

    const categoryIndex = categories.findIndex(category => category.id === id);

    if (categoryIndex === -1) {
      return res.status(404).json({ message: `Category with ID ${id} not found` });
    }

    categories = categories.filter(category => category.id !== id);
    return res.status(200).json({ message: `Category with ID ${id} deleted successfully` });
  }

  // Method not allowed
  else {
    return res.status(405).json({ message: 'Method not allowed' });
  }
}
