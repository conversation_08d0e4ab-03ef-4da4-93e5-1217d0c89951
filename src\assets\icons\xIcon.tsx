import React from 'react';

interface XIconProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

const XIcon: React.FC<XIconProps> = ({ width = 18, height = 18, color = '#1E293B', className }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 25 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_5271_4738)">
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M22.2736 2.58579C23.0546 3.36683 23.0546 4.63317 22.2736 5.41421L6.27359 21.4142C5.49254 22.1953 4.22621 22.1953 3.44516 21.4142C2.66411 20.6332 2.66411 19.3668 3.44516 18.5858L19.4452 2.58579C20.2262 1.80474 21.4925 1.80474 22.2736 2.58579Z"
          fill={color}
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M3.44516 2.58579C4.22621 1.80474 5.49254 1.80474 6.27359 2.58579L22.2736 18.5858C23.0546 19.3668 23.0546 20.6332 22.2736 21.4142C21.4925 22.1953 20.2262 22.1953 19.4452 21.4142L3.44516 5.41421C2.66411 4.63317 2.66411 3.36683 3.44516 2.58579Z"
          fill={color}
        />
      </g>
      <defs>
        <clipPath id="clip0_5271_4738">
          <rect
            width={width + 6}
            height={height + 6}
            fill="white"
            transform="translate(0.859375)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default XIcon;
