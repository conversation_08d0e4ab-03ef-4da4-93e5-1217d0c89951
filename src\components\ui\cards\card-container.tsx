import React, { ReactNode } from 'react';
import { Flex, FlexProps } from '@chakra-ui/react';

interface CardContainerProps extends Omit<FlexProps, 'children'> {
  children: ReactNode;
  variant?: string;
}

export default function CardContainer({
  children,
  variant = 'default',
  p = '1.5rem',
  px,
  py,
  gap = '1.5rem',
  ...rest
}: CardContainerProps) {
  return (
    <Flex
      bg={'white'}
      borderRadius={'15px'}
      borderColor={variant === 'bordered' ? 'borderGray' : 'none'}
      borderWidth={variant === 'bordered' ? '1px' : 'none'}
      p={variant === 'bordered' ? '1rem' : p}
      px={px}
      py={py}
      flexDirection={'column'}
      alignItems={'center'}
      justifyContent={'center'}
      gap={gap}
      {...rest}
    >
      {children}
    </Flex>
  );
}
