import React from 'react';
import { Flex, Text, Image } from '@chakra-ui/react';
// import Image from 'next/image'

function StoreBanner() {
  return (
    <Flex
      flexDirection={{ base: 'column', sm: 'row' }}
      flexWrap={'wrap'}
      alignItems={'center'}
      justifyContent={{ base: 'center', lg: 'unset' }}
      pl={{ base: '0', md: '2rem' }}
    >
      <Image
        src={'/imgs/icons/yakiniku-ya-long.png'}
        h={{ base: '18px', sm: 'unset' }}
        w={'auto'}
        mb={{ base: '1rem', sm: '0' }}
        alt="option"
      />
      <Text fontSize={'sm'} ml={'1rem'} fontWeight={'bold'}>
        やきにくや YAKINIKU-YA 麻布十番店
      </Text>
      <Text fontSize={'xs'} ml={'1rem'}>
        東京都港区麻布十番2丁目2−9 4F
      </Text>
    </Flex>
  );
}

export default StoreBanner;
