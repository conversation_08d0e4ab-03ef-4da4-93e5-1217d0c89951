import { ReactNode } from 'react';
import BlankLayout from './BlankLayout';
import WithSidebarLayout from './WithSidebarLayout';
import ClientLayout from './ClientLayout';

type LayoutProps = {
  children: ReactNode;
  type: 'blank' | 'withSidebar' | 'client';
  navConfig?: any;
  activePage?: string;
  setActivePage?: (page: string) => void;
};

export default function Layout({
  children,
  type,
  navConfig,
  activePage,
  setActivePage,
}: LayoutProps) {
  if (type === 'blank') {
    return <BlankLayout>{children}</BlankLayout>;
  }

  if (type === 'client') {
    return <ClientLayout>{children}</ClientLayout>;
  }

  return (
    <WithSidebarLayout
      navConfig={navConfig}
      activePage={activePage || ''}
      setActivePage={setActivePage || ((page: string) => {})}
    >
      {children}
    </WithSidebarLayout>
  );
}
