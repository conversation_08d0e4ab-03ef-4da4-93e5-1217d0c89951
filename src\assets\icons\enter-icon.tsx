import React from 'react';

interface EnterIconProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

const EnterIcon: React.FC<EnterIconProps> = ({
  width = 18,
  height = 18,
  color = '#1E293B',
  className,
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.923 12.382C20.0241 12.1374 20.0241 11.8626 19.923 11.618C19.8731 11.496 19.7993 11.3851 19.706 11.292L15.706 7.292C15.6138 7.19649 15.5034 7.12031 15.3814 7.0679C15.2594 7.01549 15.1282 6.9879 14.9954 6.98675C14.8626 6.9856 14.7309 7.0109 14.608 7.06118C14.4851 7.11146 14.3735 7.18571 14.2796 7.2796C14.1857 7.3735 14.1115 7.48515 14.0612 7.60805C14.0109 7.73094 13.9856 7.86262 13.9867 7.9954C13.9879 8.12818 14.0155 8.2594 14.0679 8.3814C14.1203 8.50341 14.1965 8.61375 14.292 8.706L16.586 11H8C7.73478 11 7.48043 11.1054 7.29289 11.2929C7.10536 11.4804 7 11.7348 7 12C7 12.2652 7.10536 12.5196 7.29289 12.7071C7.48043 12.8946 7.73478 13 8 13H16.586L14.293 15.293C14.1975 15.3852 14.1213 15.4956 14.0689 15.6176C14.0165 15.7396 13.9889 15.8708 13.9877 16.0036C13.9866 16.1364 14.0119 16.2681 14.0622 16.391C14.1125 16.5138 14.1867 16.6255 14.2806 16.7194C14.3745 16.8133 14.4861 16.8875 14.609 16.9378C14.7319 16.9881 14.8636 17.0134 14.9964 17.0123C15.1292 17.0111 15.2604 16.9835 15.3824 16.9311C15.5044 16.8787 15.6148 16.8025 15.707 16.707L19.707 12.707C19.7999 12.6141 19.8733 12.5036 19.923 12.382Z"
        fill={color}
      />
      <path
        d="M10 18H7C6.73478 18 6.48043 17.8946 6.29289 17.7071C6.10536 17.5196 6 17.2652 6 17V7C6 6.73478 6.10536 6.48043 6.29289 6.29289C6.48043 6.10536 6.73478 6 7 6H10C10.2652 6 10.5196 5.89464 10.7071 5.70711C10.8946 5.51957 11 5.26522 11 5C11 4.73478 10.8946 4.48043 10.7071 4.29289C10.5196 4.10536 10.2652 4 10 4H7C6.20435 4 5.44129 4.31607 4.87868 4.87868C4.31607 5.44129 4 6.20435 4 7V17C4 17.7956 4.31607 18.5587 4.87868 19.1213C5.44129 19.6839 6.20435 20 7 20H10C10.2652 20 10.5196 19.8946 10.7071 19.7071C10.8946 19.5196 11 19.2652 11 19C11 18.7348 10.8946 18.4804 10.7071 18.2929C10.5196 18.1054 10.2652 18 10 18Z"
        fill={color}
      />
    </svg>
  );
};

export default EnterIcon;
