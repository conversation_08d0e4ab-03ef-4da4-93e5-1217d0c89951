import React from 'react';
import { Box, Flex, Switch, SwitchProps, Text } from '@chakra-ui/react';

interface ToggleButtonProps extends Omit<SwitchProps, 'isChecked'> {
  isActive: boolean;
  activeColor?: string;
  inactiveColor?: string;
  label?: string;
  onChange?: () => void;
}

export const ToggleButton = React.forwardRef<HTMLInputElement, ToggleButtonProps>(
  function ToggleButton(props, ref) {
    const {
      isActive,
      activeColor = '#2B42CA',
      inactiveColor = '#94A3B8',
      label,
      onChange,
      ...rest
    } = props;

    return (
      <Flex align="center" width="fit-content">
        {label && (
          <Text mr={3} fontSize="sm" fontWeight="medium">
            {label}
          </Text>
        )}
        <Switch
          ref={ref}
          isChecked={isActive}
          onChange={onChange}
          colorScheme="blue"
          size="md"
          sx={{
            '--switch-track-width': '4rem',
            '--switch-track-height': '1.9rem',
            '.chakra-switch__track': {
              width: 'var(--switch-track-width)',
              height: 'var(--switch-track-height)',
              background: isActive ? activeColor : inactiveColor,
              borderRadius: 'full',
              padding: 0,
              transition: 'background-color 0.3s',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'start',
            },
            '.chakra-switch__thumb': {
              width: 'calc(var(--switch-track-height) - 0.3rem)',
              height: 'calc(var(--switch-track-height) - 0.3rem)',
              background: 'white',
              boxShadow: '0px 1px 3px rgba(0, 0, 0, 0.1)',
              position: 'absolute',
              top: '50%',
              transform: isActive
                ? 'translateX(calc(var(--switch-track-width) - var(--switch-track-height) + 0.15rem)) translateY(-50%)'
                : 'translateX(0.15rem) translateY(-50%)',
              transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            },
          }}
          {...rest}
        />
      </Flex>
    );
  }
);
