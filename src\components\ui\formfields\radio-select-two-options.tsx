import React from 'react';
import { Flex, Text, Radio, RadioGroup, Box } from '@chakra-ui/react';

export default function RadioSelectWithTwoOptions({
  head,
  subHead,
  option1,
  option2,
}: {
  head: string;
  subHead: string;
  option1: string;
  option2: string;
}) {
  return (
    <Flex
      flexDirection={'column'}
      gap={2}
      borderBottom={'1px solid #E5EAF1'}
      pb={'1rem'}
      mb={'1rem'}
    >
      <Text fontSize={'md'} fontWeight={'semibold'}>
        {head}
      </Text>
      <Text fontSize={'sm'} color={'black600'}>
        {subHead}
      </Text>

      <RadioGroup display={'flex'} gap={0.5} flexDirection={'column'}>
        <Box
          _hover={{
            cursor: 'pointer',
            backgroundColor: 'blue.100',
            color: 'blue.primary',
            fontWeight: '800',
          }}
          px={'0.8rem'}
          borderRadius={'10px'}
        >
          <Radio size="sm" value="1">
            <Box as="span" fontSize={'lg'} w={'100%'} fontWeight={'400'}>
              {option1}
            </Box>
          </Radio>
        </Box>

        <Box
          _hover={{
            cursor: 'pointer',
            backgroundColor: 'blue.100',
            color: 'blue.primary',
            fontWeight: '500',
          }}
          px={'0.8rem'}
          borderRadius={'10px'}
        >
          <Radio size="sm" value="2">
            <Box as="span" fontSize={'lg'} fontWeight={'400'}>
              {option2}
            </Box>
          </Radio>
        </Box>
      </RadioGroup>
    </Flex>
  );
}
