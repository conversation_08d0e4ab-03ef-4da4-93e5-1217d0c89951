import React from 'react';
import { Text } from '@chakra-ui/react';

export default function FormTitle({
  title,
  description,
}: {
  title: string;
  description: string | string[];
}) {
  return (
    <>
      <Text variant={'header'}>{title}</Text>

      {Array.isArray(description) ? (
        description.map((text: string, index: number) => (
          <Text lineHeight={'.3rem'} key={index} variant={'subHeader'}>
            {text}
          </Text>
        ))
      ) : (
        <Text lineHeight={'.3rem'} variant={'subHeader'}>
          {description}
        </Text>
      )}

      {/* <Text variant={'error'}>
            必須項目は必ずご入力ください。
        </Text> */}
    </>
  );
}
