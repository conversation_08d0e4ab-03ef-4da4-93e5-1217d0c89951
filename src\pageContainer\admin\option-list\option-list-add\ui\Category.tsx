import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Text } from '@chakra-ui/react';
import PenIcon from '@/assets/icons/pen';
import TrashIcon from '@/assets/icons/trash';
import { useTempCatStore } from '@/store/temp/admin/tempCatTagStore';
import { CategoryType } from '@/types/catTagTypes';
import { useState } from 'react';
import { Input } from '@chakra-ui/react';
import { CheckIcon } from '@chakra-ui/icons';

type CategoryProps = {
  category: CategoryType;
  // handleEditCategory: (category: CategoryType) => void;
};

export default function Category({ category }: CategoryProps) {
  const [editMode, setEditMode] = useState(false);
  const [newcategoryName, setNewCategory] = useState('');
  const deleteCategory = useTempCatStore(state => state.deleteCategory);
  const editCategory = useTempCatStore(state => state.editCategory);

  return (
    <Flex alignItems={'center'} height={'1rem'} justifyContent={'space-between'}>
      {editMode ? (
        <Input
          border={'none'}
          borderRadius={'none'}
          borderColor={'gray.100'}
          borderBottom={'1px solid'}
          p={0}
          m={0}
          h={'1.3rem'}
          fontSize={'sm'}
          fontWeight={'bold'}
          value={newcategoryName}
          name="category"
          onChange={e => setNewCategory(e.target.value)}
          placeholder={category.name}
          letterSpacing={'-.04em'}
          lineHeight={'1rem'}
        />
      ) : (
        <Text w={'100%'} letterSpacing={'-.04em'} lineHeight={'1rem'} fontWeight={'bold'}>
          {category.name}
        </Text>
      )}

      <Flex>
        <Button
          variant={'ghost'}
          h={'auto'}
          fontSize={'sm'}
          fontWeight={'normal'}
          m={0}
          p={0}
          w={'auto'}
          display={'flex'}
          gap={4}
          _hover={{ bg: 'transparent' }}
          onClick={() => {
            if (editMode) {
              editCategory({ ...category, name: newcategoryName });
              setNewCategory('');
              setEditMode(!editMode);
            }
            if (!editMode) {
              setNewCategory(category.name);
              setEditMode(!editMode);
            }
          }}
          disabled={editMode && newcategoryName === ''}
        >
          {editMode ? (
            <CheckIcon color="black" height={15} width={15} />
          ) : (
            <PenIcon color="black" height={15} width={15} />
          )}
        </Button>
        <Button
          variant={'ghost'}
          h={'auto'}
          fontSize={'sm'}
          fontWeight={'normal'}
          m={0}
          p={0}
          w={'auto'}
          display={'flex'}
          gap={4}
          onClick={() => deleteCategory(category.id)}
        >
          <TrashIcon color="black" height={20} width={20} />
        </Button>
      </Flex>
    </Flex>
  );
}
