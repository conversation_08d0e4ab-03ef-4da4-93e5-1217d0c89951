import { <PERSON><PERSON>ventHandler } from 'react';

type goToPreviousProps = {
  currentDate: Date;
  view: string;
  Views: any;
  setCurrentDate: (date: Date) => void;
};

type goToNextProps = {
  currentDate: Date;
  view: string;
  Views: any;
  setCurrentDate: (date: Date) => void;
};

export const goToPrevious = ({ currentDate, view, Views, setCurrentDate }: goToPreviousProps) => {
  const newDate = new Date(currentDate);
  if (view === Views.DAY) {
    newDate.setDate(newDate.getDate() - 1);
  } else if (view === Views.WEEK) {
    newDate.setDate(newDate.getDate() - 7);
  } else {
    newDate.setMonth(newDate.getMonth() - 1);
  }
  setCurrentDate(newDate);
};

export const goToNext = ({ currentDate, view, Views, setCurrentDate }: goToNextProps) => {
  const newDate = new Date(currentDate);
  if (view === Views.DAY) {
    newDate.setDate(newDate.getDate() + 1);
  } else if (view === Views.WEEK) {
    newDate.setDate(newDate.getDate() + 7);
  } else {
    newDate.setMonth(newDate.getMonth() + 1);
  }
  setCurrentDate(newDate);
};
