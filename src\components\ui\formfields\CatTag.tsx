import React from 'react';
import { Flex, SelectField } from '@chakra-ui/react';

type CatTagProps = {
  label?: string;
  categoryOptions?: string[];
  tagOptions?: string[];
  onChange?: (
    e: React.ChangeEvent<HTMLSelectElement | HTMLInputElement | HTMLTextAreaElement>
  ) => void;
  onTagsChange?: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  select1Name?: string;
  select2Name?: string;
};

export default function CatTag({
  label,
  categoryOptions,
  tagOptions,
  onChange,
  onTagsChange,
  select1Name,
  select2Name,
}: CatTagProps) {
  return (
    <Flex gap={3}>
      <SelectField
        border={'1px solid #E5EAF1'}
        borderRadius={'10px'}
        placeholder="Category"
        px={3}
        py={1}
        w={'10rem'}
        name={select1Name}
        onChange={e => {
          onChange?.(e);
        }}
      >
        {categoryOptions?.map(option => (
          <option key={option} value={option}>
            {option}
          </option>
        ))}
      </SelectField>
      <SelectField
        border={'1px solid #E5EAF1'}
        borderRadius={'10px'}
        placeholder="Tag"
        px={3}
        py={1}
        w={'10rem'}
        name={select2Name}
        onChange={e => {
          onTagsChange?.(e);
        }}
      >
        {tagOptions?.map(option => (
          <option key={option} value={option}>
            {option}
          </option>
        ))}
      </SelectField>
    </Flex>
  );
}
