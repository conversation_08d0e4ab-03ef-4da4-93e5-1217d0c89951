import CardContainer from '@/components/ui/cards/card-container';
import { Button, Flex, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Router from 'next/router';

function CheckCreditCharge() {
  const { t } = useTranslation('common');
  return (
    <Flex flexDirection={'column'} gap={'1.5rem'}>
      <Text variant={'header'}>{t('creditCharge.title')}</Text>

      <CardContainer>
        <Flex
          alignItems={'center'}
          justifyContent={'center'}
          borderRadius={'.5rem'}
          p={'.5rem'}
          w={'100%'}
          gap={'1rem'}
          boxShadow={'0px 0px 10px 0px rgba(0, 0, 0, 0.10)'}
        >
          <Text color={'mainColor'} fontSize={'2xl'} fontWeight={'bold'}>
            1,000
          </Text>
          <Text>{t('creditCharge.creditUnit')}</Text>
        </Flex>
        <Flex gap={'1rem'}>
          <Button
            variant={'rounded'}
            fontSize={'md'}
            borderColor={'black'}
            onClick={() => {
              Router.back();
            }}
          >
            {t('buttons.back')}
          </Button>
          <Button variant={'roundedBlue'} fontSize={'md'}>
            {t('creditCharge.buttons.charge')}
          </Button>
        </Flex>
      </CardContainer>
    </Flex>
  );
}

export async function getServerSideProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'auth'])),
      layoutType: 'client',
    },
  };
}

export default CheckCreditCharge;
