import { ReservationEvent } from '@/types/ReservationEventType';
import { RoomType } from '@/types/RoomType';
import { getRoomColor } from '@/utils/getRoomColor';

type getEventStyleGetterProps = {
  event: ReservationEvent;
  rooms: RoomType[];
};

export const eventStyleGetter = ({ event, rooms }: getEventStyleGetterProps) => {
  const backgroundColor = getRoomColor({ roomName: event.room, rooms });
  const roomClass = event.room.toLowerCase().replace(' ', '-');
  const statusClass = `status-${event.status}`;
  const style = {
    backgroundColor,
    borderRadius: '6px',
    opacity: event.status === 'cancelled' ? 0.6 : 1,
    color: 'white',
    border: '0px',
    display: 'block',
    fontSize: '11px',
    fontWeight: 'bold',
    padding: '2px 4px',
  };
  return {
    style,
    className: `${roomClass} ${statusClass}`,
  };
};
