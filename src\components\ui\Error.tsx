import React from 'react';
import { Flex, Text, Image, Box } from '@chakra-ui/react';
// import { useTranslation } from 'next-i18next';

interface ErrorProps {
  err?: string;
}

export const Error = ({ err }: ErrorProps) => {
  //   const { t } = useTranslation('auth');
  return (
    <Flex
      display={err ? 'flex' : 'none'}
      borderRadius={'5px'}
      alignItems={'center'}
      gap={2}
      p={4}
      w={'100%'}
      bg={'red.100'}
    >
      <Box>
        <Image src="/imgs/icons/error.png" alt="Error Icon" width={5} height={5} />
      </Box>
      <Text textStyle={'xs'} textAlign={'start'} color="red.600">
        {err}
      </Text>
    </Flex>
  );
};
