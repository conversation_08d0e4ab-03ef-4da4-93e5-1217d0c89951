import Assignment from '@/assets/icons/assignment';
import BellIcon from '@/assets/icons/bell';
import BurgerIcon from '@/assets/icons/burger';
import GroupIcon from '@/assets/icons/group';
import OptionsListIcon from '@/assets/icons/options-list';
import StoreIcon from '@/assets/icons/store';
import StoreOptionIcon from '@/assets/icons/storeOption';
import { mockStores, Store } from '@/types/storeTypes';
import { ChevronDownIcon } from '@chakra-ui/icons';
import { Button, Flex, Image, Menu, MenuButton, MenuItem, MenuList, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import LanguageSwitcher from '../ui/language-switcher';
import Proto from '../ui/proto';

type SideBarProps = {
  navConfig: {
    navItems: Array<{
      id: string;
      labelKey: string;
      icon: string;
      path: string;
    }>;
  };
  activePage: string;
  setActivePage: (page: string) => void;
};

function SideBar({ navConfig, activePage, setActivePage }: SideBarProps) {
  const { t } = useTranslation('admin');
  const [isMounted, setIsMounted] = useState(false);
  const [isMobileNavVisible, setIsMobileNavVisible] = useState(false);
  const [stores] = useState<Store[]>(mockStores);
  const [currentStore, setCurrentStore] = useState<Store>(mockStores[0]);
  const router = useRouter();

  // Fix hydration issues by only rendering client-side components after mount
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Set active page based on current route
  useEffect(() => {
    const currentPath = router.pathname;
    const matchingNavItem = navConfig.navItems.find(item => item.path === currentPath);

    if (matchingNavItem && matchingNavItem.id !== activePage) {
      setActivePage(matchingNavItem.id);
    }
  }, [router.pathname, navConfig.navItems, activePage, setActivePage]);

  const handleNavClick = (page: string, path: string) => {
    console.log('handleNavClick', page, path);
    setActivePage(page);

    // Close mobile menu when navigation item is clicked
    if (window.innerWidth < 992) {
      setIsMobileNavVisible(false);
    }

    router.push(path);
  };

  const toggleMobileNav = () => {
    setIsMobileNavVisible(prev => !prev);
  };

  const isActive = (page: string) => activePage === page;

  // Show a simplified version during server-side rendering
  if (!isMounted) {
    return (
      <Flex
        bg={'white'}
        p={4}
        minH={'100dvh'}
        maxW={'16rem'}
        flexDirection={'column'}
        justifyContent={'space-between'}
      >
        <Flex flexDirection={'column'} width="250px">
          <Text fontWeight={700} variant={'brand'} mb={4}>
            Justyoyaku
          </Text>
          <nav>{/* Simple placeholder during SSR */}</nav>
        </Flex>
      </Flex>
    );
  }

  return (
    <Flex
      position={'fixed'}
      top={0}
      left={0}
      zIndex={'1000'}
      // bottom={0}
      bg={'white'}
      p={4}
      pb={0}
      pt={{ base: '0', lg: 4 }}
      minH={{ base: '5rem', lg: '100dvh' }}
      w={{ base: '100%', lg: '16rem' }}
      flexDirection={{ base: 'row', lg: 'column' }}
      justifyContent={'space-between'}
    >
      <Flex flexDirection={'column'} justifyContent={{ base: 'center', lg: 'flex-start' }}>
        <Flex
          display={'flex'}
          flexDirection={'row'}
          mb={{ base: '0', lg: '2.5rem' }}
          mt={{ base: '0', lg: '.5rem' }}
          alignItems={'center'}
        >
          <Flex display={{ base: 'flex', lg: 'none' }}>
            <Button
              variant={'ghost'}
              p={0}
              h={'auto'}
              fontSize={'sm'}
              fontWeight={'normal'}
              m={0}
              w={'auto'}
              display={'flex'}
              gap={4}
              _hover={{ bg: 'transparent' }}
              onClick={toggleMobileNav}
            >
              <BurgerIcon width={25} height={25} color="blue" />
            </Button>
          </Flex>
          <Text fontSize={{ base: '1.5rem', lg: '3xl' }} variant={'brand'}>
            Justyoyaku
          </Text>
        </Flex>

        {/* Language Switcher - Desktop only */}
        <Flex display={{ base: 'none', lg: 'flex' }} mb={4} justifyContent="flex-start">
          <LanguageSwitcher />
        </Flex>

        <Flex
          as="nav"
          bg={'white'}
          position={{ base: 'fixed', lg: 'relative' }}
          zIndex={'100'}
          left={{ base: '0', lg: 'auto' }}
          right={{ base: '0', lg: 'auto' }}
          top={{ base: '5rem', lg: '0' }}
          display={{ base: isMobileNavVisible ? 'flex' : 'none', lg: 'flex' }}
          flexDirection={{ base: 'column-reverse', lg: 'column' }}
          gap={4}
          px={{ base: '1rem', lg: 0 }}
        >
          {/* Language Switcher - Mobile only */}
          <Flex display={{ base: 'flex', lg: 'none' }} mb={4} justifyContent="flex-start">
            <LanguageSwitcher />
          </Flex>

          <Menu>
            <MenuButton
              as={Button}
              rightIcon={<ChevronDownIcon />}
              fontSize={'sm'}
              border={'1px solid #E5EAF1'}
              px={2}
              py={6}
              mb={2}
              bg="white"
              width="100%"
              maxWidth="14rem"
              textAlign="left"
              justifyContent="space-between"
              overflow="hidden"
            >
              <Flex alignItems={'center'} minWidth={0} flex={1}>
                <Image
                  src={currentStore.imageUrl || '/imgs/icons/tempProfile.png'}
                  alt=""
                  width={6}
                  height={6}
                  mr={2}
                  flexShrink={0}
                />{' '}
                <Text
                  fontWeight={400}
                  overflow="hidden"
                  textOverflow="ellipsis"
                  whiteSpace="nowrap"
                  flex={1}
                  title={currentStore.name}
                >
                  {currentStore.name}
                </Text>
              </Flex>
            </MenuButton>
            <MenuList>
              {stores.map(store => (
                <MenuItem
                  key={store.id}
                  fontWeight={'300'}
                  onClick={() => setCurrentStore(store)}
                  overflow="hidden"
                  textOverflow="ellipsis"
                  whiteSpace="nowrap"
                  title={store.name}
                  maxWidth="100%"
                >
                  {store.name}
                </MenuItem>
              ))}
              <MenuItem
                onClick={() => {
                  router.push('/admin/add-store');
                }}
              >
                <Flex justifyContent="space-between" width="100%" alignItems="center">
                  <Flex
                    as="span"
                    border={'1px solid black'}
                    borderRadius={'md'}
                    px={3}
                    py={1}
                    ml={2}
                    fontWeight={'300'}
                    fontSize={'md'}
                    cursor="pointer"
                  >
                    {t('sidebar.storeManagement.addStore')}
                  </Flex>
                </Flex>
              </MenuItem>
            </MenuList>
          </Menu>

          <Flex flexDirection="column" mt={4}>
            {navConfig.navItems.map(item => (
              <Button
                py={2}
                px={3}
                mb={2}
                w={{ base: '100%', lg: '14rem' }}
                alignItems="center"
                justifyContent={'flex-start'}
                borderRadius="50rem"
                bg={isActive(item.id) ? 'gray.100' : 'white'}
                _hover={{ bg: 'gray.100', cursor: 'pointer' }}
                key={item.id}
                onClick={() => handleNavClick(item.id, item.path)}
              >
                <Flex w={'1.8rem'}>
                  {item.icon === 'assignment' ? (
                    <Assignment
                      width={25}
                      height={25}
                      color={isActive(item.id) ? '#2B42CA' : '#1E293B'}
                    />
                  ) : null}
                  {item.icon === 'membersList' ? (
                    <GroupIcon
                      width={25}
                      height={25}
                      color={isActive(item.id) ? '#2B42CA' : '#1E293B'}
                    />
                  ) : null}
                  {item.icon === 'newsList' ? (
                    <BellIcon
                      width={25}
                      height={25}
                      color={isActive(item.id) ? '#2B42CA' : '#1E293B'}
                    />
                  ) : null}
                  {item.icon === 'optionsList' ? (
                    <OptionsListIcon
                      width={25}
                      height={25}
                      color={isActive(item.id) ? '#2B42CA' : '#1E293B'}
                    />
                  ) : null}
                  {item.icon === 'storeDetails' ? (
                    <StoreIcon
                      width={25}
                      height={25}
                      color={isActive(item.id) ? '#2B42CA' : '#1E293B'}
                    />
                  ) : null}
                  {item.icon === 'storeOption' ? (
                    <StoreOptionIcon
                      width={25}
                      height={25}
                      color={isActive(item.id) ? '#2B42CA' : '#1E293B'}
                    />
                  ) : null}
                </Flex>
                <Text
                  fontSize="sm"
                  fontWeight={isActive(item.id) ? 800 : 400}
                  color={isActive(item.id) ? 'blue.primary' : 'black'}
                >
                  {t(item.labelKey)}
                </Text>
              </Button>
            ))}
          </Flex>
        </Flex>
      </Flex>

      <Flex flexDirection={'column'} justifyContent={{ base: 'center', lg: 'flex-start' }}>
        <Flex alignItems={'center'}>
          <Flex
            h={{ base: 'auto', lg: '3rem' }}
            w={'3rem'}
            mr={3}
            alignItems={'center'}
            justifyContent={'center'}
          >
            <Image
              borderRadius={'50px'}
              src="/imgs/icons/profileIcon.svg"
              alt=""
              width={10}
              height={10}
            />
          </Flex>
          <Menu placement="top" strategy="fixed">
            <MenuButton
              display={{ base: 'none', sm: 'block' }}
              as={Button}
              variant="ghost"
              p={0}
              // height="auto"
              w={'70%'}
              textAlign={'left'}
              _hover={{ bg: 'transparent' }}
            >
              <Text fontSize="md" fontWeight={500}>
                {t('sidebar.userMenu.username')}
              </Text>
            </MenuButton>
            <MenuList ml={'-3.8rem'} minW="13rem">
              <MenuItem
                fontSize="sm"
                onClick={() => {
                  router.push('/admin/mypage/123');
                }}
              >
                {t('sidebar.userMenu.myPage')}
              </MenuItem>
              <MenuItem
                fontSize="sm"
                onClick={() => {
                  router.push('/login');
                }}
              >
                {t('sidebar.userMenu.logout')}
              </MenuItem>
            </MenuList>
          </Menu>
        </Flex>
        <Flex display={{ base: 'none', lg: 'flex' }}>
          <Proto />
        </Flex>
      </Flex>
    </Flex>
  );
}

export default SideBar;
