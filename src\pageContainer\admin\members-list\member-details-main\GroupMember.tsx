import React from 'react'
import { But<PERSON>, <PERSON>lex, Text } from '@chakra-ui/react'
import PenIcon from '@/assets/icons/pen';
import TrashIcon from '@/assets/icons/trash';

export default function GroupMember({ editMode, memberName }: { editMode: boolean ; memberName: string }) {
  return (
    <Flex alignItems={'center'}>
        <Text fontSize={'sm'} color={'black600'} mr={'1rem'}>
            {memberName}
        </Text>
        {editMode && (
            <>
            <Button
                variant={'ghost'}
                fontSize={'xs'}
                fontWeight={'normal'}
                p={0}
                m={0}
                gap={2}
                h={'auto'}
                w={'15px'}
                _hover={{ bg: 'transparent' }}
            >
                <PenIcon width={15} height={15} color="black" className="my-icon-class" />
            </Button>
            <Button
                variant={'ghost'}
                fontSize={'xs'}
                fontWeight={'normal'}
                p={0}
                m={0}
                gap={2}
                h={'auto'}
                w={'15px'}
                _hover={{ bg: 'transparent' }}
            >
                <TrashIcon width={20} height={20} color="black" className="my-icon-class" />
            </Button>
            </>
        )}
    </Flex>
  )
}
