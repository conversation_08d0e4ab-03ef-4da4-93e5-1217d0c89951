import { <PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>lay, <PERSON>dal<PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>er, ModalCloseButton, ModalBody, Modal<PERSON>ooter, <PERSON><PERSON>, <PERSON> } from '@chakra-ui/react'
import React from 'react'
import InputField from '../ui/formfields/InputField';
import DropDown from '../ui/formfields/DropDown';
import DateSelect from '../ui/formfields/DateSelect';
import { AdminReservationType } from '@/types/adminReservationsTypes';

export default function AdminReservationModal({
    isOpen,
    onClose,
    selectedReservation
}: {
    isOpen: boolean;
    onClose: () => void;
    selectedReservation: AdminReservationType | null;
}) {
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
            <ModalHeader textAlign={'center'}>絞り込み</ModalHeader>
            <ModalCloseButton />
            <ModalBody display={'flex'} flexDirection={'column'} gap={'1rem'}>
                <DateSelect 
                label={'予約日時'} 
                // onChange={handleDateRangeChange}
                initialStartDate={selectedReservation?.dateTime || ''}
                initialEndDate="2025-03-01"
                />
                {/* <Text>予約詳細</Text> */}
                <InputField label={'予約日時'} type={'text'} placeholder={'placeholder'} value={selectedReservation?.user} />

                <DropDown 
                label={selectedReservation?.user || 'オプション'}
                placeholder={'ステータス'} 
                options={['ステータス1', 'ステータス2', 'ステータス3']} />
                <DropDown 
                label={'ステータス'} 
                placeholder={'連絡先'} 
                options={['連絡先1', '連絡先2', '連絡先3']} />

            </ModalBody>
            <ModalFooter justifyContent={'center'}>
                <Button w={'auto'} variant={'roundedBlue'} px={'3rem'} onClick={onClose}>
                表示する
                </Button>
            </ModalFooter>
        </ModalContent>
    </Modal>
  )
}
