import React from 'react';
import { Button, Flex, Input, Text, HStack, InputGroup, InputLeftElement } from '@chakra-ui/react';
import { SearchIcon } from '@chakra-ui/icons';
import Router from 'next/router';
import ReactBigCalendarComponent from '@/components/ui/calendar/react-big-calendar-component';

export default function ReservationsCalendar() {
  return (
    <Flex w="100%" flexDirection="column" pt="1rem" pr="2rem" gap="1rem">
      <Text variant="subHeaderXL">予約カレンダー (React Big Calendar)</Text>
      <Flex flexDirection="column" bg="white" borderRadius="1rem" gap="1rem" p="2rem">
        <Flex justifyContent="flex-end">
          <Flex gap="1rem">
            <Flex justifyContent="flex-end" my={-4}>
              <HStack spacing={4}>
                <InputGroup maxW={{ base: '8rem', md: '8rem' }}>
                  <InputLeftElement
                    pointerEvents="none"
                    height="100%"
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                  >
                    <SearchIcon color="gray" />
                  </InputLeftElement>
                  <Input
                    placeholder="絞り込み"
                    py={2}
                    px={0}
                    variant="rounded"
                    border="1px solid gray"
                  />
                </InputGroup>
              </HStack>
            </Flex>
            <Button
              variant="roundedBlue"
              fontSize="sm"
              fontWeight="normal"
              py="1.25rem"
              w="auto"
              onClick={() => {
                Router.push('/admin/reservations-list');
              }}
            >
              リストで表示
            </Button>
          </Flex>
        </Flex>
        <ReactBigCalendarComponent />
      </Flex>
    </Flex>
  );
}

ReservationsCalendar.getInitialProps = async () => {
  return { layoutType: 'withSidebar' };
};
