import React from 'react';

interface AssignmentIconProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

const AssignmentIcon: React.FC<AssignmentIconProps> = ({
  width = 18,
  height = 18,
  color = '#1E293B',
  className,
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask
        id="mask0_4001_17925"
        style={{ maskType: 'alpha' }}
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="24"
        height="24"
      >
        <rect width="24" height="24" fill={color} />
      </mask>
      <g mask="url(#mask0_4001_17925)">
        <path
          d="M10.6 13.25L8.45 11.1C8.26667 10.9167 8.03333 10.825 7.75 10.825C7.46667 10.825 7.23333 10.9167 7.05 11.1C6.86667 11.2833 6.775 11.5167 6.775 11.8C6.775 12.0833 6.86667 12.3167 7.05 12.5L9.9 15.35C10.1 15.55 10.3333 15.65 10.6 15.65C10.8667 15.65 11.1 15.55 11.3 15.35L16.95 9.7C17.1333 9.51667 17.225 9.28333 17.225 9C17.225 8.71667 17.1333 8.48333 16.95 8.3C16.7667 8.11667 16.5333 8.025 16.25 8.025C15.9667 8.025 15.7333 8.11667 15.55 8.3L10.6 13.25ZM5 21C4.45 21 3.97917 20.8042 3.5875 20.4125C3.19583 20.0208 3 19.55 3 19V5C3 4.45 3.19583 3.97917 3.5875 3.5875C3.97917 3.19583 4.45 3 5 3H9.2C9.41667 2.4 9.77917 1.91667 10.2875 1.55C10.7958 1.18333 11.3667 1 12 1C12.6333 1 13.2042 1.18333 13.7125 1.55C14.2208 1.91667 14.5833 2.4 14.8 3H19C19.55 3 20.0208 3.19583 20.4125 3.5875C20.8042 3.97917 21 4.45 21 5V19C21 19.55 20.8042 20.0208 20.4125 20.4125C20.0208 20.8042 19.55 21 19 21H5ZM5 19H19V5H5V19ZM12 4.25C12.2167 4.25 12.3958 4.17917 12.5375 4.0375C12.6792 3.89583 12.75 3.71667 12.75 3.5C12.75 3.28333 12.6792 3.10417 12.5375 2.9625C12.3958 2.82083 12.2167 2.75 12 2.75C11.7833 2.75 11.6042 2.82083 11.4625 2.9625C11.3208 3.10417 11.25 3.28333 11.25 3.5C11.25 3.71667 11.3208 3.89583 11.4625 4.0375C11.6042 4.17917 11.7833 4.25 12 4.25Z"
          fill={color}
        />
      </g>
    </svg>
  );
};

export default AssignmentIcon;
