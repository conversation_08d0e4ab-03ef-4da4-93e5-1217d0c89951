import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  ModalCloseButton,
  ModalBody,
  Button,
  Text,
  Flex,
  Input,
  Grid,
} from '@chakra-ui/react';
import Category from './ui/Category';
import Tag from './ui/Tag';
import { useTempCatStore, useTempTagStore } from '@/store/temp/admin/tempCatTagStore';
import { CategoryType, TagType } from '@/types/catTagTypes';

type CatTagModalProps = {
  isOpen: boolean;
  onClose: () => void;
};

export default function CatTagModal({ isOpen, onClose }: CatTagModalProps) {
  const categories = useTempCatStore(state => state.categories);
  const addCategory = useTempCatStore(state => state.addCategory);

  const addTag = useTempTagStore(state => state.addTag);
  const tags = useTempTagStore(state => state.tags);

  const [category, setCategory] = useState('');
  const [tag, setTag] = useState('');

  const handleAddCategory = () => {
    addCategory({ id: categories.length + 1, name: category, description: '' });
    setCategory('');
    console.log(categories);
  };

  // const handleEditCategory = (category: CategoryType) => {
  //   setCategory(category.name);
  // };

  const handleAddTag = () => {
    addTag({ id: tags.length + 1, name: tag, description: '' });
    setTag('');
    console.log(tag);
  };

  const handleEditTag = (tag: TagType) => {
    setTag(tag.name);
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent>
        <Flex justifyContent={'center'} alignItems={'center'} pt={'3rem'}>
          <Text variant={'subHeaderNormal'}>カテゴリー・タグ管理</Text>
        </Flex>
        <ModalCloseButton />
        <ModalBody fontSize={'sm'} display={'flex'} flexDirection={'column'} gap={'.5rem'}>
          <Text variant={'subHeader'}>カテゴリー</Text>

          {categories.map(category => (
            <Category
              key={category.id}
              category={category}
              // handleEditCategory={handleEditCategory}
            />
          ))}

          <Flex position={'relative'}>
            <Input
              border={'1px solid black'}
              borderRadius={'10px'}
              px={3}
              py={1}
              value={category}
              name="category"
              onChange={e => setCategory(e.target.value)}
              placeholder="カテゴリー追加"
            />
            <Button
              position={'absolute'}
              right={0}
              zIndex={10}
              variant={'ghost'}
              _hover={{ bg: 'transparent' }}
              onClick={handleAddCategory}
            >
              +
            </Button>
          </Flex>

          <Text variant={'subHeader'}>タグ</Text>

          <Grid templateColumns="repeat(2, 1fr)" gap={'.5rem'}>
            {tags.map(tag => (
              <Tag key={tag.id} tag={tag} />
            ))}
          </Grid>

          <Flex mt={'1rem'} position={'relative'}>
            <Input
              border={'1px solid black'}
              // backgroundColor={'blue.100'}
              borderRadius={'10px'}
              px={3}
              py={1}
              placeholder="タグ追加"
              name="tag"
              value={tag}
              onChange={e => setTag(e.target.value)}
            />
            <Button
              position={'absolute'}
              right={0}
              zIndex={10}
              variant={'ghost'}
              _hover={{ bg: 'transparent' }}
              onClick={handleAddTag}
            >
              +
            </Button>
          </Flex>
        </ModalBody>
        <ModalFooter gap={4} justifyContent={'center'}>
          <Button
            px={'3rem'}
            py={'.7rem'}
            mt={0}
            w={'auto'}
            variant="roundedBlue"
            onClick={onClose}
          >
            完了
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
