import { GoogleButton } from '@/components/ui/buttons/GoogleButton';
import FormTitle from '@/components/ui/formfields/FormTitle';
import ImageInputRounded from '@/components/ui/formfields/ImageInputRounded';
import InputField from '@/components/ui/formfields/InputField';
import ToggleTimeSelect from '@/components/ui/ToggleTimeSelect';
import useToggleTimeSelect from '@/hooks/useToggleTimeSelect';
import { Box, Button, Flex, Stack, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { useState } from 'react';
import Router from 'next/router';
import { Error } from '@/components/ui/Error';

export default function UserInfoFormField() {
  const { t } = useTranslation('admin');
  const [formData, setFormData] = useToggleTimeSelect();

  const [error, setError] = useState<string>('');

  const [activeStep, setActiveStep] = useState(0);
  const steps = [
    {
      title: t('enterUserInformation.steps.userInfo.title'),
      description: [t('enterUserInformation.steps.userInfo.description')],
    },
    {
      title: t('enterUserInformation.steps.storeInfo.title'),
      description: [t('enterUserInformation.steps.storeInfo.description')],
    },
    {
      title: t('enterUserInformation.steps.externalCalendar.title'),
      description: [t('enterUserInformation.steps.externalCalendar.description')],
    },
    {
      title: t('enterUserInformation.steps.businessHours.title'),
      description: [t('enterUserInformation.steps.businessHours.description')],
    },
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleNext = () => {
    if ((activeStep === 0 && !formData.username) || formData.username === 'admin') {
      !formData.username ? setError('errors.invalidCredentials') : setError('errors.URLInUse');

      return;
    } else {
      setError('');
    }
    if (
      (activeStep === 1 && !formData.storeName) ||
      !formData.storeID ||
      formData.storeID === 'admin'
    ) {
      !formData.storeName ? setError('errors.invalidCredentials') : setError('errors.URLInUse');
      return;
    } else {
      setError('');
    }

    if (activeStep < steps.length - 1) setActiveStep(activeStep + 1);
  };

  const handleBack = () => {
    setActiveStep(activeStep - 1);
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    if (activeStep < 3) {
      handleNext();
      return;
    }
    Router.push('/admin/reservations-list');
  };

  return (
    <Flex w={{ base: '90dvw', sm: '30rem' }} justify={'center'} gap="4" direction="column">
      {steps.map(
        (step, index) =>
          index === activeStep && (
            <FormTitle key={index} title={step.title} description={step.description} />
          )
      )}
      <Error err={error ? t(error) : ''} />
      <Box>
        <Flex justify="space-between">
          <Text color="gray.500">
            {t('addStore.modal.stepIndicator', { current: activeStep + 1, total: steps.length })}
          </Text>
        </Flex>

        <Flex w="100%" gap={2} mb={2}>
          {steps.map((_, index) => (
            <Box
              key={index}
              h="5px"
              flex="1"
              bg={index <= activeStep ? 'mainColor' : 'gray.200'}
              borderRadius="full"
            />
          ))}
        </Flex>
      </Box>
      <Box bg={'white'} borderRadius={'20px'} p="6" pb={0}>
        <form onSubmit={handleSubmit}>
          {activeStep === 0 && (
            <Stack gap="4" align="flex-start">
              <ImageInputRounded
                label={t('enterUserInformation.form.profileIcon')}
                // errors={error}
                secondLabel={t('enterUserInformation.form.changePhoto')}
                // onChange={handleInputChange}
                value={formData.profileIcon}
              />

              <InputField
                label={t('enterUserInformation.form.username')}
                name={'username'}
                value={formData.username}
                onChange={handleInputChange}
                required
              />

              <InputField
                label={t('enterUserInformation.form.groupName')}
                name={'groupname'}
                variant="textArea"
                value={formData.groupname}
                onChange={handleInputChange}
                errors={error}
              />
            </Stack>
          )}
          {activeStep === 1 && (
            <Stack gap="4" align="flex-start">
              <ImageInputRounded
                label={t('addStore.modal.form.storeIcon')}
                secondLabel={t('enterUserInformation.form.changePhoto')}
                // onChange={handleInputChange}
                value={formData.profileIcon}
              />

              <InputField
                label={t('addStore.modal.form.storeName')}
                name={'storeName'}
                placeholder={t('addStore.modal.form.storeNamePlaceholder')}
                value={formData.storeName}
                onChange={handleInputChange}
                required
              />

              <InputField
                variant={'refixed'}
                label={t('addStore.modal.form.storeId')}
                name={'storeID'}
                placeholder={t('addStore.modal.form.storeIdPlaceholder')}
                value={formData.storeID}
                onChange={handleInputChange}
                required
                // errors={error}
                helperText={t('addStore.modal.form.storeIdHelper')}
              />

              {activeStep === 1 && error === 'errors.URLInUse' && (
                <Error err={error ? t(error) : ''} />
              )}

              <InputField
                label={t('addStore.modal.form.storeOverview')}
                name={'storeOverview'}
                variant="textArea"
                value={formData.storeOverview}
                onChange={handleInputChange}
                errors={error}
              />
            </Stack>
          )}

          {activeStep === 2 && <GoogleButton />}

          {activeStep === 3 && (
            <ToggleTimeSelect
              formData={formData}
              setFormData={setFormData}
              handleInputChange={handleInputChange}
              // addTimeSlot={addTimeSlot}
              // removeTimeSlot={removeTimeSlot}
            />
          )}

          <Flex mb={10} gap={'1rem'} pt={'1rem'} flexDirection={'column'}>
            {activeStep < 3 && (
              <Button py={'1.4rem'} onClick={handleNext} variant="roundedBlue">
                {t('addStore.modal.buttons.next')}
              </Button>
            )}
            {activeStep === 3 && (
              <Button py={'1.4rem'} type="submit" variant="roundedBlue">
                {t('addStore.modal.buttons.complete')}
              </Button>
            )}
            {activeStep > 0 && (
              <Button py={'1.4rem'} onClick={handleBack} variant="RoundedOutline">
                {t('addStore.modal.buttons.back')}
              </Button>
            )}
          </Flex>
        </form>
      </Box>
    </Flex>
  );
}
