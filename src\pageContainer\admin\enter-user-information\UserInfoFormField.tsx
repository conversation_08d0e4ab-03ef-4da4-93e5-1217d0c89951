import { GoogleButton } from '@/components/ui/buttons/GoogleButton';
import FormTitle from '@/components/ui/formfields/FormTitle';
import ImageInputRounded from '@/components/ui/formfields/ImageInputRounded';
import InputField from '@/components/ui/formfields/InputField';
import ToggleTimeSelect from '@/components/ui/ToggleTimeSelect';
import useToggleTimeSelect from '@/hooks/useToggleTimeSelect';
import { Box, Button, Flex, Stack, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { useState } from 'react';

export default function UserInfoFormField() {
  const { t } = useTranslation('admin');
  const [formData, setFormData] = useToggleTimeSelect();

  const [errors, setErrors] = useState<Record<string, string>>({});

  const [activeStep, setActiveStep] = useState(0);
  const steps = [
    {
      title: 'ユーザー設定',
      description: ['ユーザーに関する情報を設定します。'],
    },
    {
      title: '店舗情報',
      description: ['店舗に関する情報を設定します。'],
    },
    {
      title: '外部カレンダー接続',
      description: ['外部サービスのカレンダーと連携すると、スケジュールの同期ができます。'],
    },
    {
      title: '営業時間',
      description: [
        '予約可能な時間の範囲を設定します。',
        'この設定は後から変更・カスタマイズ可能です。',
      ],
    },
  ];

  const handleInputChange = (e: any) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleNext = () => {
    if (activeStep < steps.length - 1) setActiveStep(activeStep + 1);

    // if (validateStep({ step: activeStep, formData, setErrors })) {
    //   setActiveStep(activeStep + 1);

    // }
  };

  const handleBack = () => {
    setActiveStep(activeStep - 1);
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
    // Router.push('/admin/reservations-list');
  };

  return (
    <Flex w={{ base: '90dvw', sm: '30rem' }} justify={'center'} gap="4" direction="column">
      {steps.map(
        (step, index) =>
          index === activeStep && (
            <FormTitle key={index} title={step.title} description={step.description} />
          )
      )}

      <Box>
        <Flex justify="space-between">
          <Text color="gray.500">
            {t('addStore.modal.stepIndicator', { current: activeStep + 1, total: steps.length })}
          </Text>
        </Flex>

        <Flex w="100%" gap={2} mb={2}>
          {steps.map((_, index) => (
            <Box
              key={index}
              h="5px"
              flex="1"
              bg={index <= activeStep ? 'mainColor' : 'gray.200'}
              borderRadius="full"
            />
          ))}
        </Flex>
      </Box>

      <Box bg={'white'} borderRadius={'20px'} p="6" pb={0}>
        <form onSubmit={handleSubmit}>
          {/* {steps.map(
            (step, index) =>
              index === activeStep && (
                <FormTitle key={index} title={step.title} description={step.description} />
              )
          )} */}
          {activeStep === 0 && (
            <Stack gap="4" align="flex-start">
              <ImageInputRounded
                label={'プロフィールアイコン'}
                errors={errors.profileIcon}
                // onChange={handleInputChange}
                value={formData.profileIcon}
              />

              <InputField
                label={'ユーザー名'}
                name={'storeName'}
                value={formData.username}
                onChange={handleInputChange}
                required
              />

              <InputField
                label={'グループ名'}
                name={'storeOverview'}
                variant="textArea"
                value={formData.storeOverview}
                onChange={handleInputChange}
                errors={errors.storeOverview}
              />
            </Stack>
          )}
          {activeStep === 1 && (
            <Stack gap="4" align="flex-start">
              <ImageInputRounded
                label={t('addStore.modal.form.storeIcon')}
                errors={errors.profileIcon}
                // onChange={handleInputChange}
                value={formData.profileIcon}
              />

              <InputField
                label={t('addStore.modal.form.storeName')}
                name={'storeName'}
                placeholder={t('addStore.modal.form.storeNamePlaceholder')}
                value={formData.username}
                onChange={handleInputChange}
                required
              />

              <InputField
                variant={'refixed'}
                label={t('addStore.modal.form.storeId')}
                name={'storeID'}
                placeholder={t('addStore.modal.form.storeIdPlaceholder')}
                value={formData.storeID}
                onChange={handleInputChange}
                required
                errors={errors.storeID}
                helperText={t('addStore.modal.form.storeIdHelper')}
              />

              <InputField
                label={t('addStore.modal.form.storeOverview')}
                name={'storeOverview'}
                variant="textArea"
                value={formData.storeOverview}
                onChange={handleInputChange}
                errors={errors.storeOverview}
              />
            </Stack>
          )}

          {activeStep === 2 && <GoogleButton />}

          {activeStep === 3 && (
            <ToggleTimeSelect
              formData={formData}
              setFormData={setFormData}
              handleInputChange={handleInputChange}
              // addTimeSlot={addTimeSlot}
              // removeTimeSlot={removeTimeSlot}
            />
          )}

          <Flex mb={10} gap={'1rem'} pt={'1rem'} flexDirection={'column'}>
            {activeStep < 3 && (
              <Button py={'1.4rem'} onClick={handleNext} variant="roundedBlue">
                {t('addStore.modal.buttons.next')}
              </Button>
            )}
            {activeStep === 3 && (
              <Button py={'1.4rem'} type="submit" variant="roundedBlue">
                {t('addStore.modal.buttons.complete')}
              </Button>
            )}
            {activeStep > 0 && (
              <Button py={'1.4rem'} onClick={handleBack} variant="RoundedOutline">
                {t('addStore.modal.buttons.back')}
              </Button>
            )}
          </Flex>
        </form>
      </Box>
    </Flex>
  );
}
