import type { NextApiRequest, NextApiResponse } from 'next';
import { newsList, NewsList } from '../newsList';

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<NewsList | { message: string }>
) {
  if (req.method === 'GET') {
    const id = parseInt(req.query.id as string, 10);

    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid ID format' });
    }

    const newsItem = newsList.find(item => item.id === id);

    if (!newsItem) {
      return res.status(404).json({ message: `News item with ID ${id} not found` });
    }

    return res.status(200).json(newsItem);
  } else if (req.method === 'PUT') {
    const id = parseInt(req.query.id as string, 10);

    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid ID format' });
    }

    const { category, title, date, image, details, body } = req.body;

    const newsItemIndex = newsList.findIndex(item => item.id === id);

    if (newsItemIndex === -1) {
      return res.status(404).json({ message: `News item with ID ${id} not found` });
    }

    // Update the news item
    newsList[newsItemIndex] = {
      ...newsList[newsItemIndex],
      category: category !== undefined ? category : newsList[newsItemIndex].category,
      title: title || newsList[newsItemIndex].title,
      date: date || newsList[newsItemIndex].date,
      image: image || newsList[newsItemIndex].image,
      details: details || newsList[newsItemIndex].details,
      body: body || newsList[newsItemIndex].body,
    };

    return res.status(200).json(newsList[newsItemIndex]);
  } else if (req.method === 'DELETE') {
    const id = parseInt(req.query.id as string, 10);

    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid ID format' });
    }

    const newsItemIndex = newsList.findIndex(item => item.id === id);

    if (newsItemIndex === -1) {
      return res.status(404).json({ message: `News item with ID ${id} not found` });
    }

    // Remove the item from the array
    const deletedItem = newsList.splice(newsItemIndex, 1)[0];

    return res.status(200).json({ message: `News item with ID ${id} deleted successfully` });
  } else {
    return res.status(405).json({ message: 'Method not allowed' });
  }
}
