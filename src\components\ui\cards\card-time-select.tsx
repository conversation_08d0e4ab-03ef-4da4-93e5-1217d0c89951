import CheckCircleIcon from '@/assets/icons/check-circle';
import CircleIcon from '@/assets/icons/circle';
import { constants } from '@/constants/constants';
import { ChevronLeftIcon, ChevronRightIcon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  Flex,
  Input,
  Link,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalFooter,
  ModalHeader,
  ModalOverlay,
  SelectField,
  Table,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tr,
  useDisclosure,
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import React, { useEffect, useState } from 'react';

function CardTimeSelect() {
  const { t } = useTranslation('common');
  const [hours, setHours] = React.useState(constants.WORKING_HOURS);
  const [days, setDays] = React.useState(constants.DAY_NAMES_SUN_START);
  const [dates, setDates] = React.useState<string[]>([]);
  const [selectedCells, setSelectedCells] = React.useState<{
    [key: string]: boolean;
  }>({});
  const [currentMonth, setCurrentMonth] = React.useState(new Date());
  const [selectedTime, setSelectedTime] = React.useState<{
    day: string;
    date: string;
    hour: string;
    fullDate: Date;
    key: string;
  } | null>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [numberOfPeople, setNumberOfPeople] = React.useState<number | string>(1);
  const MAX_PEOPLE = constants.MAX_PEOPLE;

  const steps = [
    { title: t('usage'), description: '' },
    { title: t('reservationChangeComplete'), description: '' },
  ];

  const [activeStep, setActiveStep] = useState(0);

  const incrementHour = (hourString: string, increment: number) => {
    const [hourStr] = hourString.split(':');
    let hour = parseInt(hourStr, 10);
    hour = (hour + increment) % 24;
    return `${hour.toString().padStart(2, '0')}:00`;
  };

  useEffect(() => {
    const newDates: string[] = [];
    const newDayNames: string[] = [];
    const dayNames = [
      t('calendar.sundayShort'),
      t('calendar.mondayShort'),
      t('calendar.tuesdayShort'),
      t('calendar.wednesdayShort'),
      t('calendar.thursdayShort'),
      t('calendar.fridayShort'),
      t('calendar.saturdayShort'),
    ];
    const now = new Date(currentMonth);

    const dayOfWeek = now.getDay();
    const diff = now.getDate() - dayOfWeek;
    const startOfWeek = new Date(now);
    startOfWeek.setDate(diff);

    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);

      const month = date.getMonth() + 1;
      const day = date.getDate();
      newDates.push(`${month}/${day}`);

      newDayNames.push(dayNames[date.getDay()]);
    }

    setDates(newDates);
    setDays(newDayNames);
  }, [currentMonth, t]);

  const toggleCell = (day: string, hour: string, date: string, index: number) => {
    const key = `day-${index}-${hour}`;
    const now = new Date(currentMonth);
    const dayOfWeek = now.getDay();
    const diff = now.getDate() - dayOfWeek;
    const startOfWeek = new Date(now);
    startOfWeek.setDate(diff);

    const selectedDate = new Date(startOfWeek);
    selectedDate.setDate(startOfWeek.getDate() + index);

    const [hourStr, minuteStr] = hour.split(':');
    selectedDate.setHours(parseInt(hourStr, 10), parseInt(minuteStr || '0', 10));

    setSelectedTime({
      day,
      date,
      hour,
      fullDate: selectedDate,
      key,
    });

    onOpen();
  };

  const prevWeek = () => {
    const newDate = new Date(currentMonth);
    newDate.setDate(newDate.getDate() - 7);
    setCurrentMonth(newDate);
  };

  const nextWeek = () => {
    const newDate = new Date(currentMonth);
    newDate.setDate(newDate.getDate() + 7);
    setCurrentMonth(newDate);
  };

  const formatMonth = (date: Date) => {
    return t('formats.yearMonth', {
      year: date.getFullYear(),
      month: date.getMonth() + 1,
    });
  };

  const formatFullDate = (date: Date) => {
    return t('formats.yearMonthDay', {
      year: date.getFullYear(),
      month: date.getMonth() + 1,
      day: date.getDate(),
    });
  };

  const confirmReservation = () => {
    if (selectedTime) {
      setSelectedCells(prev => ({
        ...prev,
        [selectedTime.key]: !prev[selectedTime.key],
      }));
    }
    setActiveStep(0);
    onClose();
  };

  return (
    <>
      <Flex
        bg={'white'}
        flexDirection={'column'}
        p={{ base: '1rem', md: '2rem' }}
        borderRadius={'1rem'}
      >
        <Flex justifyContent={'space-between'} alignItems={'center'} mb={4}>
          <Text fontSize={'sm'} fontWeight={'bold'}>
            {t('calendar.dateSelection')}
          </Text>

          <Flex
            border={'1px solid'}
            p={'.2rem'}
            borderColor={'gray.200'}
            borderRadius={'5px'}
            gap={2}
          >
            <Button
              px={'1.5rem'}
              variant={'ghost'}
              fontWeight={'bold'}
              color={'mainColor'}
              bg={'blue.100'}
            >
              {t('navigation.weekView')}
            </Button>
            <Button px={'1.5rem'} variant={'ghost'} fontWeight={'bold'} color={'mainColor'}>
              {t('navigation.monthView')}
            </Button>
          </Flex>
        </Flex>

        <Flex justifyContent={'space-between'} alignItems={'center'} mb={4}>
          <Button variant="ghost" onClick={prevWeek} leftIcon={<ChevronLeftIcon />}>
            {t('navigation.previousWeek')}
          </Button>
          <Text fontWeight={'bold'}>{formatMonth(currentMonth)}</Text>
          <Button variant="ghost" onClick={nextWeek} rightIcon={<ChevronRightIcon />}>
            {t('navigation.nextWeek')}
          </Button>
        </Flex>

        <Table variant="simple" size="sm" cellSpacing="2px">
          <Thead>
            <Tr>
              <Th p={1}></Th>
              {days.map((day, dayIndex) => (
                <Th fontWeight={'normal'} key={`day-${dayIndex}`} textAlign="center" p={1}>
                  <Text>{day}</Text>
                  <Text>{dates[dayIndex]}</Text>
                </Th>
              ))}
            </Tr>
          </Thead>
          <Tbody>
            {hours.map((hour, hourIndex) => (
              <Tr border={'none'} key={`hour-${hourIndex}`}>
                <Td border={'none'} fontWeight="medium" p={1}>
                  <Text fontWeight={'normal'}>{hour}</Text>
                </Td>
                {days.map((day, dayIndex) => {
                  const key = `day-${dayIndex}-${hour}`;
                  const isSelected = selectedCells[key];

                  return (
                    <Td border={'none'} key={key} p={'2px'} textAlign="center">
                      <Box
                        display={'flex'}
                        justifyContent="center"
                        alignItems="center"
                        as="button"
                        w="35px"
                        h="35px"
                        bg={isSelected ? 'gray.50' : 'green.50'}
                        color={isSelected ? 'gray.300' : 'green.500'}
                        fontWeight="1000"
                        onClick={() =>
                          isSelected ? null : toggleCell(day, hour, dates[dayIndex], dayIndex)
                        }
                        _hover={{ borderColor: 'green.500' }}
                      >
                        {isSelected ? '−' : <CircleIcon />}
                      </Box>
                    </Td>
                  );
                })}
              </Tr>
            ))}
          </Tbody>
        </Table>
      </Flex>

      <Modal isOpen={isOpen} onClose={onClose} isCentered>
        <ModalOverlay />
        <ModalContent borderRadius="1rem" maxW="90%" w="400px">
          {activeStep === 0 && (
            <ModalHeader borderBottom="1px solid" borderColor="gray.200" p={4}>
              {selectedTime && formatFullDate(selectedTime.fullDate)}
            </ModalHeader>
          )}
          <ModalCloseButton onClick={() => setActiveStep(0)} />

          {activeStep === 0 && (
            <ModalBody p={6}>
              {selectedTime && (
                <Flex direction="column" gap={4}>
                  <Text fontWeight="bold" fontSize="md">
                    {t('usage')}
                  </Text>
                  <Flex alignItems={'center'} gap={2}>
                    <SelectField
                      fontSize={'sm'}
                      borderRadius={'10px'}
                      border={'1px solid #E5EAF1'}
                      w={'auto'}
                      px={3}
                      py={3}
                    >
                      <option value="option1">{selectedTime.hour}</option>
                    </SelectField>
                    <Text>:</Text>
                    <SelectField
                      fontSize={'sm'}
                      borderRadius={'10px'}
                      border={'1px solid #E5EAF1'}
                      w={'auto'}
                      px={3}
                      py={3}
                    >
                      <option value="option2">{incrementHour(selectedTime.hour, 1)}</option>
                    </SelectField>
                  </Flex>
                  <Text fontWeight="bold" fontSize="md">
                    {t('addPeople')}
                  </Text>

                  <Flex alignItems={'center'} gap={'1.5rem'}>
                    <Button
                      bg={'gray.100'}
                      variant={'ghost'}
                      fontSize={'1.5rem'}
                      fontWeight={'normal'}
                      display={'flex'}
                      gap={4}
                      alignItems={'center'}
                      justifyContent={'center'}
                      onClick={() =>
                        setNumberOfPeople(prev =>
                          typeof prev === 'number' ? Math.max(1, prev - 1) : 1
                        )
                      }
                    >
                      -
                    </Button>
                    <Input
                      type="number"
                      border={'1px solid #E5EAF1'}
                      value={numberOfPeople}
                      onChange={e => {
                        const inputValue = e.target.value;
                        if (inputValue === '') {
                          setNumberOfPeople('');
                        } else {
                          const value = parseInt(inputValue, 10);
                          if (!isNaN(value)) {
                            setNumberOfPeople(Math.min(value, MAX_PEOPLE));
                          }
                        }
                      }}
                      onBlur={() => {
                        if (
                          numberOfPeople === '' ||
                          (typeof numberOfPeople === 'number' && numberOfPeople < 1)
                        ) {
                          setNumberOfPeople(1);
                        }
                      }}
                      borderRadius={'10px'}
                      px={3}
                      py={1}
                      w={'100%'}
                      max={MAX_PEOPLE}
                    />
                    <Button
                      bg={'gray.100'}
                      variant={'ghost'}
                      fontSize={'1.5rem'}
                      fontWeight={'normal'}
                      display={'flex'}
                      gap={4}
                      alignItems={'center'}
                      justifyContent={'center'}
                      onClick={() =>
                        setNumberOfPeople(prev => {
                          const current =
                            typeof prev === 'number' ? prev : parseInt(prev || '1', 10);
                          return Math.min(current + 1, MAX_PEOPLE);
                        })
                      }
                    >
                      +
                    </Button>
                  </Flex>
                </Flex>
              )}
            </ModalBody>
          )}

          {activeStep === 1 && (
            <ModalBody
              display={'flex'}
              flexDirection={'column'}
              justifyContent={'center'}
              alignItems={'center'}
              gap={'1rem'}
              p={'1.5rem'}
              mt={'2rem'}
            >
              <Flex
                justifyContent={'center'}
                alignItems={'center'}
                textAlign={'center'}
                flexDirection={'column'}
                width="250px"
              >
                <Text fontWeight={700} variant={'brand'} mb={4}>
                  {constants.COMPANY_NAME}
                </Text>
                <Text fontWeight={700} variant={'subHeaderXL'} mb={4}>
                  予約変更完了
                </Text>
                <CheckCircleIcon />
              </Flex>

              <Flex fontSize={'sm'} gap={'1rem'}>
                <Text variant={'category'}>リクエスト予約</Text>
                <Text letterSpacing={'.5px'} fontWeight={'bold'}>
                  000000000000
                </Text>
              </Flex>

              <Flex fontSize={'sm'}>
                <Text>ご予約が完了しました。</Text>
                <Link variant={'underline'}>予約確認はこちら</Link>
              </Flex>

              <Flex w={'100%'} gap={'1rem'} bg={'gray.50'} px={'1rem'} borderRadius={'1rem'}>
                <Flex w={'100%'} flexDirection={'column'} gap={'1px'} bg={'gray.200'}>
                  <Flex bg={'gray.50'} py={'1rem'} flexDirection={'column'}>
                    <Text fontWeight={'medium'}>注意事項</Text>
                    <Text fontWeight={'medium'}>予約に関する注意事項</Text>

                    <Flex mt={'.5rem'} fontSize={'sm'} flexDirection={'column'}>
                      <Text fontSize={'sm'}>予約確認のためご連絡することがございます。</Text>
                      <Text fontSize={'sm'}>繋がりやすい連絡先を登録ください。</Text>
                    </Flex>
                  </Flex>
                  <Flex bg={'gray.50'} gap={'1rem'} py={'1rem'} flexDirection={'column'}>
                    <Text fontWeight={'medium'}>キャンセルポリシー</Text>
                    <Text fontSize={'sm'}>
                      キャンセル料なしキャンセルはご予約前日までにご対応ください。当日キャンセルも可能ではございますが、何度も当日キャンセルをする方は予約の制限をかけさせていただく場合がございます。予めご注意ください。
                    </Text>
                  </Flex>
                </Flex>
              </Flex>
            </ModalBody>
          )}

          <ModalFooter p={4}>
            {activeStep === 0 && (
              <Flex w="100%" alignItems={'center'} gap={4}>
                <Button variant="rounded" flex={1} onClick={onClose}>
                  戻る
                </Button>
                <Button variant="roundedBlue" flex={1} onClick={() => setActiveStep(1)}>
                  変更する
                </Button>
              </Flex>
            )}
            {activeStep === 1 && (
              <Flex w="100%" alignItems={'center'} gap={4}>
                <Button
                  border={'none'}
                  bg={'gray.50'}
                  variant="rounded"
                  flex={1}
                  onClick={confirmReservation}
                >
                  戻る
                </Button>
              </Flex>
            )}
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
}

export default CardTimeSelect;
