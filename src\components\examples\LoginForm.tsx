import { useState } from 'react';
import { <PERSON>ton, FormControl, FormLabel, Input, Text, VStack, Alert, AlertIcon } from '@chakra-ui/react';
import { useUserStore } from '@/store/userStore';

export default function LoginForm() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  
  // Get state and actions from the store
  const { login, isLoading, error, clearError } = useUserStore();
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await login(email, password);
  };
  
  return (
    <VStack as="form" spacing={4} onSubmit={handleSubmit}>
      {error && (
        <Alert status="error" borderRadius="md" onClick={clearError} cursor="pointer">
          <AlertIcon />
          {error}
        </Alert>
      )}
      
      <FormControl isRequired>
        <FormLabel>Email</FormLabel>
        <Input 
          type="email" 
          value={email} 
          onChange={(e) => setEmail(e.target.value)} 
          placeholder="Enter your email"
        />
      </FormControl>
      
      <FormControl isRequired>
        <FormLabel>Password</FormLabel>
        <Input 
          type="password" 
          value={password} 
          onChange={(e) => setPassword(e.target.value)} 
          placeholder="Enter your password"
        />
      </FormControl>
      
      <Button 
        type="submit" 
        colorScheme="blue" 
        width="full" 
        isLoading={isLoading}
        loadingText="Logging in"
      >
        Login
      </Button>
    </VStack>
  );
}
