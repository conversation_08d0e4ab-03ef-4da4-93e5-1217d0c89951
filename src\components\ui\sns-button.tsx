import AppleIcon from '@/assets/icons/apple-icon';
import GoogleIcon from '@/assets/icons/google';
import LineIcon from '@/assets/icons/line-icon';
import { Button, Flex, Link, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';

interface SnsButtonProps {
  variant?: string;
  isLinked?: boolean;
}

function SnsButton({ variant, isLinked = false }: SnsButtonProps) {
  const { t } = useTranslation('profile');
  return (
    <Button
      display={'flex'}
      justifyContent={'space-between'}
      variant={'rounded'}
      fontSize={'sm'}
      fontWeight={'bold'}
      color={'mainColor'}
    >
      <Flex alignItems={'center'} gap={'1rem'}>
        {variant === 'google' ? (
          <GoogleIcon width={20} height={20} />
        ) : variant === 'apple' ? (
          <AppleIcon width={25} height={25} />
        ) : variant === 'line' ? (
          <LineIcon width={25} height={25} />
        ) : null}
        <Text fontSize={'md'} fontWeight={'medium'}>
          <span>
            {variant === 'google'
              ? t('account.googleCalendar')
              : variant === 'apple'
              ? t('account.appleCalendar')
              : variant === 'line'
              ? t('account.lineRegistration')
              : null}
          </span>
        </Text>
      </Flex>
      <Link variant={'blue'} href={'#'}>
        <span>{isLinked ? t('account.disconnect') : t('account.linkAccount')}</span>
      </Link>
    </Button>
  );
}

export default SnsButton;
