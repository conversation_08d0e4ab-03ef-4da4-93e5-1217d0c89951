import { Flex, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';

function CardTextStoreInformation() {
  const { t } = useTranslation('common');

  // TODO: This could be dynamic data from an API or props
  const storeInfo = {
    location: '東京・麻布十番駅',
    distance: '500m',
    lunchPrice: '9,000',
    dinnerPrice: '12,000',
    openingHours: '11:30〜21:30',
  };

  const today = new Date().getDay();
  const dayKeys = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
  const todayKey = dayKeys[today];

  return (
    <Flex
      bg={'white'}
      flexDirection={'column'}
      p={{ base: '1rem', md: '2rem' }}
      borderRadius={'1rem'}
    >
      <Text fontSize={'sm'} fontWeight={'bold'} textAlign={'center'}>
        {t('store.storeInformation')}
      </Text>
      <Flex flexDirection={'column'} gap={'.5rem'} mt={'1rem'}>
        <Text fontSize={'xs'} color={'black600'}>
          {t('store.storeLocation', {
            location: storeInfo.location,
            distance: storeInfo.distance,
          })}
        </Text>
        <Text fontSize={'xs'} color={'black600'}>
          {t('store.storePricing', {
            lunch: storeInfo.lunchPrice,
            dinner: storeInfo.dinnerPrice,
          })}
        </Text>
        <Text fontSize={'xs'} color={'black600'}>
          {t('store.storeHoursToday', {
            day: t(`calendar.${todayKey}`),
            hours: storeInfo.openingHours,
          })}
        </Text>
      </Flex>
    </Flex>
  );
}

export default CardTextStoreInformation;
