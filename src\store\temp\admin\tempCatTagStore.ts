import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { tempCategory, tempTag } from '@/.temp/optionList/tempCategoryTagList';
import { CategoryType, TagType } from '@/types/catTagTypes';

type Cat = {
  categories: CategoryType[];
  addCategory: (category: CategoryType) => void;
  deleteCategory: (id: string | number) => void;
  editCategory: (category: CategoryType) => void;
};

type Tag = {
  tags: TagType[];
  addTag: (tag: TagType) => void;
  deleteTag: (id: string | number) => void;
  editTag: (tag: TagType) => void;
};

export const useTempCatStore = create<Cat>()(
  immer(set => ({
    categories: tempCategory,
    addCategory: (category: CategoryType) => {
      if (category.name === '') return;
      if (!category.name) return;
      set(state => {
        const exists = state.categories.some(c => c.id === category.id || c.name === category.name);
        if (!exists) {
          state.categories.push(category);
        }
      });
    },
    deleteCategory: (id: string | number) => {
      set(state => {
        state.categories = state.categories.filter(c => c.id !== id);
      });
    },
    editCategory: (category: CategoryType) => {
      set(state => {
        const index = state.categories.findIndex(c => c.id === category.id);
        if (index !== -1) {
          state.categories[index] = category;
        }
      });
    },
  }))
);

export const useTempTagStore = create<Tag>()(
  immer(set => ({
    tags: tempTag,
    addTag: (tag: TagType) => {
      if (tag.name === '') return;
      if (!tag.name) return;
      set(state => {
        const exists = state.tags.some(c => c.id === tag.id || c.name === tag.name);
        if (!exists) {
          state.tags.push(tag);
        }
      });
    },
    deleteTag: (id: string | number) => {
      set(state => {
        state.tags = state.tags.filter(c => c.id !== id);
      });
    },
    editTag: (tag: TagType) => {
      set(state => {
        const index = state.tags.findIndex(c => c.id === tag.id);
        if (index !== -1) {
          state.tags[index] = tag;
        }
      });
    },
  }))
);
