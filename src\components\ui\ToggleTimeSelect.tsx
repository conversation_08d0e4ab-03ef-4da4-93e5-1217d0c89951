import { DAY_NAMES_SUN_START } from '@/constants/dayNames';
import { FormDataType } from '@/types/FormDataType';
import { addTimeSlot } from '@/utils/addTimeSlot';
import { removeTimeSlot } from '@/utils/removeTimeSlot';
import { Button, Flex, Image, Stack, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import TimeSelect from './formfields/time-select';
import { ToggleButton } from './formfields/toggle-button';

type ToggleTimeSelectProps = {
  formData: FormDataType;
  setFormData: (value: FormDataType) => void;
  handleInputChange: (e: any) => void;
  // addTimeSlot: any;
  // removeTimeSlot: any;
};

export default function ToggleTimeSelect({
  formData,
  setFormData,
  handleInputChange,
}: // addTimeSlot,
// removeTimeSlot,
ToggleTimeSelectProps) {
  const { t } = useTranslation('admin');

  // Day name mapping for translation
  const getDayTranslationKey = (japaneseDayName: string): string => {
    const mapping: Record<string, string> = {
      月曜日: 'monday',
      火曜日: 'tuesday',
      水曜日: 'wednesday',
      木曜日: 'thursday',
      金曜日: 'friday',
      土曜日: 'saturday',
      日曜日: 'sunday',
    };
    return mapping[japaneseDayName] || japaneseDayName;
  };
  return (
    <Stack gap="4" align="flex-start">
      {DAY_NAMES_SUN_START.map((day, index) => (
        <Flex key={index} alignItems={'flex-start'} w={'100%'} justifyContent={'space-between'}>
          <Flex alignItems={'center'} gap={2}>
            <ToggleButton
              isActive={Boolean(formData.availability[day as keyof typeof formData.availability])}
              onChange={() =>
                setFormData({
                  ...formData,
                  availability: {
                    ...formData.availability,
                    [day]: !formData.availability[day as keyof typeof formData.availability],
                  },
                })
              }
            />
            <Text fontSize={'sm'} fontWeight={700}>
              {t(`addStore.timeSelect.dayNames.${getDayTranslationKey(day)}`)}
            </Text>
          </Flex>
          <Flex direction="column" justifyContent={'center'} w={'57%'} gap={2}>
            {formData.availability[day as keyof typeof formData.availability] ? (
              formData.availability.timeSlots[
                day as keyof typeof formData.availability.timeSlots
              ].map((slot: Record<string, unknown>, index: number) => (
                <Flex key={`${day}-${index}`} gap={4} alignItems="center">
                  <TimeSelect />

                  {index === 0 &&
                  formData.availability[day as keyof typeof formData.availability] ? (
                    <Button
                      size="sm"
                      ml={2}
                      p={1}
                      variant="ghost"
                      onClick={() => addTimeSlot({ day, formData, setFormData })}
                    >
                      <Image
                        src="/imgs/icons/plus.svg"
                        alt={t('addStore.timeSelect.buttons.addTimeSlot')}
                        width={18}
                        height={18}
                      />
                    </Button>
                  ) : (
                    index > 0 && (
                      <Button
                        size="sm"
                        ml={2}
                        p={1}
                        variant="ghost"
                        onClick={() => removeTimeSlot({ day, index, formData, setFormData })}
                      >
                        <Image
                          src="/imgs/icons/trash.svg"
                          alt={t('addStore.timeSelect.buttons.removeTimeSlot')}
                          width={18}
                          height={18}
                        />
                      </Button>
                    )
                  )}
                </Flex>
              ))
            ) : (
              <Text>{t('addStore.timeSelect.closedDay')}</Text>
            )}
          </Flex>
        </Flex>
      ))}
    </Stack>
  );
}
