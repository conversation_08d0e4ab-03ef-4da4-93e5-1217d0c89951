import { BadgeStatusType } from '@/constants/badgeStatusType';
import { Badge } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import React from 'react';

/**
 * Props interface for StatusBadge component
 */
interface StatusBadgeProps {
  status: string;
}

/**
 * StatusBadge React component that displays a status badge with internationalization support
 * @param props - Component props containing the status string
 * @returns JSX Badge element with translated status text
 */
export const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  type ReservationStatus = BadgeStatusType['BADGE_STATUS'];
  const { t } = useTranslation(['admin', 'common']);

  const statusMap: Record<ReservationStatus, { bg: string; color: string; text: string }> = {
    confirmed: {
      bg: '#ECEFF1',
      color: 'black',
      text: 'admin:membersList.filter.statusOptions.pending',
    },
    pending: {
      bg: '#DEE7FB',
      color: '#2B42CA',
      text: 'admin:membersList.filter.statusOptions.pending',
    },
    cancelled: {
      bg: '#607D8B',
      color: '#ECEFF1',
      text: 'admin:membersList.filter.statusOptions.cancelled',
    },
    active: {
      bg: '#ECEFF1',
      color: 'black',
      text: 'admin:membersList.filter.statusOptions.active',
    },
    deleted: {
      bg: '#607D8B',
      color: 'white',
      text: 'admin:membersList.filter.statusOptions.deleted',
    },
    important: { bg: 'alert', color: 'white', text: 'common:newsList.category.important' },
    news: { bg: 'stroke', color: 'black', text: 'common:newsList.category.news' },
    event: { bg: '#E8F5E8', color: '#2E7D32', text: 'common:newsList.category.event' },
  };

  const { bg, color, text } =
    status in statusMap
      ? statusMap[status as ReservationStatus]
      : { bg: status, color: 'gray', text: status };

  return (
    <Badge
      className="status-badge"
      color={color}
      bg={bg}
      px={2}
      py={1}
      minW={'5rem'}
      textAlign={'center'}
      borderRadius="md"
      fontWeight={'normal'}
      textTransform={'none'}
    >
      {t(text)}
    </Badge>
  );
};
