import React from 'react';

interface TriangleIconProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

const TriangleIcon: React.FC<TriangleIconProps> = ({
  width = 18,
  height = 18,
  color = '#1E293B',
  className,
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_5271_5274)">
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M12 2C12.6896 2 13.3305 2.35524 13.696 2.94L23.696 18.94C24.0813 19.5565 24.1017 20.3336 23.7493 20.9695C23.3968 21.6054 22.7271 22 22 22H2C1.27295 22 0.603167 21.6054 0.250716 20.9695C-0.101735 20.3336 -0.0813316 19.5565 0.304004 18.94L10.304 2.94C10.6695 2.35524 11.3104 2 12 2ZM5.6085 18H18.3915L12 7.77359L5.6085 18Z"
          fill={color}
        />
      </g>
      <defs>
        <clipPath id="clip0_5271_5274">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default TriangleIcon;
