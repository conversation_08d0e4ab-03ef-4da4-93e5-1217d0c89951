import Assignment from '@/assets/icons/assignment';
import DocumentIcon from '@/assets/icons/document';
import PenIcon from '@/assets/icons/pen';
import {
  Button,
  Flex,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  <PERSON>dal<PERSON>ooter,
  ModalOverlay,
  Text,
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';

export default function ConfirmationModal({
  variant,
  isOpen,
  onClose,
  setModalStep,
  modalStep,
  handleClose,
  handleSubmit,
}: {
  variant?: string;
  isOpen: boolean;
  setModalStep?: (value: number) => void;
  onClose: () => void;
  modalStep: number;
  handleClose: () => void;
  handleSubmit: () => void;
}) {
  const { t } = useTranslation('admin');
  return (
    <Modal isOpen={isOpen} onClose={handleClose}>
      <ModalOverlay />
      {modalStep === 1 ? (
        <ModalContent>
          <Flex justifyContent={'center'} alignItems={'center'} h={'5rem'}>
            {variant === 'add' ? (
              <DocumentIcon width={30} height={30} color="black" className="my-icon-class" />
            ) : (
              <PenIcon width={25} height={25} color="black" className="my-icon-class" />
            )}
          </Flex>
          <ModalCloseButton />
          <ModalBody fontSize={'sm'} textAlign={'center'}>
            <Text mb={4} fontSize={'md'} fontWeight={'bold'}>
              {t('confirmationModal.step1.message')}
            </Text>
          </ModalBody>

          <ModalFooter gap={4} justifyContent={'center'}>
            <Button px={'3rem'} w={'auto'} variant="rounded" onClick={onClose}>
              {t('confirmationModal.buttons.back')}
            </Button>
            <Button px={'3rem'} w={'auto'} variant="roundedBlue" onClick={handleSubmit}>
              {t('confirmationModal.buttons.register')}
            </Button>
          </ModalFooter>
        </ModalContent>
      ) : (
        <ModalContent>
          <Flex justifyContent={'center'} alignItems={'center'} h={'5rem'}>
            <Assignment width={25} height={25} />
          </Flex>
          <ModalCloseButton />
          <ModalBody fontSize={'sm'} textAlign={'center'}>
            <Text mb={4} fontSize={'md'} fontWeight={'bold'}>
              {t('confirmationModal.step2.message')}
            </Text>
          </ModalBody>

          <ModalFooter gap={4} justifyContent={'center'}>
            <Button px={'3rem'} w={'auto'} variant="roundedBlue" onClick={handleClose}>
              {t('confirmationModal.buttons.close')}
            </Button>
          </ModalFooter>
        </ModalContent>
      )}
    </Modal>
  );
}
