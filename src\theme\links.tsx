export const links = {
  default: {
    color: '#2B42CA',
    textDecoration: 'none',
    fontWeight: '500',
    transition: 'all 0.2s ease',
    _hover: {
      textDecoration: 'underline',
      color: 'blue.700',
    },
    _focus: {
      boxShadow: '0 0 0 3px rgba(43, 66, 202, 0.3)',
      outline: 'none',
    },
  },

  underlined: {
    color: '#2B42CA',
    textDecoration: 'underline',
    fontWeight: '500',
    transition: 'all 0.2s ease',
    _hover: {
      color: 'blue.700',
    },
    _focus: {
      boxShadow: '0 0 0 3px rgba(43, 66, 202, 0.3)',
      outline: 'none',
    },
  },

  blue: {
    color: '#2B42CA',
    textDecoration: 'none',
    fontWeight: '400',
    transition: 'all 0.2s ease',
    _hover: {
      color: 'blue.700',
    },
    _focus: {
      boxShadow: '0 0 0 3px rgba(43, 66, 202, 0.3)',
      outline: 'none',
    },
  },

  subtle: {
    color: 'black600',
    textDecoration: 'none',
    fontWeight: '400',
    transition: 'all 0.2s ease',
    _hover: {
      color: '#2B42CA',
      textDecoration: 'underline',
    },
    _focus: {
      boxShadow: '0 0 0 3px rgba(43, 66, 202, 0.3)',
      outline: 'none',
    },
  },

  button: {
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#2B42CA',
    backgroundColor: 'transparent',
    border: 'none',
    padding: '0',
    fontWeight: '500',
    cursor: 'pointer',
    textDecoration: 'underline',
    transition: 'all 0.2s ease',
    _hover: {
      color: 'blue.700',
    },
    _focus: {
      boxShadow: '0 0 0 3px rgba(43, 66, 202, 0.3)',
      outline: 'none',
    },
    _disabled: {
      opacity: 0.6,
      cursor: 'not-allowed',
      _hover: {
        color: '#2B42CA',
      },
    },
  },

  nav: {
    color: 'black',
    textDecoration: 'none',
    fontWeight: '500',
    padding: '0.5rem 0',
    borderBottom: '2px solid transparent',
    transition: 'all 0.2s ease',
    _hover: {
      color: '#2B42CA',
      borderBottomColor: '#2B42CA',
    },
    _active: {
      color: '#2B42CA',
      borderBottomColor: '#2B42CA',
    },
    _focus: {
      boxShadow: 'none',
      outline: 'none',
    },
  },
};
