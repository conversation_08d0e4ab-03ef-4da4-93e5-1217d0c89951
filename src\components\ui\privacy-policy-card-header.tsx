import { ReactNode } from 'react';
import { Flex, Text } from '@chakra-ui/react';

interface CardContainerProps {
  children: ReactNode;
  variant?: string;
}

export default function PrivacyPolicyCardHeader({
  children,
  variant = 'default',
}: CardContainerProps) {
  return (
    <Flex w={'100%'}>
      <Text
        w={'100%'}
        fontWeight={'bold'}
        borderBottom={'1px solid'}
        borderColor={'gray.200'}
        position={'relative'}
        px={'.5rem'}
        _after={{
          content: '""',
          display: 'block',
          width: '3px',
          height: '100%',
          backgroundColor: 'mainColor',
          position: 'absolute',
          bottom: 0,
          left: 0,
        }}
      >
        {children}
      </Text>
    </Flex>
  );
}
