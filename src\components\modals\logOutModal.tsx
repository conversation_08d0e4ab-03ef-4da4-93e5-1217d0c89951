import {
  <PERSON>ton,
  <PERSON>dal,
  ModalBody,
  ModalCloseButton,
  Modal<PERSON>ontent,
  <PERSON>dal<PERSON><PERSON>er,
  Modal<PERSON>eader,
  ModalOverlay,
  Text,
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import Router from 'next/router';

interface LogOutProps {
  isOpen: boolean;
  onClose: () => void;
  context?: 'admin' | 'client';
}

export default function LogOutModal({ isOpen, onClose, context: context = 'admin' }: LogOutProps) {
  const { t } = useTranslation('profile');

  const loginUrl = context === 'client' ? '/client/login' : '/login';

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent borderRadius={'1rem'}>
        <ModalHeader>
          <Text borderBottom={'1px solid'} borderBottomColor="gray.200" pt={'1rem'} pb={'.5rem'}>
            {t('account.logout')}
          </Text>
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody mb={'1rem'}>
          <Text>{t('account.confirmLogout')}</Text>
        </ModalBody>
        <ModalFooter display={'flex'} gap={4} justifyContent={'center'}>
          <Button borderColor={'black'} variant="rounded" onClick={onClose}>
            <span>{t('actions.cancel')}</span>
          </Button>
          <Button
            variant="roundedBlue"
            onClick={() => {
              onClose();
              Router.push(loginUrl);
            }}
          >
            <span>{t('account.logout')}</span>
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
