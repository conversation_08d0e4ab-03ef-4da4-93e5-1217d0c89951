import React from 'react';

interface FlagIconProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

const FlagIcon: React.FC<FlagIconProps> = ({
  width = 18,
  height = 18,
  color = '#1E293B',
  className,
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.8103 5.55931L15.8898 1.0305C16.1066 0.710688 15.9937 0.293563 15.6376 0.098875C15.5198 0.0344687 15.3846 0.00028125 15.2467 0H0.754792C0.337926 0 0 0.303531 0 0.677969V15.322C0 15.6965 0.337926 16 0.754792 16C1.17166 16 1.50958 15.6965 1.50958 15.322V11.1187H15.2467C15.6636 11.1178 16.0008 10.8137 15.9999 10.4392C15.9996 10.3153 15.9615 10.1939 15.8898 10.0882L12.8103 5.55931Z"
        fill={color}
      />
    </svg>
  );
};

export default FlagIcon;
