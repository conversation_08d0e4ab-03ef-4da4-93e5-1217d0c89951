import MemberList from '@/components/tables/member-list';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

export default function MembersListPage() {
  return <MemberList />;
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'admin'])),
      layoutType: 'withSidebar',
    },
  };
}
