import { color } from 'framer-motion';

export const buttons = {
  rounded: {
    borderRadius: '50px',
    paddingY: '1.5rem',
    paddingLeft: '1rem',
    width: '100%',
    fontSize: '.9rem',
    border: '1px solid #e2e8f0',
    transition: 'all 0.3s ease',
    _hover: {
      backgroundColor: '#f0f4fe',
    },
  },

  roundedNoBorder: {
    borderRadius: '50px',
    padding: '1.2rem 1rem 1.2rem 1rem',
    fontSize: '.9rem',
    transition: 'all 0.3s ease',
    backgroundColor: 'borderGray',
    _hover: {
      backgroundColor: '#f0f4fe',
    },
  },

    roundedBlue: {
        borderRadius: "50px",
        paddingY: "1.5rem",
        margin: '0',
        width: "100%",
        fontSize: ".9rem",
        border: "1px solid #e2e8f0",
        transition: "all 0.3s ease",
        backgroundColor: "#2B42CA",
        color: "white",
        _hover: {
          backgroundColor: "blue",
        }
      },

      blue: {
        transition: "all 0.3s ease",
        backgroundColor: "#2B42CA",
        color: "white",
        _hover: {
          backgroundColor: "blue",
        }
      },

  RoundedOutline: {
    borderRadius: '50px',
    paddingY: '1.5rem',
    // margin: '1.5rem 0',
    width: '100%',
    fontSize: '.9rem',
    // border: "1px solid #e2e8f0",
    transition: 'all 0.3s ease',
    backgroundColor: '#F1F5F9',
    color: 'black',
    _hover: {
      backgroundColor: '#ECF0F8',
    },
  },

  card: {
    borderRadius: '15px',
    padding: '1.7rem',
    width: '100%',
    fontSize: '1rem',
    transition: 'all 0.3s ease',
    backgroundColor: 'white',
    color: 'black',
  },

  SquaredHighlight: {
    borderRadius: '10px',
    padding: '1rem',
    width: '100%',
    fontSize: '.9rem',
    transition: 'all 0.3s ease',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    // backgroundColor: "red",
    color: 'black',
    _hover: {
      backgroundColor: '#ECF0F8',
    },
  },
};
