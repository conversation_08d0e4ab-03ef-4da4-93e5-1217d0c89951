import Login from '@/components/forms/login';
import { Flex } from '@chakra-ui/react';
import '@fontsource/inter';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

export default function LoginPage() {
  return (
    <>
      <Flex
        flexDirection={'column'}
        alignItems={'center'}
        justifyContent={'center'}
        h={'100vh'}
        w={{ base: '100%', md: 'auto' }}
        fontFamily="Inter, sans-serif"
      >
        <Login context="client" />
      </Flex>
    </>
  );
}
export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'auth'])),
      layoutType: 'blank',
    },
  };
}
