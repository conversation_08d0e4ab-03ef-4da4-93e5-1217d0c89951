import GoogleIcon from '@/assets/icons/google';
import PlusIcon from '@/assets/icons/plus-icon';
import TrashIcon from '@/assets/icons/trash';
import {
  Box,
  Button,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  HStack,
  Input,
  Stack,
  Text,
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import Image from 'next/image';
import { useState } from 'react';
import TimeSelect from '../ui/formfields/time-select';
import { ToggleButton } from '../ui/formfields/toggle-button';

export default function CreateAccount() {
  const { t } = useTranslation('auth');
  const [formData, setFormData] = useState({
    profileIcon: '',
    username: '',
    groupname: '',
    storeID: '',
    storeOverview: '',
    availability: {
      monday: false,
      tuesday: false,
      wednesday: false,
      thursday: false,
      friday: false,
      saturday: false,
      sunday: false,
      timeSlots: {
        monday: [{}],
        tuesday: [{}],
        wednesday: [{}],
        thursday: [{}],
        friday: [{}],
        saturday: [{}],
        sunday: [{}],
      },
    },
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    phoneNumber: '',
  });

  const addTimeSlot = (day: keyof typeof formData.availability.timeSlots) => {
    setFormData({
      ...formData,
      availability: {
        ...formData.availability,
        timeSlots: {
          ...formData.availability.timeSlots,
          [day]: [...formData.availability.timeSlots[day], {}],
        },
      },
    });
  };

  const removeTimeSlot = (day: keyof typeof formData.availability.timeSlots, index: number) => {
    setFormData({
      ...formData,
      availability: {
        ...formData.availability,
        timeSlots: {
          ...formData.availability.timeSlots,
          [day]: formData.availability.timeSlots[day].filter((_, i) => i !== index),
        },
      },
    });
  };

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [activeStep, setActiveStep] = useState(0);

  const steps = [
    { title: t('createAccount.steps.userInfo.title'), description: '' },
    { title: t('createAccount.steps.storeInfo.title'), description: '' },
    { title: t('createAccount.steps.externalCalendar.title'), description: '' },
    { title: t('createAccount.steps.businessHours.title'), description: '' },
  ];

  const progressPercent = ((activeStep + 1) / steps.length) * 100;

  const handleInputChange = (e: any) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const validateStep = (step: any) => {
    let stepErrors: Record<string, string> = {};
    let isValid = true;

    if (step === 0) {
      if (!formData.username) {
        stepErrors.username = t('createAccount.validation.usernameRequired');
        isValid = false;
      }
      if (!formData.password) {
        stepErrors.password = t('createAccount.validation.passwordRequired');
        isValid = false;
      }
      if (formData.password !== formData.confirmPassword) {
        stepErrors.confirmPassword = t('createAccount.validation.passwordMismatch');
        isValid = false;
      }
    } else if (step === 1) {
      if (!formData.firstName) {
        stepErrors.firstName = t('createAccount.validation.firstNameRequired');
        isValid = false;
      }
      if (!formData.lastName) {
        stepErrors.lastName = t('createAccount.validation.lastNameRequired');
        isValid = false;
      }
      if (!formData.phoneNumber) {
        stepErrors.phoneNumber = t('createAccount.validation.phoneNumberRequired');
        isValid = false;
      }
    }

    setErrors(stepErrors);
    return isValid;
  };

  const handleNext = () => {
    console.log(activeStep);
    setActiveStep(activeStep + 1);

    // if (validateStep(activeStep)) {
    //   setActiveStep(activeStep + 1);

    // }
  };

  const handleBack = () => {
    setActiveStep(activeStep - 1);
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();

    console.log('Form submitted:', formData);
  };

  const [isActive, setIsActive] = useState(false);

  return (
    <Flex
      w={{ base: '87%', md: '30rem' }}
      pt={{ base: '3rem', md: '0' }}
      justify={'center'}
      gap="4"
      direction="column"
    >
      {activeStep === 0 && (
        <Flex flexDirection={'column'} gap={'.5rem'}>
          <Text variant={'header'}>{t('createAccount.steps.userInfo.title')}</Text>
          <Text variant={'subHeader'}>{t('createAccount.steps.userInfo.description')}</Text>
          <Text variant={'error'}>{t('createAccount.requiredFieldsNotice')}</Text>
          <Text visibility={'hidden'} h={'0'} m={0} p={0} variant={'subHeader'}>
            {t('createAccount.steps.externalCalendar.description')}
          </Text>
        </Flex>
      )}

      {activeStep === 1 && (
        <Flex flexDirection={'column'} gap={'.5rem'}>
          <Text variant={'header'}>{t('createAccount.steps.storeInfo.title')}</Text>
          <Text variant={'subHeader'}>{t('createAccount.steps.storeInfo.description')}</Text>
          <Text variant={'error'}>{t('createAccount.requiredFieldsNotice')}</Text>
          <Text visibility={'hidden'} h={'0'} m={0} p={0} variant={'subHeader'}>
            {t('createAccount.steps.externalCalendar.description')}
          </Text>
        </Flex>
      )}
      {activeStep === 2 && (
        <Flex flexDirection={'column'} gap={'.5rem'}>
          <Text variant={'header'}>{t('createAccount.steps.externalCalendar.title')}</Text>
          <Text variant={'subHeader'}>{t('createAccount.steps.externalCalendar.description')}</Text>
        </Flex>
      )}
      {activeStep === 3 && (
        <Flex flexDirection={'column'} gap={'.5rem'}>
          <Text variant={'header'}>{t('createAccount.steps.businessHours.title')}</Text>
          <Flex fontSize={'.8rem'} flexDir={'column'} gap={'0'}>
            <Text>{t('createAccount.steps.businessHours.description')}</Text>
            <Text>{t('createAccount.steps.businessHours.customizableNote')}</Text>
          </Flex>
          <Text visibility={'hidden'} h={'0'} m={0} p={0} variant={'subHeader'}>
            {t('createAccount.steps.externalCalendar.description')}
          </Text>
        </Flex>
      )}

      {/* Progress Bar */}
      <Box mb={'0'}>
        <Flex justify="space-between" mb={1}>
          {/* <Text fontWeight="medium">{steps[activeStep].title}</Text> */}
          <Text fontSize={'sm'} color="gray.700">
            {t('createAccount.stepProgress', { current: activeStep + 1, total: steps.length })}
          </Text>
        </Flex>

        {/* Segmented Progress Bar */}
        <Flex w="100%" gap={1} mb={2}>
          {steps.map((_, index) => (
            <Box
              key={index}
              h="6px"
              flex="1"
              bg={index <= activeStep ? 'mainColor' : 'gray.200'}
              borderRadius="full"
            />
          ))}
        </Flex>

        <HStack mt={2} justify="space-between">
          {steps.map((step, index) => (
            <Box
              key={index}
              w="30%"
              textAlign={index === 0 ? 'left' : index === steps.length - 1 ? 'right' : 'center'}
            >
              <Text
                fontSize="xs"
                color={index <= activeStep ? '#273CB7' : 'gray.400'}
                fontWeight={index === activeStep ? 'bold' : 'normal'}
              >
                {step.description}
              </Text>
            </Box>
          ))}
        </HStack>
      </Box>

      <Box bg={'white'} borderRadius={'.5rem'} p="6">
        <form onSubmit={handleSubmit}>
          {activeStep === 0 && (
            <Stack gap="4" align="flex-start">
              <FormControl isInvalid={!!errors.profileIcon}>
                <Text>{t('createAccount.form.profileIcon')}</Text>
                <Flex alignItems={'center'} mt={'.5rem'}>
                  <Image
                    style={{ borderRadius: '100rem', marginRight: '10px' }}
                    src="/imgs/icons/profileIcon.svg"
                    alt=""
                    width={60}
                    height={60}
                  />

                  <FormLabel
                    fontSize={'small'}
                    margin={'0'}
                    ml={'.5rem'}
                    paddingX={'4'}
                    paddingY={'2'}
                    borderRadius={'50px'}
                    border={'1px solid black'}
                  >
                    {t('createAccount.form.changePhoto')}
                  </FormLabel>
                  <Input
                    name="profileIcon"
                    type="file"
                    value={formData.profileIcon}
                    onChange={handleInputChange}
                    display={'none'}
                  />
                  <FormErrorMessage>{errors.profileIcon}</FormErrorMessage>
                </Flex>
              </FormControl>

              <FormControl isInvalid={!!errors.username}>
                <FormLabel variant={'required'}>{t('createAccount.form.username')}</FormLabel>
                <Input
                  p={'6'}
                  name="username"
                  type="text"
                  value={formData.username}
                  onChange={handleInputChange}
                  placeholder={t('createAccount.form.usernamePlaceholder')}
                  // variant={'default'}
                />
                <FormErrorMessage>{errors.username}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!errors.groupname}>
                <FormLabel>{t('createAccount.form.groupName')}</FormLabel>
                <Input
                  p={'6'}
                  name="groupname"
                  type="text"
                  value={formData.groupname}
                  onChange={handleInputChange}
                  placeholder={t('createAccount.form.groupNamePlaceholder')}
                />
                <FormErrorMessage>{errors.groupname}</FormErrorMessage>
              </FormControl>
            </Stack>
          )}

          {activeStep === 1 && (
            <Stack gap="4" align="flex-start">
              <FormControl isInvalid={!!errors.profileIcon}>
                <Text>{t('createAccount.form.storeIcon')}</Text>
                <Flex alignItems={'center'} mt={'.5rem'}>
                  <Image
                    style={{ borderRadius: '100rem', marginRight: '10px' }}
                    src="/imgs/icons/tempProfile.png"
                    alt=""
                    width={60}
                    height={60}
                  />

                  <FormLabel
                    fontSize={'small'}
                    margin={'0'}
                    ml={'.5rem'}
                    paddingX={'4'}
                    paddingY={'2'}
                    borderRadius={'50px'}
                    border={'1px solid black'}
                  >
                    {t('createAccount.form.changePhoto')}
                  </FormLabel>
                  <Input
                    name="profileIcon"
                    type="file"
                    value={formData.profileIcon}
                    onChange={handleInputChange}
                    display={'none'}
                  />
                  <FormErrorMessage>{errors.profileIcon}</FormErrorMessage>
                </Flex>
              </FormControl>

              <FormControl isInvalid={!!errors.username}>
                <FormLabel variant={'required'}>{t('createAccount.form.storeName')}</FormLabel>
                <Input
                  p={'6'}
                  name="username"
                  type="text"
                  value={formData.username}
                  onChange={handleInputChange}
                  placeholder={t('createAccount.form.storeNamePlaceholder')}
                  // variant={'default'}
                />
                <FormErrorMessage>{errors.username}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!errors.storeID}>
                <FormLabel variant={'required'}>{t('createAccount.form.storeId')}</FormLabel>
                <Flex gap={0}>
                  <FormLabel
                    bg={'#F8FAFC'}
                    color={'#94B2D7'}
                    fontSize={'sm'}
                    m={0}
                    px={4}
                    borderLeftRadius={'10px'}
                    border={'1px solid #E5EAF1'}
                    display={'flex'}
                    alignItems={'center'}
                  >
                    xxx.com/
                  </FormLabel>

                  <Input
                    p={'6'}
                    name="storeID"
                    type="text"
                    value={formData.storeID}
                    onChange={handleInputChange}
                    placeholder={t('createAccount.form.storeIdPlaceholder')}
                    borderLeftRadius={0}
                  />
                </Flex>
                <FormErrorMessage>{errors.storeOverview}</FormErrorMessage>
                <Text variant={'errorText'}>{t('createAccount.form.storeIdNote')}</Text>
              </FormControl>
              <FormControl isInvalid={!!errors.storeOverview}>
                <FormLabel>{t('createAccount.form.storeOverview')}</FormLabel>
                <Input
                  p={'6'}
                  name="storeOverview"
                  type="text"
                  value={formData.storeOverview}
                  onChange={handleInputChange}
                  placeholder={t('createAccount.form.storeOverviewPlaceholder')}
                />
                <FormErrorMessage>{errors.groupname}</FormErrorMessage>
              </FormControl>
            </Stack>
          )}

          {activeStep === 2 && (
            <Stack gap="3" align="flex-start">
              <Text fontSize={'sm'}>{t('createAccount.form.storeOverview')}</Text>
              <Button
                display={'flex'}
                flexDir={'row'}
                justifyContent={'space-between'}
                variant={'rounded'}
              >
                <Flex alignItems={'center'}>
                  <GoogleIcon width={25} height={25} />
                  <Text ml={'1rem'} fontSize={'1.1rem'}>
                    {t('createAccount.form.googleCalendar')}
                  </Text>
                </Flex>
                <Text fontSize={'xs'} color={'blue'} fontWeight={'normal'}>
                  {t('createAccount.form.connect')}
                </Text>
              </Button>
            </Stack>
          )}

          {activeStep === 3 && (
            <Stack mb={'2rem'} gap="4" align="flex-start">
              {/* Monday */}
              <Flex
                flexDirection={{ base: 'column', md: 'row' }}
                gap={'.5rem'}
                w={'100%'}
                justifyContent={{ base: 'flex-start', md: 'space-between' }}
              >
                <Flex w={'16rem'} alignItems={'center'} gap={2}>
                  <ToggleButton
                    isActive={formData.availability.monday}
                    onChange={() =>
                      setFormData({
                        ...formData,
                        availability: {
                          ...formData.availability,
                          monday: !formData.availability.monday,
                        },
                      })
                    }
                  />
                  <Text fontSize={'sm'} fontWeight={700}>
                    {t('common:calendar.monday')}
                  </Text>
                </Flex>
                <Flex direction="column" justifyContent={'center'} w={'100%'} gap={2}>
                  {formData.availability.monday ? (
                    formData.availability.timeSlots.monday.map((_, index) => (
                      <Flex key={`monday-${index}`} gap={2} alignItems="center">
                        <TimeSelect width={'100%'} />

                        {index === 0 && formData.availability.monday ? (
                          <Button
                            size="sm"
                            ml={0}
                            p={1}
                            variant="ghost"
                            onClick={() => addTimeSlot('monday')}
                          >
                            <PlusIcon />
                          </Button>
                        ) : (
                          index > 0 && (
                            <Button
                              size="sm"
                              ml={0}
                              p={1}
                              variant="ghost"
                              onClick={() => removeTimeSlot('monday', index)}
                            >
                              <TrashIcon />
                            </Button>
                          )
                        )}
                      </Flex>
                    ))
                  ) : (
                    <Text>{t('common:calendar.closedDay')}</Text>
                  )}
                </Flex>
              </Flex>

              {/* Tuesday */}
              <Flex
                flexDirection={{ base: 'column', md: 'row' }}
                gap={'.5rem'}
                w={'100%'}
                justifyContent={{ base: 'flex-start', md: 'space-between' }}
              >
                <Flex w={'16rem'} alignItems={'center'} gap={2}>
                  <ToggleButton
                    isActive={formData.availability.tuesday}
                    onChange={() =>
                      setFormData({
                        ...formData,
                        availability: {
                          ...formData.availability,
                          tuesday: !formData.availability.tuesday,
                        },
                      })
                    }
                  />
                  <Text fontSize={'sm'} fontWeight={700}>
                    {t('common:calendar.tuesday')}
                  </Text>
                </Flex>
                <Flex direction="column" justifyContent={'center'} w={'100%'} gap={2}>
                  {formData.availability.tuesday ? (
                    formData.availability.timeSlots.tuesday.map((_, index) => (
                      <Flex key={`tuesday-${index}`} gap={2} alignItems="center">
                        <TimeSelect width={'100%'} />

                        {index === 0 && formData.availability.tuesday ? (
                          <Button
                            size="sm"
                            ml={0}
                            p={1}
                            variant="ghost"
                            onClick={() => addTimeSlot('tuesday')}
                          >
                            <PlusIcon />
                          </Button>
                        ) : (
                          index > 0 && (
                            <Button
                              size="sm"
                              ml={0}
                              p={1}
                              variant="ghost"
                              onClick={() => removeTimeSlot('tuesday', index)}
                            >
                              <TrashIcon />
                            </Button>
                          )
                        )}
                      </Flex>
                    ))
                  ) : (
                    <Text>{t('common:calendar.closedDay')}</Text>
                  )}
                </Flex>
              </Flex>

              {/* Wednesday */}
              <Flex
                flexDirection={{ base: 'column', md: 'row' }}
                gap={'.5rem'}
                w={'100%'}
                justifyContent={{ base: 'flex-start', md: 'space-between' }}
              >
                <Flex w={'16rem'} alignItems={'center'} gap={2}>
                  <ToggleButton
                    isActive={formData.availability.wednesday}
                    onChange={() =>
                      setFormData({
                        ...formData,
                        availability: {
                          ...formData.availability,
                          wednesday: !formData.availability.wednesday,
                        },
                      })
                    }
                  />
                  <Text fontSize={'sm'} fontWeight={700}>
                    {t('common:calendar.wednesday')}
                  </Text>
                </Flex>
                <Flex direction="column" justifyContent={'center'} w={'100%'} gap={2}>
                  {formData.availability.wednesday ? (
                    formData.availability.timeSlots.wednesday.map((_, index) => (
                      <Flex key={`wednesday-${index}`} gap={2} alignItems="center">
                        <TimeSelect width={'100%'} />

                        {index === 0 && formData.availability.wednesday ? (
                          <Button
                            size="sm"
                            ml={0}
                            p={1}
                            variant="ghost"
                            onClick={() => addTimeSlot('wednesday')}
                          >
                            <PlusIcon />
                          </Button>
                        ) : (
                          index > 0 && (
                            <Button
                              size="sm"
                              ml={0}
                              p={1}
                              variant="ghost"
                              onClick={() => removeTimeSlot('wednesday', index)}
                            >
                              <TrashIcon />
                            </Button>
                          )
                        )}
                      </Flex>
                    ))
                  ) : (
                    <Text>{t('common:calendar.closedDay')}</Text>
                  )}
                </Flex>
              </Flex>

              {/* Thursday */}
              <Flex
                flexDirection={{ base: 'column', md: 'row' }}
                gap={'.5rem'}
                w={'100%'}
                justifyContent={{ base: 'flex-start', md: 'space-between' }}
              >
                <Flex w={'16rem'} alignItems={'center'} gap={2}>
                  <ToggleButton
                    isActive={formData.availability.thursday}
                    onChange={() =>
                      setFormData({
                        ...formData,
                        availability: {
                          ...formData.availability,
                          thursday: !formData.availability.thursday,
                        },
                      })
                    }
                  />
                  <Text fontSize={'sm'} fontWeight={700}>
                    {t('common:calendar.thursday')}
                  </Text>
                </Flex>
                <Flex direction="column" justifyContent={'center'} w={'100%'} gap={2}>
                  {formData.availability.thursday ? (
                    formData.availability.timeSlots.thursday.map((_, index) => (
                      <Flex key={`thursday-${index}`} gap={2} alignItems="center">
                        <TimeSelect width={'100%'} />

                        {index === 0 && formData.availability.thursday ? (
                          <Button
                            size="sm"
                            ml={0}
                            p={1}
                            variant="ghost"
                            onClick={() => addTimeSlot('thursday')}
                          >
                            <PlusIcon />
                          </Button>
                        ) : (
                          index > 0 && (
                            <Button
                              size="sm"
                              ml={0}
                              p={1}
                              variant="ghost"
                              onClick={() => removeTimeSlot('thursday', index)}
                            >
                              <TrashIcon />
                            </Button>
                          )
                        )}
                      </Flex>
                    ))
                  ) : (
                    <Text>{t('common:calendar.closedDay')}</Text>
                  )}
                </Flex>
              </Flex>

              {/* Friday */}
              <Flex
                flexDirection={{ base: 'column', md: 'row' }}
                gap={'.5rem'}
                w={'100%'}
                justifyContent={{ base: 'flex-start', md: 'space-between' }}
              >
                <Flex w={'16rem'} alignItems={'center'} gap={2}>
                  <ToggleButton
                    isActive={formData.availability.friday}
                    onChange={() =>
                      setFormData({
                        ...formData,
                        availability: {
                          ...formData.availability,
                          friday: !formData.availability.friday,
                        },
                      })
                    }
                  />
                  <Text fontSize={'sm'} fontWeight={700}>
                    {t('common:calendar.friday')}
                  </Text>
                </Flex>
                <Flex direction="column" justifyContent={'center'} w={'100%'} gap={2}>
                  {formData.availability.friday ? (
                    formData.availability.timeSlots.friday.map((_, index) => (
                      <Flex key={`friday-${index}`} gap={2} alignItems="center">
                        <TimeSelect width={'100%'} />

                        {index === 0 && formData.availability.friday ? (
                          <Button
                            size="sm"
                            ml={0}
                            p={1}
                            variant="ghost"
                            onClick={() => addTimeSlot('friday')}
                          >
                            <PlusIcon />
                          </Button>
                        ) : (
                          index > 0 && (
                            <Button
                              size="sm"
                              ml={0}
                              p={1}
                              variant="ghost"
                              onClick={() => removeTimeSlot('friday', index)}
                            >
                              <TrashIcon />
                            </Button>
                          )
                        )}
                      </Flex>
                    ))
                  ) : (
                    <Text>{t('common:calendar.closedDay')}</Text>
                  )}
                </Flex>
              </Flex>

              {/* Saturday */}
              <Flex
                flexDirection={{ base: 'column', md: 'row' }}
                gap={'.5rem'}
                w={'100%'}
                justifyContent={{ base: 'flex-start', md: 'space-between' }}
              >
                <Flex w={'16rem'} alignItems={'center'} gap={2}>
                  <ToggleButton
                    isActive={formData.availability.saturday}
                    onChange={() =>
                      setFormData({
                        ...formData,
                        availability: {
                          ...formData.availability,
                          saturday: !formData.availability.saturday,
                        },
                      })
                    }
                  />
                  <Text fontSize={'sm'} fontWeight={700}>
                    {t('common:calendar.saturday')}
                  </Text>
                </Flex>
                <Flex direction="column" justifyContent={'center'} w={'100%'} gap={2}>
                  {formData.availability.saturday ? (
                    formData.availability.timeSlots.saturday.map((_, index) => (
                      <Flex key={`saturday-${index}`} gap={2} alignItems="center">
                        <TimeSelect width={'100%'} />

                        {index === 0 && formData.availability.saturday ? (
                          <Button
                            size="sm"
                            ml={0}
                            p={1}
                            variant="ghost"
                            onClick={() => addTimeSlot('saturday')}
                          >
                            <PlusIcon />
                          </Button>
                        ) : (
                          index > 0 && (
                            <Button
                              size="sm"
                              ml={0}
                              p={1}
                              variant="ghost"
                              onClick={() => removeTimeSlot('saturday', index)}
                            >
                              <TrashIcon />
                            </Button>
                          )
                        )}
                      </Flex>
                    ))
                  ) : (
                    <Text>{t('common:calendar.closedDay')}</Text>
                  )}
                </Flex>
              </Flex>

              {/* Sunday */}
              <Flex
                flexDirection={{ base: 'column', md: 'row' }}
                gap={'.5rem'}
                w={'100%'}
                justifyContent={{ base: 'flex-start', md: 'space-between' }}
              >
                <Flex w={'16rem'} alignItems={'center'} gap={2}>
                  <ToggleButton
                    isActive={formData.availability.sunday}
                    onChange={() =>
                      setFormData({
                        ...formData,
                        availability: {
                          ...formData.availability,
                          sunday: !formData.availability.sunday,
                        },
                      })
                    }
                  />
                  <Text fontSize={'sm'} fontWeight={700}>
                    {t('common:calendar.sunday')}
                  </Text>
                </Flex>
                <Flex direction="column" justifyContent={'center'} w={'100%'} gap={2}>
                  {formData.availability.sunday ? (
                    formData.availability.timeSlots.sunday.map((_, index) => (
                      <Flex key={`sunday-${index}`} gap={2} alignItems="center">
                        <TimeSelect width={'100%'} />

                        {index === 0 && formData.availability.sunday ? (
                          <Button
                            size="sm"
                            ml={0}
                            p={1}
                            variant="ghost"
                            onClick={() => addTimeSlot('sunday')}
                          >
                            <PlusIcon />
                          </Button>
                        ) : (
                          index > 0 && (
                            <Button
                              size="sm"
                              ml={0}
                              p={1}
                              variant="ghost"
                              onClick={() => removeTimeSlot('sunday', index)}
                            >
                              <TrashIcon />
                            </Button>
                          )
                        )}
                      </Flex>
                    ))
                  ) : (
                    <Text>{t('common:calendar.closedDay')}</Text>
                  )}
                </Flex>
              </Flex>
            </Stack>
          )}

          <Flex mt={'1rem'} flexDirection={'column'} gap={'1rem'}>
            {activeStep < steps.length - 1 ? (
              <Button onClick={handleNext} variant="roundedBlue" ml={activeStep === 0 ? 'auto' : 0}>
                {t('createAccount.buttons.next')}
              </Button>
            ) : (
              <Button type="submit" variant="roundedBlue" ml={activeStep === 0 ? 'auto' : 0}>
                {t('createAccount.buttons.complete')}
              </Button>
            )}
            {activeStep > 0 && (
              <Button onClick={handleBack} variant="RoundedOutline">
                {t('createAccount.buttons.back')}
              </Button>
            )}
          </Flex>
        </form>
      </Box>
    </Flex>
  );
}
