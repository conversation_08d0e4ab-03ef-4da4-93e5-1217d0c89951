import React from 'react';
import { Flex, Text, Image, Button, FormControl, FormLabel, Input } from '@chakra-ui/react';
import AddImage from '@/assets/icons/addImage';
import XIcon from '@/assets/icons/xIcon';

export default function ImageInput({
  src,
  label,
  variant,
  page,
}: {
  src?: string[];
  label?: string;
  variant?: string;
  page?: 'add' | 'edit';
}) {
  return variant === 'bottom-label' ? (
    <Flex pt={'3rem'} justifyContent={'center'}>
      <FormControl
        flexDirection={'column'}
        display={'flex'}
        justifyContent={'center'}
        alignItems={'center'}
      >
        {page === 'add' ? (
          <FormLabel
            position={'relative'}
            border={'2px dashed rgb(19, 97, 206)'}
            borderRadius={'10px'}
            px={{ base: 0, md: 3 }}
            py={0}
            m={0}
            placeholder="画像を追加"
            display={'flex'}
            flexDirection={'column'}
            gap={4}
            alignItems={'center'}
            justifyContent={'center'}
            h={{ base: '12rem', md: '15rem' }}
            w={{ base: '18rem', md: '21rem' }}
          >
            <AddImage width={30} height={30} />

            <Text fontSize={'md'} fontWeight={'bold'}>
              画像を追加
            </Text>
          </FormLabel>
        ) : (
          <Flex
            bg={'red.100'}
            position={'relative'}
            overflow={'hidden'}
            borderRadius={'10px'}
            height={{ base: '12rem', md: '15rem' }}
            w={{ base: '15rem', md: '21rem' }}
          >
            <Image
              style={{ objectFit: 'cover', width: '100%' }}
              src={'/imgs/icons/Placeholder.svg'}
              alt="option"
            />
            <Flex
              position={'absolute'}
              top={{ base: '.06rem', md: '.5rem' }}
              right={{ base: '.06rem', md: '.5rem' }}
            >
              <Button p={0} borderRadius={'100%'} top={'1rem'} right={'1rem'}>
                <XIcon height={20} width={20} />
              </Button>
            </Flex>
          </Flex>
        )}
        <Input
          border={'1px solid #E5EAF1'}
          type="file"
          visibility={'hidden'}
          m={0}
          p={0}
          h={0}
          w={0}
        />

        <Text textAlign={'center'}>カバー画像</Text>
      </FormControl>
    </Flex>
  ) : (
    <Flex flexDirection={'column'} gap={2}>
      {label && (
        <Flex>
          <Text fontSize={'sm'} fontWeight={'bold'}>
            {label}
          </Text>
        </Flex>
      )}

      <Flex
        gap={2}
        flexDirection={{ base: 'column', sm: 'row' }}
        borderBottom={'1px solid #E5EAF1'}
        pb={'1rem'}
        mb={'1rem'}
      >
        {src?.map((src, index) => (
          <Flex key={index} flexDirection={'column'} w={{ base: '100%', md: '24rem' }} gap={2}>
            <Flex position={'relative'} overflow={'hidden'} borderRadius={'10px'} height={'12rem'}>
              <Image
                style={{ objectFit: 'cover', width: '100%' }}
                src={src}
                alt="option"
                width={180}
                height={'100%'}
              />
              <Button p={0} borderRadius={'100%'} position={'absolute'} top={'1rem'} right={'1rem'}>
                <XIcon height={20} width={20} />
              </Button>
            </Flex>
          </Flex>
        ))}
        <Flex
          flexDirection={'row'}
          w={{ base: '100%', md: '24rem' }}
          h={{ base: '12rem' }}
          gap={2}
          // pt={{ base: 0, sm: '1.7rem' }}
        >
          <FormControl h={'100%'} position={'relative'}>
            <FormLabel
              position={'absolute'}
              bottom={0}
              top={0}
              w={'100%'}
              h={'12rem'}
              border={'2px dashed rgb(19, 97, 206)'}
              borderRadius={'10px'}
              placeholder="画像を追加"
              display={'flex'}
              flexDirection={'column'}
              gap={4}
              alignItems={'center'}
              justifyContent={'center'}
              m={0}
              p={0}
            >
              <AddImage width={30} height={30} />

              <Text fontSize={'md'} fontWeight={'bold'}>
                画像を追加
              </Text>
            </FormLabel>
            <Input
              border={'1px solid #E5EAF1'}
              type="file"
              visibility={'hidden'}
              m={0}
              p={0}
              h={0}
              w={0}
            />
          </FormControl>

          <FormControl
            display={{ base: 'flex', sm: 'none' }}
            h={'12rem'}
            // w={{ base: '100%', md: '24rem' }}
            position={'relative'}
          >
            <FormLabel
              position={'absolute'}
              bottom={0}
              top={0}
              w={'100%'}
              border={'2px dashed rgb(19, 97, 206)'}
              borderRadius={'10px'}
              placeholder="画像を追加"
              display={'flex'}
              flexDirection={'column'}
              gap={4}
              alignItems={'center'}
              justifyContent={'center'}
              m={0}
              p={0}
            >
              <AddImage width={30} height={30} />

              <Text fontSize={'md'} fontWeight={'bold'}>
                画像を追加
              </Text>
            </FormLabel>
            <Input
              border={'1px solid #E5EAF1'}
              type="file"
              visibility={'hidden'}
              m={0}
              p={0}
              h={0}
              w={0}
            />
          </FormControl>
        </Flex>
      </Flex>
    </Flex>
  );
}
