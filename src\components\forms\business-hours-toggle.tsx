import React from 'react';
import { Flex, Button, Text, Stack, Image } from '@chakra-ui/react';
import { ToggleButton } from '../ui/formfields/toggle-button';
import TimeSelect from '../ui/formfields/time-select';

function BusinessHoursToggle({
  formData,
  setFormData,
  addTimeSlot,
  removeTimeSlot,
}: {
  formData: any;
  setFormData: any;
  addTimeSlot: any;
  removeTimeSlot: any;
}) {
  return (
    <Stack gap="4" align="flex-start">
      {/* Monday */}
      <Flex alignItems={'flex-start'} w={'100%'} justifyContent={'space-between'}>
        <Flex alignItems={'center'} gap={2}>
          <ToggleButton
            isActive={formData.availability.monday}
            onChange={() =>
              setFormData({
                ...formData,
                availability: {
                  ...formData.availability,
                  monday: !formData.availability.monday,
                },
              })
            }
          />
          <Text fontSize={'sm'} fontWeight={700}>
            月曜日
          </Text>
        </Flex>
        <Flex direction="column" justifyContent={'center'} w={'57%'} gap={2}>
          {formData.availability.monday ? (
            formData.availability.timeSlots.monday.map((_: any, index: number) => (
              <Flex key={`monday-${index}`} gap={4} alignItems="center">
                <TimeSelect />

                {index === 0 && formData.availability.monday ? (
                  <Button
                    size="sm"
                    ml={2}
                    p={1}
                    variant="ghost"
                    onClick={() => addTimeSlot('monday')}
                  >
                    <Image src="/imgs/icons/plus.svg" alt="Delete" width={18} height={18} />
                  </Button>
                ) : (
                  index > 0 && (
                    <Button
                      size="sm"
                      ml={2}
                      p={1}
                      variant="ghost"
                      onClick={() => removeTimeSlot('monday', index)}
                    >
                      <Image src="/imgs/icons/trash.svg" alt="Delete" width={18} height={18} />
                    </Button>
                  )
                )}
              </Flex>
            ))
          ) : (
            <Text>定休日</Text>
          )}
        </Flex>
      </Flex>

      {/* Tuesday */}
      <Flex alignItems={'flex-start'} w={'100%'} justifyContent={'space-between'}>
        <Flex alignItems={'center'} gap={2}>
          <ToggleButton
            isActive={formData.availability.tuesday}
            onChange={() =>
              setFormData({
                ...formData,
                availability: {
                  ...formData.availability,
                  tuesday: !formData.availability.tuesday,
                },
              })
            }
          />
          <Text fontSize={'sm'} fontWeight={700}>
            火曜日
          </Text>
        </Flex>
        <Flex direction="column" justifyContent={'center'} w={'57%'} gap={2}>
          {formData.availability.tuesday ? (
            formData.availability.timeSlots.tuesday.map((_: any, index: number) => (
              <Flex key={`tuesday-${index}`} gap={4} alignItems="center">
                <TimeSelect />

                {index === 0 && formData.availability.tuesday ? (
                  <Button
                    size="sm"
                    ml={2}
                    p={1}
                    variant="ghost"
                    onClick={() => addTimeSlot('tuesday')}
                  >
                    <Image src="/imgs/icons/plus.svg" alt="Add" width={18} height={18} />
                  </Button>
                ) : (
                  index > 0 && (
                    <Button
                      size="sm"
                      ml={2}
                      p={1}
                      variant="ghost"
                      onClick={() => removeTimeSlot('tuesday', index)}
                    >
                      <Image src="/imgs/icons/trash.svg" alt="Delete" width={18} height={18} />
                    </Button>
                  )
                )}
              </Flex>
            ))
          ) : (
            <Text>定休日</Text>
          )}
        </Flex>
      </Flex>

      {/* Wednesday */}
      <Flex alignItems={'flex-start'} w={'100%'} justifyContent={'space-between'}>
        <Flex alignItems={'center'} gap={2}>
          <ToggleButton
            isActive={formData.availability.wednesday}
            onChange={() =>
              setFormData({
                ...formData,
                availability: {
                  ...formData.availability,
                  wednesday: !formData.availability.wednesday,
                },
              })
            }
          />
          <Text fontSize={'sm'} fontWeight={700}>
            水曜日
          </Text>
        </Flex>
        <Flex direction="column" justifyContent={'center'} w={'57%'} gap={2}>
          {formData.availability.wednesday ? (
            formData.availability.timeSlots.wednesday.map((_: any, index: number) => (
              <Flex key={`wednesday-${index}`} gap={4} alignItems="center">
                <TimeSelect />

                {index === 0 && formData.availability.wednesday ? (
                  <Button
                    size="sm"
                    ml={2}
                    p={1}
                    variant="ghost"
                    onClick={() => addTimeSlot('wednesday')}
                  >
                    <Image src="/imgs/icons/plus.svg" alt="Add" width={18} height={18} />
                  </Button>
                ) : (
                  index > 0 && (
                    <Button
                      size="sm"
                      ml={2}
                      p={1}
                      variant="ghost"
                      onClick={() => removeTimeSlot('wednesday', index)}
                    >
                      <Image src="/imgs/icons/trash.svg" alt="Delete" width={18} height={18} />
                    </Button>
                  )
                )}
              </Flex>
            ))
          ) : (
            <Text>定休日</Text>
          )}
        </Flex>
      </Flex>

      {/* Thursday */}
      <Flex alignItems={'flex-start'} w={'100%'} justifyContent={'space-between'}>
        <Flex alignItems={'center'} gap={2}>
          <ToggleButton
            isActive={formData.availability.thursday}
            onChange={() =>
              setFormData({
                ...formData,
                availability: {
                  ...formData.availability,
                  thursday: !formData.availability.thursday,
                },
              })
            }
          />
          <Text fontSize={'sm'} fontWeight={700}>
            木曜日
          </Text>
        </Flex>
        <Flex direction="column" justifyContent={'center'} w={'57%'} gap={2}>
          {formData.availability.thursday ? (
            formData.availability.timeSlots.thursday.map((_: any, index: number) => (
              <Flex key={`thursday-${index}`} gap={4} alignItems="center">
                <TimeSelect />

                {index === 0 && formData.availability.thursday ? (
                  <Button
                    size="sm"
                    ml={2}
                    p={1}
                    variant="ghost"
                    onClick={() => addTimeSlot('thursday')}
                  >
                    <Image src="/imgs/icons/plus.svg" alt="Add" width={18} height={18} />
                  </Button>
                ) : (
                  index > 0 && (
                    <Button
                      size="sm"
                      ml={2}
                      p={1}
                      variant="ghost"
                      onClick={() => removeTimeSlot('thursday', index)}
                    >
                      <Image src="/imgs/icons/trash.svg" alt="Delete" width={18} height={18} />
                    </Button>
                  )
                )}
              </Flex>
            ))
          ) : (
            <Text>定休日</Text>
          )}
        </Flex>
      </Flex>

      {/* Friday */}
      <Flex alignItems={'flex-start'} w={'100%'} justifyContent={'space-between'}>
        <Flex alignItems={'center'} gap={2}>
          <ToggleButton
            isActive={formData.availability.friday}
            onChange={() =>
              setFormData({
                ...formData,
                availability: {
                  ...formData.availability,
                  friday: !formData.availability.friday,
                },
              })
            }
          />
          <Text fontSize={'sm'} fontWeight={700}>
            金曜日
          </Text>
        </Flex>
        <Flex direction="column" justifyContent={'center'} w={'57%'} gap={2}>
          {formData.availability.friday ? (
            formData.availability.timeSlots.friday.map((_: any, index: number) => (
              <Flex key={`friday-${index}`} gap={4} alignItems="center">
                <TimeSelect />

                {index === 0 && formData.availability.friday ? (
                  <Button
                    size="sm"
                    ml={2}
                    p={1}
                    variant="ghost"
                    onClick={() => addTimeSlot('friday')}
                  >
                    <Image src="/imgs/icons/plus.svg" alt="Add" width={18} height={18} />
                  </Button>
                ) : (
                  index > 0 && (
                    <Button
                      size="sm"
                      ml={2}
                      p={1}
                      variant="ghost"
                      onClick={() => removeTimeSlot('friday', index)}
                    >
                      <Image src="/imgs/icons/trash.svg" alt="Delete" width={18} height={18} />
                    </Button>
                  )
                )}
              </Flex>
            ))
          ) : (
            <Text>定休日</Text>
          )}
        </Flex>
      </Flex>

      {/* Saturday */}
      <Flex alignItems={'flex-start'} w={'100%'} justifyContent={'space-between'}>
        <Flex alignItems={'center'} gap={2}>
          <ToggleButton
            isActive={formData.availability.saturday}
            onChange={() =>
              setFormData({
                ...formData,
                availability: {
                  ...formData.availability,
                  saturday: !formData.availability.saturday,
                },
              })
            }
          />
          <Text fontSize={'sm'} fontWeight={700}>
            土曜日
          </Text>
        </Flex>
        <Flex direction="column" justifyContent={'center'} w={'57%'} gap={2}>
          {formData.availability.saturday ? (
            formData.availability.timeSlots.saturday.map((_: any, index: number) => (
              <Flex key={`saturday-${index}`} gap={4} alignItems="center">
                <TimeSelect />

                {index === 0 && formData.availability.saturday ? (
                  <Button
                    size="sm"
                    ml={2}
                    p={1}
                    variant="ghost"
                    onClick={() => addTimeSlot('saturday')}
                  >
                    <Image src="/imgs/icons/plus.svg" alt="Add" width={18} height={18} />
                  </Button>
                ) : (
                  index > 0 && (
                    <Button
                      size="sm"
                      ml={2}
                      p={1}
                      variant="ghost"
                      onClick={() => removeTimeSlot('saturday', index)}
                    >
                      <Image src="/imgs/icons/trash.svg" alt="Delete" width={18} height={18} />
                    </Button>
                  )
                )}
              </Flex>
            ))
          ) : (
            <Text>定休日</Text>
          )}
        </Flex>
      </Flex>

      {/* Sunday */}
      <Flex alignItems={'flex-start'} w={'100%'} justifyContent={'space-between'}>
        <Flex alignItems={'center'} gap={2}>
          <ToggleButton
            isActive={formData.availability.sunday}
            onChange={() =>
              setFormData({
                ...formData,
                availability: {
                  ...formData.availability,
                  sunday: !formData.availability.sunday,
                },
              })
            }
          />
          <Text fontSize={'sm'} fontWeight={700}>
            日曜日
          </Text>
        </Flex>
        <Flex direction="column" justifyContent={'center'} w={'57%'} gap={2}>
          {formData.availability.sunday ? (
            formData.availability.timeSlots.sunday.map((_: any, index: number) => (
              <Flex key={`sunday-${index}`} gap={4} alignItems="center">
                <TimeSelect />

                {index === 0 && formData.availability.sunday ? (
                  <Button
                    size="sm"
                    ml={2}
                    p={1}
                    variant="ghost"
                    onClick={() => addTimeSlot('sunday')}
                  >
                    <Image src="/imgs/icons/plus.svg" alt="Add" width={18} height={18} />
                  </Button>
                ) : (
                  index > 0 && (
                    <Button
                      size="sm"
                      ml={2}
                      p={1}
                      variant="ghost"
                      onClick={() => removeTimeSlot('sunday', index)}
                    >
                      <Image src="/imgs/icons/trash.svg" alt="Delete" width={18} height={18} />
                    </Button>
                  )
                )}
              </Flex>
            ))
          ) : (
            <Text>定休日</Text>
          )}
        </Flex>
      </Flex>
    </Stack>
  );
}

export default BusinessHoursToggle;
