import { Button, HStack } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';

const LanguageSwitcher = () => {
  const router = useRouter();
  const { i18n } = useTranslation();

  const changeLanguage = (locale: string) => {
    router.push(router.pathname, router.asPath, { locale });
  };

  return (
    <HStack spacing={2}>
      <Button
        size="sm"
        variant={i18n.language === 'en' ? 'solid' : 'outline'}
        onClick={() => changeLanguage('en')}
      >
        EN
      </Button>
      <Button
        size="sm"
        variant={i18n.language === 'ja' ? 'solid' : 'outline'}
        onClick={() => changeLanguage('ja')}
      >
        JP
      </Button>
    </HStack>
  );
};

export default LanguageSwitcher;
