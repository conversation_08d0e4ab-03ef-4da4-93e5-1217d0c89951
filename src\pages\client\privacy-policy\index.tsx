import CardContainer from '@/components/ui/cards/card-container';
import PrivacyPolicyCard from '@/components/ui/privacy-policy-card';
import PrivacyPolicyCardHeader from '@/components/ui/privacy-policy-card-header';
import { Flex, ListItem, Text, UnorderedList } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

function PrivacyPolicy() {
  const { t } = useTranslation('common');
  return (
    <Flex flexDirection={'column'} gap={'2rem'}>
      <Text variant={'header'}>{t('privacyPolicy.title')}</Text>

      <CardContainer alignItems={'stretch'} justifyContent={'flex-start'}>
        <PrivacyPolicyCard>
          <Flex>
            <PrivacyPolicyCardHeader>
              {t('privacyPolicy.sections.introduction.title')}
            </PrivacyPolicyCardHeader>
          </Flex>

          <Flex w={'100%'} justifyContent={'flex-start'}>
            <Text fontSize={'xs'}>{t('privacyPolicy.sections.introduction.content')}</Text>
          </Flex>
        </PrivacyPolicyCard>

        <PrivacyPolicyCard>
          <Flex>
            <PrivacyPolicyCardHeader>
              {t('privacyPolicy.sections.informationCollection.title')}
            </PrivacyPolicyCardHeader>
          </Flex>

          <Flex flexDirection={'column'} gap={'1rem'} w={'100%'} alignItems={'flex-start'}>
            <Text fontSize={'xs'}>{t('privacyPolicy.sections.informationCollection.content')}</Text>
            <UnorderedList ml={'2rem'} fontSize={'xs'}>
              <ListItem>{t('privacyPolicy.sections.informationCollection.items.contact')}</ListItem>
              <ListItem>
                {t('privacyPolicy.sections.informationCollection.items.reservationHistory')}
              </ListItem>
              <ListItem>
                {t('privacyPolicy.sections.informationCollection.items.locationInfo')}
              </ListItem>
              <ListItem>
                {t('privacyPolicy.sections.informationCollection.items.otherInfo')}
              </ListItem>
            </UnorderedList>
          </Flex>
        </PrivacyPolicyCard>

        <PrivacyPolicyCard>
          <Flex>
            <PrivacyPolicyCardHeader>
              {t('privacyPolicy.sections.usagePurpose.title')}
            </PrivacyPolicyCardHeader>
          </Flex>

          <Flex flexDirection={'column'} gap={'1rem'} w={'100%'} alignItems={'flex-start'}>
            <Text fontSize={'xs'}>{t('privacyPolicy.sections.usagePurpose.content')}</Text>
            <UnorderedList ml={'2rem'} fontSize={'xs'}>
              <ListItem>
                {t('privacyPolicy.sections.usagePurpose.items.reservationService')}
              </ListItem>
              <ListItem>{t('privacyPolicy.sections.usagePurpose.items.customerSupport')}</ListItem>
              <ListItem>
                {t('privacyPolicy.sections.usagePurpose.items.serviceImprovement')}
              </ListItem>
              <ListItem>
                {t('privacyPolicy.sections.usagePurpose.items.legalRequirements')}
              </ListItem>
            </UnorderedList>
          </Flex>
        </PrivacyPolicyCard>

        <PrivacyPolicyCard>
          <Flex>
            <PrivacyPolicyCardHeader>
              {t('privacyPolicy.sections.thirdPartySharing.title')}
            </PrivacyPolicyCardHeader>
          </Flex>

          <Flex w={'100%'} justifyContent={'flex-start'}>
            <Text fontSize={'xs'}>{t('privacyPolicy.sections.thirdPartySharing.content')}</Text>
          </Flex>
        </PrivacyPolicyCard>

        <PrivacyPolicyCard>
          <Flex>
            <PrivacyPolicyCardHeader>
              {t('privacyPolicy.sections.securityManagement.title')}
            </PrivacyPolicyCardHeader>
          </Flex>

          <Flex w={'100%'} justifyContent={'flex-start'}>
            <Text fontSize={'xs'}>{t('privacyPolicy.sections.securityManagement.content')}</Text>
          </Flex>
        </PrivacyPolicyCard>

        <PrivacyPolicyCard>
          <Flex>
            <PrivacyPolicyCardHeader>
              {t('privacyPolicy.sections.dataRights.title')}
            </PrivacyPolicyCardHeader>
          </Flex>

          <Flex w={'100%'} justifyContent={'flex-start'}>
            <Text fontSize={'xs'}>{t('privacyPolicy.sections.dataRights.content')}</Text>
          </Flex>
        </PrivacyPolicyCard>

        <PrivacyPolicyCard>
          <Flex>
            <PrivacyPolicyCardHeader>
              {t('privacyPolicy.sections.contact.title')}
            </PrivacyPolicyCardHeader>
          </Flex>

          <Flex flexDirection={'column'} gap={'.5rem'} w={'100%'} alignItems={'flex-start'}>
            <Text fontSize={'xs'}>{t('privacyPolicy.sections.contact.content')}</Text>
            <Text fontSize={'xs'}>{t('privacyPolicy.sections.contact.email')}</Text>
            <Text fontSize={'xs'}>{t('privacyPolicy.sections.contact.phone')}</Text>
          </Flex>
        </PrivacyPolicyCard>
      </CardContainer>
    </Flex>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'auth'])),
      layoutType: 'client',
    },
  };
}

export default PrivacyPolicy;
