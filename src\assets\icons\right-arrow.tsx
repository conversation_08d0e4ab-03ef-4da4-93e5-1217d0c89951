import React from 'react';

interface RightArrowIconProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

const RightArrowIcon: React.FC<RightArrowIconProps> = ({
  width = 18,
  height = 18,
  color = '#1E293B',
  className,
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask
        id="mask0_4001_17974"
        style={{ maskType: 'alpha' }}
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="24"
        height="24"
      >
        <rect width="24" height="24" fill={color} />
      </mask>
      <g mask="url(#mask0_4001_17974)">
        <path
          d="M12.5998 12L8.6998 8.09999C8.51647 7.91665 8.4248 7.68332 8.4248 7.39999C8.4248 7.11665 8.51647 6.88332 8.6998 6.69999C8.88314 6.51665 9.11647 6.42499 9.3998 6.42499C9.68314 6.42499 9.91647 6.51665 10.0998 6.69999L14.6998 11.3C14.7998 11.4 14.8706 11.5083 14.9123 11.625C14.954 11.7417 14.9748 11.8667 14.9748 12C14.9748 12.1333 14.954 12.2583 14.9123 12.375C14.8706 12.4917 14.7998 12.6 14.6998 12.7L10.0998 17.3C9.91647 17.4833 9.68314 17.575 9.3998 17.575C9.11647 17.575 8.88314 17.4833 8.6998 17.3C8.51647 17.1167 8.4248 16.8833 8.4248 16.6C8.4248 16.3167 8.51647 16.0833 8.6998 15.9L12.5998 12Z"
          fill={color}
        />
      </g>
    </svg>
  );
};

export default RightArrowIcon;
