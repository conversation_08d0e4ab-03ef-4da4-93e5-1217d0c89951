import { clientData } from '@/.temp/sampleClientData';
import EnterIcon from '@/assets/icons/enter-icon';
import Group3Icon from '@/assets/icons/group-3';
import ClientInvitationAddMembersModal from '@/components/modals/client-invitation-add-members-modal';
import EditUserInformationModal from '@/components/modals/EditUserInformationModal';
import LogOutModal from '@/components/modals/logOutModal';
import CardButton from '@/components/ui/cards/card-button';
import CardInfo from '@/components/ui/cards/card-info';
import CardLink from '@/components/ui/cards/card-link';
import CardMyProfileBanner from '@/components/ui/cards/card-my-profile-banner';
import CardSns from '@/components/ui/cards/card-sns';
import { validateForm } from '@/utils/validateMyPageForm';
import { Flex, Text, useDisclosure } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Router from 'next/router';
import { useRef, useState } from 'react';

function MyPage() {
  const { t } = useTranslation('profile');
  const { isOpen: isLogOutOpen, onOpen: onLogOutOpen, onClose: onLogOutClose } = useDisclosure();
  const [errors, setErrors] = useState<Record<string, string>>({});

  const [data, setData] = useState({
    clientData,
  });

  const controlData = useRef({
    clientData: {
      fullName: data?.clientData.fullName,
      kana: data?.clientData.kana,
      phoneNumber: data?.clientData.phoneNumber,
      address: data?.clientData.address,
      email: data?.clientData.email,
    },
  });

  const { isOpen, onOpen, onClose } = useDisclosure();

  const {
    isOpen: isInvitationOpen,
    onOpen: onInvitationOpen,
    onClose: onInvitationClose,
  } = useDisclosure();

  const handleSave = () => {
    if (validateForm(data, setErrors)) {
      controlData.current = {
        clientData: {
          fullName: data.clientData.fullName,
          kana: data.clientData.kana,
          phoneNumber: data.clientData.phoneNumber,
          address: data.clientData.address,
          email: data.clientData.email,
        },
      };
      onClose();
    }
  };

  return (
    <>
      <Flex flexDirection={'column'} gap={'1rem'}>
        <CardMyProfileBanner data={data.clientData} onOpen={onOpen} />

        <Text fontWeight={'bold'}>
          {data ? t('navigation.reservationInfo') : t('navigation.latestReservation')}
        </Text>

        {data ? (
          <CardInfo
            variant={'reservation'}
            text={t('account.creditBalance')}
            href={'#'}
            amount={'1,000'}
          />
        ) : (
          <CardLink variant={'input'} text={t('account.noPaymentInfo')} href={'#'} />
        )}

        <CardLink
          variant={'button'}
          icon={'calendar'}
          text={t('navigation.reservationHistory')}
          href={'/client/reservation-history'}
        />
        <CardLink
          variant={'toggle'}
          icon={'bell'}
          text={t('navigation.notificationSettings')}
          href={'#'}
        />

        <CardInfo
          variant={'credit'}
          text={t('account.creditBalance')}
          href={'/client/my-page/check-credit-charge'}
          amount={'1,000'}
        />

        <CardButton
          gap={'1rem'}
          onClick={() => {
            onInvitationOpen();
          }}
        >
          <EnterIcon height={25} width={25} />
          <Text>{t('navigation.inviteMembers')}</Text>
        </CardButton>

        <CardButton
          gap={'1rem'}
          onClick={() => {
            Router.push('/client/my-page/add-group');
          }}
        >
          <Group3Icon height={25} width={25} />
          <Text>{t('account.group')}</Text>
        </CardButton>

        <CardInfo variant={'payment-info'} text={t('account.noPaymentInfo')} href={'#'} />

        <CardSns />

        <CardLink variant={'button'} icon={'faq'} text={t('navigation.faq')} href={'/client/FAQ'} />

        <CardLink
          variant={'button'}
          icon={'privacy'}
          text={t('account.privacyPolicy')}
          href={'/client/privacy-policy'}
        />

        <CardButton gap={'1rem'} onClick={onLogOutOpen}>
          <Text>{t('account.logout')}</Text>
        </CardButton>

        <ClientInvitationAddMembersModal
          isInvitationOpen={isInvitationOpen}
          onInvitationClose={onInvitationClose}
        />

        <EditUserInformationModal
          isOpen={isOpen}
          errors={errors}
          data={data}
          controlData={controlData}
          onClose={onClose}
          setData={setData}
          setErrors={setErrors}
          handleSave={handleSave}
        />
      </Flex>
      <LogOutModal isOpen={isLogOutOpen} onClose={onLogOutClose} context="client" />
    </>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'profile', 'auth'])),
      layoutType: 'client',
    },
  };
}

export default MyPage;
