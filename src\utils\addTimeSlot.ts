import { FormDataType } from "@/types/FormDataType";
import { DayKeyType } from "@/types/dayType";

export const addTimeSlot = ({ 
    day,
    formData,
    setFormData
     }: {
    day: string;
    formData: FormDataType;
    setFormData: (value: FormDataType) => void;
}) => {
    setFormData({
      ...formData,
      availability: {
        ...formData.availability,
        timeSlots: {
          ...formData.availability.timeSlots,
          [day]: [...formData.availability.timeSlots[day as keyof typeof formData.availability.timeSlots], {}],
        },
      },
    });
  };