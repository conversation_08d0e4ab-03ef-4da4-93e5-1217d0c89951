import { Flex, Button, Text, useDisclosure } from '@chakra-ui/react'
import React, { useState } from 'react'
import { useRouter } from 'next/router'
import BackIcon from '@/assets/icons/back';
import TrashIcon from '@/assets/icons/trash';
import StoreBanner from '@/components/ui/store-banner';
import TextPair from '@/components/ui/TextPair';
import { sampleAdminReservationDetails } from '@/.temp/sampleAdminReservationsList';
import DeleteConfirmationModal from '@/components/modals/delete-confirmation-modal';

export default function StoreDetailsPage() {
  const router = useRouter();
  const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();
  const [deleteStep, setDeleteStep] = useState(1);

  const handleDelete = () => {
    console.log('Deleting member');
    setDeleteStep(2); 
  };

  const handleDeleteClose = () => {
    setDeleteStep(1); 
    onDeleteClose();
  };


  return (
    <Flex w={'100%'} flexDirection={"column"} pr={{'base': 0, 'md': 6}} pl={{'base': 0, 'md': 6 , 'lg': 0}}>
      <Flex mb={{'base': '0'}} flexDirection={{'base':'column', 'sm':'row'}} bg={{'base':'white', 'md':'unset'}} px={{'base': 5, 'lg': 0}} justifyContent={{'base':'space-between'}} height={{'base':'unset', 'sm':'5rem'}} w={'100%'}>
        <Flex alignItems={'center'}>
          <Button variant={'ghost'} w={'auto'} p={0} m={0}
          onClick={() => {
            router.back();
          }}>
              <BackIcon width={30} height={30} color="black" className="my-icon-class" />
          </Button>
          <Text variant={'subHeaderLarge'} fontWeight={'bold'}>
            予約内容の詳細
          </Text>
        </Flex>

        <Flex display={{'base':'flex', 'md':'none'}} px={'1rem'} justifyContent={{'base':'space-between', 'sm':'flex-end'}} alignItems={'center'} gap={'1rem'}>
          <Button 
            variant={'rounded'} 
            color={'black'} 
            bg={'borderGray'} 
            fontSize={'xs'} 
            fontWeight={'normal'} 
            gap={2} 
            w={'auto'} 
            onClick={onDeleteOpen}
          >
            <TrashIcon width={20} height={20} color="black" className="my-icon-class" />
            削除
          </Button>
        </Flex>
      </Flex>

      <Flex flexDirection={'column'} bg={'white'} gap={'4rem'} p={'2rem'} pt={'4rem'} borderRadius={'1rem'}>
      <StoreBanner />

        <Flex px={'1rem'} display={{'base':'none', 'md':'flex'}} justifyContent={{'base':'space-between'}} alignItems={'center'} gap={'1rem'}>
          <Text fontWeight={'bold'} variant={'subHeaderMedium'}>
            2025年10月25日(金) 16:00~18:00 のご予約情報
          </Text>

          <Button 
            variant={'rounded'} 
            py={'.5rem'}
            px={'1.5rem'}
            color={'black'} 
            bg={'borderGray'} 
            fontSize={'sm'} 
            fontWeight={'normal'} 
            gap={2} 
            w={'auto'} 
            onClick={onDeleteOpen}
          >
            <TrashIcon width={20} height={20} color="black" className="my-icon-class" />
            削除
          </Button>
        </Flex>

        <Flex px={'1rem'} flexDirection={'column'}>
          {sampleAdminReservationDetails.map((item: { header: string | string[], subtext: string[] }, index: number) => (
            index === 0 ? (
              <Flex key={index} justifyContent={'space-between'} alignItems={'center'} borderBottom={'1px solid #E5EAF1'} mb={'1rem'}>
                <TextPair
                header={item.header}
                subtext={item.subtext}
                borderBottom={'none'}
                />
                <Button variant={'rounded'} fontSize={'sm'} bg={'borderGray'} fontWeight={'normal'} gap={2} w={'auto'} py={'.5rem'} px={'1.5rem'} onClick={() => {
                  router.push('/admin/members-list/member-details-main/1');
                }}>   
                会員詳細
                </Button>
              </Flex>
            ) : (
              <TextPair
                key={index}
                header={item.header}
                subtext={item.subtext}
              />
            )
          ))}
        </Flex>
      </Flex>

      <DeleteConfirmationModal
        isOpen={isDeleteOpen}
        deleteStep={deleteStep}
        handleDelete={handleDelete}
        handleClose={handleDeleteClose}
        redirect={'/admin/reservations-list'}
      />
    </Flex>
  )
}

StoreDetailsPage.getInitialProps = async () => {
  return { layoutType: "withSidebar" };
};
