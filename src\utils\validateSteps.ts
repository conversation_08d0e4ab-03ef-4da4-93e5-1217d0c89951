export  const validateStep = ({ step, formData, setErrors }: { step: number; formData: any; setErrors: any }) => {
    let stepErrors: Record<string, string> = {};
    let isValid = true;

    if (step === 0) {
      if (!formData.username) {
        stepErrors.username = 'メールアドレスを入力してください';
        isValid = false;
      }
      if (!formData.password) {
        stepErrors.password = 'パスワードを入力してください';
        isValid = false;
      } else {
        const minLength = 8;
        const maxLength = 32;
        const hasUpperCase = /[A-Z]/.test(formData.password);
        const hasLowerCase = /[a-z]/.test(formData.password);
        const hasDigit = /\d/.test(formData.password);
        const hasSpecialChar = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(formData.password);

        if (formData.password.length < minLength) {
          stepErrors.password = `パスワードは${minLength}文字以上である必要があります`;
          isValid = false;
        } else if (formData.password.length > maxLength) {
          stepErrors.password = `パスワードは${maxLength}文字以下である必要があります`;
          isValid = false;
        } else if (!(hasUpperCase && hasLowerCase && hasDigit && hasSpecialChar)) {
          stepErrors.password = 'パスワードは大文字、小文字、数字、特殊文字を含む必要があります';
          isValid = false;
        }
      }
      if (formData.password !== formData.confirmPassword) {
        stepErrors.confirmPassword = 'パスワードが一致しません';
        isValid = false;
      }
    } else if (step === 1) {
      if (!formData.firstName) {
        stepErrors.firstName = '名前を入力してください';
        isValid = false;
      }
      if (!formData.lastName) {
        stepErrors.lastName = '姓を入力してください';
        isValid = false;
      }
      if (!formData.phoneNumber) {
        stepErrors.phoneNumber = '電話番号を入力してください';
        isValid = false;
      } else {
        const digitsOnly = formData.phoneNumber.replace(/[^0-9]/g, '');

        const validFormat = /^[0-9+\-() ]*$/.test(formData.phoneNumber);

        const validLength = digitsOnly.length >= 10 && digitsOnly.length <= 15;

        if (!validFormat) {
          stepErrors.phoneNumber = '電話番号は数字と記号(+, -, (, ))のみ使用できます';
          isValid = false;
        } else if (!validLength) {
          stepErrors.phoneNumber = '電話番号は10〜15桁の数字を入力してください';
          isValid = false;
        }
      }
    }

    setErrors(stepErrors);
    return isValid;
};