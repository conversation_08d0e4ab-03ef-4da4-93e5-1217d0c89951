/* eslint-disable react-hooks/rules-of-hooks */
import DropdownFAQ from '@/components/ui/dropdownFAQ';
import { Flex, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useState } from 'react';

function index() {
  const { t } = useTranslation('common');
  const [isOpen1, setIsOpen] = useState(false);
  const [isOpen2, setIsOpen2] = useState(false);
  const [isOpen3, setIsOpen3] = useState(false);
  const [isOpen4, setIsOpen4] = useState(false);
  const [isOpen5, setIsOpen5] = useState(false);
  const [isOpen6, setIsOpen6] = useState(false);

  return (
    <Flex flexDirection={'column'}>
      <Text variant={'header'} py={'1rem'}>
        {t('faq.title')}
      </Text>
      <Flex flexDirection="column" gap="1px" borderRadius="15px">
        <DropdownFAQ
          isOpen={isOpen1}
          question={t('faq.questions.question1.title')}
          answer={t('faq.questions.question1.answer')}
          setIsOpen={setIsOpen}
        />

        <DropdownFAQ
          isOpen={isOpen2}
          question={t('faq.questions.question2.title')}
          answer={t('faq.questions.question2.answer')}
          setIsOpen={setIsOpen2}
        />

        <DropdownFAQ
          isOpen={isOpen3}
          question={t('faq.questions.question3.title')}
          answer={t('faq.questions.question3.answer')}
          setIsOpen={setIsOpen3}
        />

        <DropdownFAQ
          isOpen={isOpen4}
          question={t('faq.questions.question4.title')}
          answer={t('faq.questions.question4.answer')}
          setIsOpen={setIsOpen4}
        />

        <DropdownFAQ
          isOpen={isOpen5}
          question={t('faq.questions.question5.title')}
          answer={t('faq.questions.question5.answer')}
          setIsOpen={setIsOpen5}
        />

        <DropdownFAQ
          isOpen={isOpen6}
          question={t('faq.questions.question6.title')}
          answer={t('faq.questions.question6.answer')}
          setIsOpen={setIsOpen6}
        />
      </Flex>
    </Flex>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'auth'])),
      layoutType: 'client',
    },
  };
}

export default index;
