/**
 * Date utility functions for calendar operations
 */

/**
 * Get the number of days in a month
 */
export const getDaysInMonth = (year: number, month: number): number => {
  return new Date(year, month + 1, 0).getDate();
};

/**
 * Get the day of the week for the first day of the month (0-6, where 0 is Sunday)
 */
export const getFirstDayOfMonth = (year: number, month: number): number => {
  return new Date(year, month, 1).getDay();
};

/**
 * Format a date as YYYY年MM月
 */
export const formatMonthYear = (date: Date): string => {
  return `${date.getFullYear()}年${date.getMonth() + 1}月`;
};

/**
 * Normalize a date by removing the time component
 */
export const normalizeDate = (date: Date): Date => {
  return new Date(date.getFullYear(), date.getMonth(), date.getDate());
};

/**
 * Check if two dates are the same day (ignoring time)
 */
export const isSameDay = (date1: Date, date2: Date): boolean => {
  const normalizedDate1 = normalizeDate(date1);
  const normalizedDate2 = normalizeDate(date2);
  return normalizedDate1.getTime() === normalizedDate2.getTime();
};

/**
 * Check if a date is in a list of dates
 */
export const isDateInList = (date: Date, dateList: Date[]): boolean => {
  return dateList.some(d => isSameDay(date, d));
};

/**
 * Check if a date is before another date
 */
export const isDateBefore = (date: Date, compareDate: Date): boolean => {
  const normalizedDate = normalizeDate(date);
  const normalizedCompareDate = normalizeDate(compareDate);
  return normalizedDate.getTime() < normalizedCompareDate.getTime();
};

/**
 * Check if a date is after another date
 */
export const isDateAfter = (date: Date, compareDate: Date): boolean => {
  const normalizedDate = normalizeDate(date);
  const normalizedCompareDate = normalizeDate(compareDate);
  return normalizedDate.getTime() > normalizedCompareDate.getTime();
};

/**
 * Get a new date for the previous month
 */
export const getPreviousMonthDate = (date: Date): Date => {
  return new Date(date.getFullYear(), date.getMonth() - 1, 1);
};

/**
 * Get a new date for the next month
 */
export const getNextMonthDate = (date: Date): Date => {
  return new Date(date.getFullYear(), date.getMonth() + 1, 1);
};

/**
 * Format a reservation date with day of week and time range
 * @param date The date object or date string
 * @param startTime Start time string (e.g., "20:00")
 * @param endTime End time string (e.g., "22:00")
 * @param t Translation function
 * @returns Formatted date string based on current locale
 */
export const formatReservationDateTime = (
  date: Date | string,
  startTime: string,
  endTime: string,
  t: (key: string, options?: any) => string
): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const month = dateObj.getMonth() + 1;
  const day = dateObj.getDate();

  // Get day of week in current locale
  const dayOfWeekIndex = dateObj.getDay(); // 0 = Sunday, 1 = Monday, etc.
  const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
  const dayOfWeek = t(`calendar.${dayNames[dayOfWeekIndex]}Short`);

  return t('reservations.dateTimeFormat', {
    month,
    day,
    dayOfWeek,
    startTime,
    endTime,
  });
};
