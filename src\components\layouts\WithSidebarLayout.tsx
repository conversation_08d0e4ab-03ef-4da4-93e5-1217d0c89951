import { Flex } from '@chakra-ui/react';
import { ReactNode } from 'react';
import SideBar from '@/components/navigation/sideBar';
import Proto from '../ui/proto';

type WithSidebarLayoutProps = {
  children: ReactNode;
  navConfig: any;
  activePage: string;
  setActivePage: (page: string) => void;
};

export default function WithSidebarLayout({
  children,
  navConfig,
  activePage,
  setActivePage,
}: WithSidebarLayoutProps) {
  return (
    <Flex
      w={'100%'}
      gap={8}
      bg={'LightBackgroundColor'}
      direction="row"
      pl={{ base: '0', lg: '18rem' }}
      pt={{ base: '5rem', lg: '0' }}
      pb={'1rem'}
      minH={'100vh'}
    >
      <SideBar navConfig={navConfig} activePage={activePage} setActivePage={setActivePage} />
      {children}

      <Flex
        position={'fixed'}
        bottom={0}
        w={'100%'}
        justifyContent={'center'}
        alignItems={'center'}
      >
        <Proto />
      </Flex>
    </Flex>
  );
}
