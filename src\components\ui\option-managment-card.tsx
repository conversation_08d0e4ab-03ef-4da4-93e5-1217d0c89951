import PenIcon from '@/assets/icons/pen';
import { OptionListType } from '@/types/optionListType';
import { Button, Flex, GridItem, Image, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import Router from 'next/router';
import OptionManagmentCategory from './option-managment-category';

type OptionManagmentCardProps = {
  option?: OptionListType;
};

function OptionManagmentCard({ option }: OptionManagmentCardProps) {
  const { t } = useTranslation('common');
  return (
    <GridItem display={'flex'} flexDirection={'column'} border={'1px solid #E5EAF1'} p={3} pb={0}>
      <Flex gap={2} flexDirection={{ base: 'column', sm: 'row' }}>
        <Flex borderRadius={'5px'} h={{ base: '5rem', sm: '4rem' }} overflow={'hidden'}>
          <Image
            style={{ objectFit: 'cover', width: '100%', objectPosition: 'center' }}
            src={'/imgs/icons/Placeholder.svg'}
            alt="option"
          />
        </Flex>
        <Flex flexDirection={'column'} gap={2} ml={2}>
          <Text fontSize={{ base: 'sm', sm: 'md' }}>{option?.menuTitle}</Text>
          <Text fontSize={'xs'} fontWeight={'bold'}>
            {t('optionslist.card.priceFormat', {
              price: option?.price,
              people: option?.numberOfPeople,
            })}
          </Text>
        </Flex>
      </Flex>

      <Flex flexDirection={'column'} justifyContent={'space-between'} gap={'1.5rem'} mt={4}>
        <Text
          as="pre"
          fontSize={{ base: 'sm' }}
          fontWeight={'inherit'}
          lineHeight={1.5}
          fontFamily={'inherit'}
          whiteSpace="pre-wrap"
        >
          {option?.menuDetails?.trim()}
        </Text>
        <Flex gap={2} flexWrap={'wrap'}>
          {option?.tag?.map((tag, index) => (
            <OptionManagmentCategory key={`${tag}-${index}`} props={tag} />
          ))}
        </Flex>
      </Flex>

      <Flex
        py={'1rem'}
        justifyContent={'flex-end'}
        alignItems={'flex-end'}
        alignSelf={'auto'}
        height={'100%'}
      >
        <Button
          variant={'roundedBlue'}
          fontSize={'sm'}
          fontWeight={'normal'}
          gap={2}
          w={'auto'}
          p={'1.3rem'}
          onClick={() => {
            Router.push(`/admin/options-list/option-list-edit/${option?.id}`);
          }}
        >
          <PenIcon width={15} height={15} color="white" className="my-icon-class" />
          {t('optionslist.card.editButton')}
        </Button>
      </Flex>
    </GridItem>
  );
}
export default OptionManagmentCard;
