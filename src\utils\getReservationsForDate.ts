import { sampleReservations } from '@/.temp/sampleCalendarReservations';
import { ReservationEvent } from '@/types/ReservationEventType';
import moment from 'moment';

export const getReservationsForDate = (date: Date) => {
  const targetDate = moment(date).format('YYYY-MM-DD');

  const dayReservations = sampleReservations.filter(reservation => {
    const reservationDate = moment(reservation.start).format('YYYY-MM-DD');
    return reservationDate === targetDate && reservation.status !== 'cancelled';
  });

  const groupedByRoom: { [key: string]: ReservationEvent[] } = {};

  dayReservations.forEach(reservation => {
    if (!groupedByRoom[reservation.room]) {
      groupedByRoom[reservation.room] = [];
    }
    groupedByRoom[reservation.room].push(reservation);
  });

  Object.keys(groupedByRoom).forEach(room => {
    groupedByRoom[room].sort((a, b) => a.start.getTime() - b.start.getTime());
  });

  return groupedByRoom;
};
