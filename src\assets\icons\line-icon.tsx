import React from 'react';

interface LineIconProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

const LineIcon: React.FC<LineIconProps> = ({
  width = 18,
  height = 18,
  color = '#1E293B',
  className,
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_4003_5147)">
        <path
          d="M17.4913 2H6.50875C4.01864 2 2 4.01864 2 6.50875V17.4913C2 19.9814 4.01864 22 6.50875 22H17.4913C19.9814 22 22 19.9814 22 17.4913V6.50875C22 4.01864 19.9814 2 17.4913 2Z"
          fill="#4CC764"
        />
        <path
          d="M18.6663 11.0575C18.6663 8.07374 15.6751 5.64624 11.9982 5.64624C8.32133 5.64624 5.33008 8.07374 5.33008 11.0575C5.33008 13.7325 7.70258 15.9725 10.907 16.3962C11.1238 16.4431 11.4195 16.5394 11.4945 16.725C11.562 16.8937 11.5382 17.1575 11.5163 17.3281C11.5163 17.3281 11.4382 17.7987 11.4213 17.8987C11.392 18.0675 11.2876 18.5581 11.9988 18.2581C12.7107 17.9581 15.8388 15.9969 17.2376 14.3869C18.2038 13.3269 18.667 12.2519 18.667 11.0575H18.6663Z"
          fill="white"
        />
        <path
          d="M16.4474 12.7806H14.5743C14.5037 12.7806 14.4468 12.7237 14.4468 12.6531V12.6512V9.74623V9.7431C14.4468 9.67248 14.5037 9.6156 14.5743 9.6156H16.4474C16.5174 9.6156 16.5749 9.6731 16.5749 9.7431V10.2162C16.5749 10.2869 16.518 10.3437 16.4474 10.3437H15.1743V10.835H16.4474C16.5174 10.835 16.5749 10.8925 16.5749 10.9625V11.4356C16.5749 11.5062 16.518 11.5631 16.4474 11.5631H15.1743V12.0544H16.4474C16.5174 12.0544 16.5749 12.1119 16.5749 12.1819V12.655C16.5749 12.7256 16.518 12.7825 16.4474 12.7825V12.7806Z"
          fill="#4CC764"
        />
        <path
          d="M9.51834 12.7806C9.58834 12.7806 9.64584 12.7237 9.64584 12.6531V12.18C9.64584 12.11 9.58834 12.0525 9.51834 12.0525H8.24521V9.74249C8.24521 9.67249 8.18771 9.61499 8.11771 9.61499H7.64459C7.57396 9.61499 7.51709 9.67187 7.51709 9.74249V12.6506V12.6531C7.51709 12.7237 7.57396 12.7806 7.64459 12.7806H9.51771H9.51834Z"
          fill="#4CC764"
        />
        <path
          d="M10.6454 9.6156H10.1729C10.1025 9.6156 10.0454 9.67268 10.0454 9.7431V12.6531C10.0454 12.7235 10.1025 12.7806 10.1729 12.7806H10.6454C10.7158 12.7806 10.7729 12.7235 10.7729 12.6531V9.7431C10.7729 9.67268 10.7158 9.6156 10.6454 9.6156Z"
          fill="#4CC764"
        />
        <path
          d="M13.8649 9.6156H13.3918C13.3212 9.6156 13.2643 9.67248 13.2643 9.7431V11.4712L11.933 9.6731C11.9299 9.66873 11.9262 9.66435 11.923 9.65998C11.923 9.65998 11.923 9.65998 11.9224 9.65935C11.9199 9.65685 11.9174 9.65373 11.9149 9.65123C11.9143 9.6506 11.913 9.64998 11.9124 9.64935C11.9099 9.64748 11.908 9.6456 11.9055 9.64373C11.9043 9.6431 11.903 9.64185 11.9018 9.64123C11.8999 9.63935 11.8974 9.6381 11.8949 9.63685C11.8937 9.63623 11.8924 9.63498 11.8912 9.63435C11.8887 9.6331 11.8868 9.63185 11.8843 9.6306C11.883 9.62998 11.8818 9.62935 11.8805 9.62873C11.878 9.62748 11.8755 9.62623 11.873 9.6256C11.8718 9.6256 11.8705 9.62435 11.8687 9.62435C11.8662 9.62373 11.8637 9.62248 11.8612 9.62185C11.8599 9.62185 11.858 9.62123 11.8568 9.6206C11.8543 9.6206 11.8518 9.61935 11.8493 9.61873C11.8474 9.61873 11.8455 9.61873 11.8437 9.6181C11.8412 9.6181 11.8393 9.61748 11.8368 9.61748C11.8343 9.61748 11.8324 9.61748 11.8299 9.61748C11.8287 9.61748 11.8268 9.61748 11.8255 9.61748H11.3555C11.2855 9.61748 11.228 9.67435 11.228 9.74498V12.655C11.228 12.725 11.2849 12.7825 11.3555 12.7825H11.8287C11.8993 12.7825 11.9562 12.7256 11.9562 12.655V10.9269L13.2893 12.7275C13.2987 12.7406 13.3099 12.7512 13.3224 12.7594C13.3224 12.7594 13.3237 12.76 13.3237 12.7606C13.3262 12.7625 13.3287 12.7637 13.3318 12.7656C13.333 12.7662 13.3343 12.7669 13.3355 12.7675C13.3374 12.7687 13.3399 12.7694 13.3418 12.7706C13.3437 12.7719 13.3462 12.7725 13.348 12.7731C13.3493 12.7731 13.3505 12.7744 13.3518 12.7744C13.3549 12.7756 13.3574 12.7762 13.3605 12.7769C13.3605 12.7769 13.3618 12.7769 13.3624 12.7769C13.373 12.7794 13.3843 12.7812 13.3955 12.7812H13.8655C13.9355 12.7812 13.993 12.7244 13.993 12.6537V9.74373C13.993 9.67373 13.9362 9.61623 13.8655 9.61623L13.8649 9.6156Z"
          fill="#4CC764"
        />
      </g>
      <defs>
        <clipPath id="clip0_4003_5147">
          <rect width="20" height="20" fill="white" transform="translate(2 2)" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default LineIcon;
