import React, { useState } from 'react';
import { Box, Flex, Input, Text, InputGroup, InputLeftElement, FormLabel } from '@chakra-ui/react';
import { CalendarIcon } from '@chakra-ui/icons';

interface DateRangeSelectProps {
  label?: string;
  onChange?: (startDate: string, endDate: string) => void;
  initialStartDate?: string;
  initialEndDate?: string;
}

function DateSelect({ 
  label = "対象期間", 
  onChange,
  initialStartDate = "",
  initialEndDate = ""
}: DateRangeSelectProps) {
  const [startDate, setStartDate] = useState(initialStartDate);
  const [endDate, setEndDate] = useState(initialEndDate);

  const handleStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setStartDate(e.target.value);
    if (onChange) onChange(e.target.value, endDate);
  };

  const handleEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEndDate(e.target.value);
    if (onChange) onChange(startDate, e.target.value);
  };

  return (
    <Box>
      <FormLabel>{label}</FormLabel>
      <Flex 
        direction={{ base: "column", md: "row" }} 
        align="center" 
        gap={'.5rem'}
        w={'auto'}
        // bg={'red.100'}
      >
        {/* <InputGroup w={'auto'}> */}
          {/* <InputLeftElement pointerEvents="none">
            <CalendarIcon color="black" />
          </InputLeftElement> */}
          <Input
            type="date"
            value={startDate}
            onChange={handleStartDateChange}
            placeholder="YYYY/MM/DD"
            borderRadius="md"
            borderColor="gray.200"
            // pl="2rem"
          />
        {/* </InputGroup> */}
         
        <Text fontWeight={'900'} fontSize="xs">〜</Text>
        
        {/* <InputGroup w={'auto'}> */}
          {/* <InputLeftElement pointerEvents="none">
            <CalendarIcon color=" black" />
          </InputLeftElement> */}
          <Input
            type="date"
            value={endDate}
            onChange={handleEndDateChange}
            placeholder="YYYY/MM/DD"
            borderRadius="md"
            borderColor="gray.200"
            // pl="2rem"
          />
        {/* </InputGroup> */}
      </Flex>
    </Box>
  );
}

export default DateSelect;