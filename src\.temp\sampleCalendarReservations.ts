import { ReservationEvent } from '../types/ReservationEventType';

export const sampleReservations: ReservationEvent[] = [
  {
    id: 1,
    title: '予約: 鈴木様',
    start: new Date('2025-07-03T09:30:00'),
    end: new Date('2025-07-03T10:30:00'),
    room: 'Room 01',
    groupName: 'Just Group',
    phoneNumber: '080-1234-1234',
    peopleCount: 4,
    status: 'confirmed',
    customerName: '鈴木様',
  },
  {
    id: 2,
    title: '予約: 田中様',
    start: new Date('2025-07-03T10:00:00'),
    end: new Date('2025-07-03T14:00:00'),
    room: 'Room 02',
    groupName: 'Tanaka Group', // cspell:disable-line
    phoneNumber: '080-5678-9012',
    peopleCount: 6,
    status: 'confirmed',
    customerName: '田中様',
  },
  {
    id: 3,
    title: '予約: 佐藤様',
    start: new Date('2025-07-03T09:30:00'),
    end: new Date('2025-07-03T12:00:00'),
    room: 'Room 03',
    groupName: 'Sato Group', // cspell:disable-line
    phoneNumber: '080-3456-7890',
    peopleCount: 8,
    status: 'pending',
    customerName: '佐藤様',
  },
  {
    id: 4,
    title: '予約: 山田様',
    start: new Date('2025-07-03T15:00:00'),
    end: new Date('2025-07-03T17:00:00'),
    room: 'Room 02',
    groupName: 'Yamada Group',
    phoneNumber: '080-2468-1357',
    peopleCount: 3,
    status: 'confirmed',
    customerName: '山田様',
  },
  {
    id: 5,
    title: '予約: 高橋様',
    start: new Date('2025-07-03T13:00:00'),
    end: new Date('2025-07-03T15:00:00'),
    room: 'Room 03',
    groupName: 'Takahashi Group', // cspell:disable-line
    phoneNumber: '080-1357-2468',
    peopleCount: 7,
    status: 'confirmed',
    customerName: '高橋様',
  },
  {
    id: 6,
    title: '予約: 渡辺様',
    start: new Date('2025-07-03T11:00:00'),
    end: new Date('2025-07-03T13:00:00'),
    room: 'Room 04',
    groupName: 'Watanabe Group', // cspell:disable-line
    phoneNumber: '080-7531-9642',
    peopleCount: 4,
    status: 'cancelled',
    customerName: '渡辺様',
  },
  {
    id: 7,
    title: '予約: 伊藤様',
    start: new Date('2025-07-03T16:00:00'),
    end: new Date('2025-07-03T18:00:00'),
    room: 'Room 05',
    groupName: 'Ito Group',
    phoneNumber: '080-9876-5432',
    peopleCount: 5,
    status: 'confirmed',
    customerName: '伊藤様',
  },
  {
    id: 8,
    title: '予約: 小林様',
    start: new Date('2025-07-03T08:30:00'),
    end: new Date('2025-07-03T10:00:00'),
    room: 'Room 01',
    groupName: 'グループ名', // cspell:disable-line
    phoneNumber: '080-1111-2222',
    peopleCount: 2,
    status: 'confirmed',
    customerName: '小林様',
  },
  {
    id: 9,
    title: '予約: 加藤様',
    start: new Date('2025-07-03T14:30:00'),
    end: new Date('2025-07-03T16:30:00'),
    room: 'Room 03',
    groupName: 'Kato Group', // cspell:disable-line
    phoneNumber: '080-3333-4444',
    peopleCount: 6,
    status: 'pending',
    customerName: '加藤様',
  },
  {
    id: 10,
    title: '予約: 松本様',
    start: new Date('2025-07-03T12:00:00'),
    end: new Date('2025-07-03T14:00:00'),
    room: 'Room 02',
    groupName: 'Matsumoto Group', // cspell:disable-line
    phoneNumber: '080-5555-6666',
    peopleCount: 5,
    status: 'confirmed',
    customerName: '松本様',
  },
];
