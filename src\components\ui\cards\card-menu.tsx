import { Button, Flex, Image, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import Router from 'next/router';

interface CardMenuProps {
  coursePrice?: string;
  courseImage?: string;
}

function CardMenu({
  coursePrice = '12,000',
  courseImage = '/imgs/icons/Placeholder.svg',
}: CardMenuProps = {}) {
  const { t } = useTranslation('common');

  return (
    <Flex flexDirection={'column'} p={{ base: '1rem', md: '2rem' }} bg={'white'} gap={'.5rem'}>
      <Flex>
        <Text fontSize={'sm'} fontWeight={'bold'} textAlign={'center'}>
          {t('reservations.reservationPlan')}
        </Text>
      </Flex>
      <Flex>
        <Flex w={'6rem'} overflow={'hidden'} borderRadius={'10px'}>
          <Image
            style={{ objectFit: 'cover', width: '100%' }}
            src={courseImage}
            alt={t('courses.courseImage')}
          />
        </Flex>
        <Flex flexDirection={'column'} ml={'1rem'}>
          <Text fontWeight={'500'}>{t('courses.beefFeastCourse')}</Text>
          <Flex fontSize={'sm'}>
            <Text fontWeight={'bold'}>{t('formats.coursePrice', { price: coursePrice })}</Text>
            <Text>{t('formats.perPerson')}</Text>
          </Flex>
        </Flex>
      </Flex>
      <Flex>
        <Text fontSize={'sm'}>{t('courses.courseDescription')}</Text>
      </Flex>
      <Flex>
        <Text variant={'category'}>{t('buttons.requestReservation')}</Text>
      </Flex>

      <Flex justifyContent={'flex-end'}>
        <Button
          px={'3rem'}
          py={'.5rem'}
          w={'auto'}
          variant={'roundedBlue'}
          fontWeight={'normal'}
          onClick={() => {
            Router.push('/client/shop-calendar');
          }}
        >
          {t('buttons.reserve')}
        </Button>
      </Flex>
    </Flex>
  );
}

export default CardMenu;
