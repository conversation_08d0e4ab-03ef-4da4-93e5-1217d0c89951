import { <PERSON>, Button, Flex, <PERSON><PERSON>, <PERSON>Stack, useColorModeValue } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import Link from 'next/link';
import { useRouter } from 'next/router';
import ClientStoreBanner from './clients-store-banner';
import LanguageSwitcher from './language-switcher';

function ClientHeader() {
  const router = useRouter();
  const { t } = useTranslation('auth');

  const bgColor = useColorModeValue('white', 'gray.800');

  return (
    <Box
      as="header"
      position="fixed"
      top={0}
      w={'100dvw'}
      zIndex={10}
      bg={bgColor}
      py={'.3rem'}
      px={{ base: '1rem' }}
    >
      <Flex justify="space-between" align="center">
        <Link href="/client/home" passHref>
          <Heading as="h1" size="md" cursor="pointer">
            <ClientStoreBanner />
          </Heading>
        </Link>

        <HStack spacing={4}>
          <LanguageSwitcher />
          <Button
            variant="rounded"
            my={'.5rem'}
            py={'1.4rem'}
            colorScheme="blue"
            border={'1px solid gray'}
            fontWeight={400}
            onClick={() => router.push('/login')}
          >
            {t('login.title')}
          </Button>
        </HStack>
      </Flex>
    </Box>
  );
}

export default ClientHeader;
