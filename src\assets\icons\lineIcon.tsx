import React from 'react';

interface LineIconProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

const LineIcon: React.FC<LineIconProps> = ({
  width = 18,
  height = 18,
  color = '#1E293B',
  className,
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_4616_4254)">
        <path
          d="M15.4913 0H4.50875C2.01864 0 0 2.01864 0 4.50875V15.4913C0 17.9814 2.01864 20 4.50875 20H15.4913C17.9814 20 20 17.9814 20 15.4913V4.50875C20 2.01864 17.9814 0 15.4913 0Z"
          fill="#4CC764"
        />
        <path
          d="M16.6663 9.05749C16.6663 6.07374 13.6751 3.64624 9.9982 3.64624C6.32133 3.64624 3.33008 6.07374 3.33008 9.05749C3.33008 11.7325 5.70258 13.9725 8.90695 14.3962C9.12383 14.4431 9.41945 14.5394 9.49445 14.725C9.56195 14.8937 9.5382 15.1575 9.51633 15.3281C9.51633 15.3281 9.4382 15.7987 9.42133 15.8987C9.39195 16.0675 9.28758 16.5581 9.99883 16.2581C10.7107 15.9581 13.8388 13.9969 15.2376 12.3869C16.2038 11.3269 16.667 10.2519 16.667 9.05749H16.6663Z"
          fill="white"
        />
        <path
          d="M14.4474 10.7806H12.5743C12.5037 10.7806 12.4468 10.7237 12.4468 10.6531V10.6512V7.74623V7.7431C12.4468 7.67248 12.5037 7.6156 12.5743 7.6156H14.4474C14.5174 7.6156 14.5749 7.6731 14.5749 7.7431V8.21623C14.5749 8.28685 14.518 8.34373 14.4474 8.34373H13.1743V8.83498H14.4474C14.5174 8.83498 14.5749 8.89248 14.5749 8.96248V9.4356C14.5749 9.50623 14.518 9.5631 14.4474 9.5631H13.1743V10.0544H14.4474C14.5174 10.0544 14.5749 10.1119 14.5749 10.1819V10.655C14.5749 10.7256 14.518 10.7825 14.4474 10.7825V10.7806Z"
          fill="#4CC764"
        />
        <path
          d="M7.51834 10.7806C7.58834 10.7806 7.64584 10.7237 7.64584 10.6531V10.18C7.64584 10.11 7.58834 10.0525 7.51834 10.0525H6.24521V7.74249C6.24521 7.67249 6.18771 7.61499 6.11771 7.61499H5.64459C5.57396 7.61499 5.51709 7.67187 5.51709 7.74249V10.6506V10.6531C5.51709 10.7237 5.57396 10.7806 5.64459 10.7806H7.51771H7.51834Z"
          fill="#4CC764"
        />
        <path
          d="M8.64541 7.6156H8.17291C8.10249 7.6156 8.04541 7.67268 8.04541 7.7431V10.6531C8.04541 10.7235 8.10249 10.7806 8.17291 10.7806H8.64541C8.71583 10.7806 8.77291 10.7235 8.77291 10.6531V7.7431C8.77291 7.67268 8.71583 7.6156 8.64541 7.6156Z"
          fill="#4CC764"
        />
        <path
          d="M11.8649 7.6156H11.3918C11.3212 7.6156 11.2643 7.67248 11.2643 7.7431V9.47123L9.93303 7.6731C9.9299 7.66873 9.92615 7.66435 9.92303 7.65998C9.92303 7.65998 9.92303 7.65998 9.9224 7.65935C9.9199 7.65685 9.9174 7.65373 9.9149 7.65123C9.91428 7.6506 9.91303 7.64998 9.9124 7.64935C9.9099 7.64748 9.90803 7.6456 9.90553 7.64373C9.90428 7.6431 9.90303 7.64185 9.90178 7.64123C9.8999 7.63935 9.8974 7.6381 9.8949 7.63685C9.89365 7.63623 9.8924 7.63498 9.89115 7.63435C9.88865 7.6331 9.88678 7.63185 9.88428 7.6306C9.88303 7.62998 9.88178 7.62935 9.88053 7.62873C9.87803 7.62748 9.87553 7.62623 9.87303 7.6256C9.87178 7.6256 9.87053 7.62435 9.86865 7.62435C9.86615 7.62373 9.86365 7.62248 9.86115 7.62185C9.8599 7.62185 9.85803 7.62123 9.85678 7.6206C9.85428 7.6206 9.85178 7.61935 9.84928 7.61873C9.8474 7.61873 9.84553 7.61873 9.84365 7.6181C9.84115 7.6181 9.83928 7.61748 9.83678 7.61748C9.83428 7.61748 9.8324 7.61748 9.8299 7.61748C9.82865 7.61748 9.82678 7.61748 9.82553 7.61748H9.35553C9.28553 7.61748 9.22803 7.67435 9.22803 7.74498V10.655C9.22803 10.725 9.2849 10.7825 9.35553 10.7825H9.82865C9.89928 10.7825 9.95615 10.7256 9.95615 10.655V8.92685L11.2893 10.7275C11.2987 10.7406 11.3099 10.7512 11.3224 10.7594C11.3224 10.7594 11.3237 10.76 11.3237 10.7606C11.3262 10.7625 11.3287 10.7637 11.3318 10.7656C11.333 10.7662 11.3343 10.7669 11.3355 10.7675C11.3374 10.7687 11.3399 10.7694 11.3418 10.7706C11.3437 10.7719 11.3462 10.7725 11.348 10.7731C11.3493 10.7731 11.3505 10.7744 11.3518 10.7744C11.3549 10.7756 11.3574 10.7762 11.3605 10.7769C11.3605 10.7769 11.3618 10.7769 11.3624 10.7769C11.373 10.7794 11.3843 10.7812 11.3955 10.7812H11.8655C11.9355 10.7812 11.993 10.7244 11.993 10.6537V7.74373C11.993 7.67373 11.9362 7.61623 11.8655 7.61623L11.8649 7.6156Z"
          fill="#4CC764"
        />
      </g>
      <defs>
        <clipPath id="clip0_4616_4254">
          <rect width={width} height={height} fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default LineIcon;
