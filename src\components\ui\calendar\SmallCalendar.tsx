import { ChevronLeftIcon, ChevronRightIcon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  Flex,
  Grid,
  GridItem,
  IconButton,
  Text,
  useColorModeValue,
} from '@chakra-ui/react';
import moment from 'moment';
import React from 'react';

interface SmallCalendarProps {
  currentDate: Date;
  onDateSelect: (date: Date) => void;
}

export const SmallCalendar: React.FC<SmallCalendarProps> = ({ currentDate, onDateSelect }) => {
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const todayBg = useColorModeValue('blue.500', 'blue.400');
  const selectedBg = useColorModeValue('blue.100', 'blue.700');
  const hoverBg = useColorModeValue('gray.100', 'gray.700');

  const today = new Date();
  const currentMonth = moment(currentDate);
  const startOfMonth = currentMonth.clone().startOf('month');
  const endOfMonth = currentMonth.clone().endOf('month');
  const startOfCalendar = startOfMonth.clone().startOf('week');
  const endOfCalendar = endOfMonth.clone().endOf('week');

  const days = [];
  const current = startOfCalendar.clone();

  while (current.isSameOrBefore(endOfCalendar)) {
    days.push(current.clone());
    current.add(1, 'day');
  }

  const weekDays = ['月', '火', '水', '木', '金', '土', '日'];

  const isToday = (date: moment.Moment) => {
    return date.isSame(today, 'day');
  };

  const isSelected = (date: moment.Moment) => {
    return date.isSame(currentDate, 'day');
  };

  const isCurrentMonth = (date: moment.Moment) => {
    return date.isSame(currentMonth, 'month');
  };

  return (
    <Box
      border="1px solid"
      borderColor={borderColor}
      borderRadius="md"
      p={4}
      bg="white"
      w="full"
      boxShadow="sm"
    >
      <Flex justify="space-between" align="center" mb={3}>
        <IconButton
          aria-label="Previous month"
          icon={<ChevronLeftIcon />}
          size="sm"
          variant="ghost"
          onClick={() => {
            const newDate = currentMonth.clone().subtract(1, 'month').startOf('month').toDate();
            onDateSelect(newDate);
          }}
        />
        <Text fontWeight="bold" fontSize="md" minW="120px" textAlign="center">
          {currentMonth.format('YYYY年M月')}
        </Text>
        <IconButton
          aria-label="Next month"
          icon={<ChevronRightIcon />}
          size="sm"
          variant="ghost"
          onClick={() => {
            const newDate = currentMonth.clone().add(1, 'month').startOf('month').toDate();
            onDateSelect(newDate);
          }}
        />
      </Flex>

      <Grid templateColumns="repeat(7, 1fr)" gap={1} mb={3}>
        {weekDays.map(day => (
          <GridItem key={day}>
            <Text
              textAlign="center"
              fontSize="xs"
              fontWeight="bold"
              color="gray.500"
              py={2}
              bg="gray.50"
              borderRadius="sm"
            >
              {day}
            </Text>
          </GridItem>
        ))}
      </Grid>

      <Grid templateColumns="repeat(7, 1fr)" gap={1}>
        {days.map(day => {
          const dayIsToday = isToday(day);
          const dayIsSelected = isSelected(day);
          const dayIsCurrentMonth = isCurrentMonth(day);

          return (
            <GridItem key={day.format('YYYY-MM-DD')}>
              <Button
                size="sm"
                variant="ghost"
                w="full"
                h="36px"
                p={0}
                fontSize="sm"
                fontWeight={dayIsToday || dayIsSelected ? 'bold' : 'normal'}
                color={dayIsToday ? 'white' : dayIsCurrentMonth ? 'inherit' : 'gray.400'}
                bg={dayIsToday ? todayBg : dayIsSelected ? selectedBg : 'transparent'}
                borderRadius="md"
                _hover={{
                  bg: dayIsToday ? todayBg : dayIsSelected ? selectedBg : hoverBg,
                }}
                onClick={() => onDateSelect(day.toDate())}
              >
                {day.format('D')}
              </Button>
            </GridItem>
          );
        })}
      </Grid>
    </Box>
  );
};
