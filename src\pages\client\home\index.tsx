import CardImageContainer from '@/components/ui/cards/card-image-container';
import CardMenuContainer from '@/components/ui/cards/card-menu-container';
import CardText from '@/components/ui/cards/card-text';
import CardTextStoreInformation from '@/components/ui/cards/card-text-store-information';
import { Flex } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

interface ShopData {
  name: string;
  location: string;
  description: string;
  image: string;
}

//TODO: Need to add id props for the selected reservation
function ClientHome() {
  const { t } = useTranslation('common');
  //TODO: This would typically come from an API
  const shopData: ShopData = {
    name: 'YAKINIKU-YA',
    location: '麻布十番',
    description: t('store.shopDescription', {
      shopName: 'やきにくや YAKINIKU-YA',
      location: '麻布十番',
    }),
    image: '/storeImage.png',
  };

  return (
    <Flex flexDirection={'column'} gap={'1rem'}>
      <CardImageContainer src={'/storeImage.png'} />
      <CardText text={shopData.description} />
      <CardTextStoreInformation />
      <CardMenuContainer variant={'menu'} />
      <CardMenuContainer variant={'share-space'} />
    </Flex>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'auth'])),
      layoutType: 'client',
    },
  };
}

export default ClientHome;
