import PenIcon from '@/assets/icons/pen';
import StoreBanner from '@/components/ui/store-banner';
import TextPair from '@/components/ui/TextPair';
import { Button, Flex, Image, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import router from 'next/router';

export default function StoreDetailsPage() {
  const { t } = useTranslation('admin');

  return (
    <Flex
      w={'100%'}
      flexDirection={'column'}
      pr={{ base: 0, md: 6 }}
      pl={{ base: 0, md: 6, lg: 0 }}
    >
      <Flex
        bg={{ base: 'white', md: 'unset' }}
        justifyContent={'space-between'}
        px={{ base: 5, lg: 0 }}
        alignItems={'center'}
        height={'5rem'}
        w={'100%'}
      >
        <Text fontSize={'lg'} fontWeight={'bold'}>
          {t('storeDetails.header.title')}
        </Text>

        <Button
          display={{ base: 'flex', md: 'none' }}
          variant={'roundedBlue'}
          fontSize={'xs'}
          fontWeight={'normal'}
          gap={2}
          w={'auto'}
          onClick={() => {
            router.push('/admin/store-details/store-details-edit/test-page');
          }}
        >
          <PenIcon width={12} height={12} color="white" className="my-icon-class" />
          {t('storeDetails.buttons.edit')}
        </Button>
      </Flex>

      <Flex
        flexDirection={'column'}
        bg={'white'}
        gap={4}
        p={{ base: 0, sm: 5 }}
        borderRadius={'1rem'}
      >
        <Flex justifyContent={{ base: 'center', sm: 'space-between' }}>
          <StoreBanner />
          <Button
            display={{ base: 'none', md: 'flex' }}
            as={'a'}
            href={'/admin/store-details/store-details-edit/test-page'}
            variant={'roundedBlue'}
            fontSize={'xs'}
            fontWeight={'normal'}
            gap={2}
            w={'auto'}
          >
            <PenIcon width={12} height={12} color="white" className="my-icon-class" />
            {t('storeDetails.buttons.edit')}
          </Button>
        </Flex>

        <Flex px={'1rem'} flexDirection={'column'}>
          <Flex
            flexDirection={'column'}
            gap={2}
            borderBottom={'1px solid #E5EAF1'}
            pb={'1rem'}
            mb={'1rem'}
          >
            <Text fontSize={'xs'} fontWeight={'semibold'}>
              {t('storeDetails.sections.storePhoto')}
            </Text>

            <Flex
              bg={'red.100'}
              overflow={'hidden'}
              borderRadius={'10px'}
              height={'12rem'}
              w={{ base: '100%', sm: '24rem' }}
            >
              <Image
                style={{ objectFit: 'cover', width: '100%' }}
                src={'/imgs/icons/Placeholder.svg'}
                alt="option"
              />
            </Flex>
          </Flex>

          <TextPair
            header={t('storeDetails.fields.storeId')}
            subtext={['justyotaku.com / example-shop']}
          />
          <TextPair
            header={t('storeDetails.fields.storeName')}
            subtext={['Share Space シェアスペース 渋谷店']}
          />
          <TextPair
            header={t('storeDetails.fields.storeAddress')}
            subtext={['東京都渋谷区宇田川町5丁目1−21']}
          />
          <TextPair header={t('storeDetails.fields.contact')} subtext={['080-1234-1234']} />
          <TextPair
            header={t('storeDetails.fields.businessHours')}
            subtext={[
              t('storeDetails.businessHours.monday'),
              t('storeDetails.businessHours.tuesday'),
              t('storeDetails.businessHours.wednesday'),
              t('storeDetails.businessHours.thursday'),
              t('storeDetails.businessHours.friday'),
              t('storeDetails.businessHours.saturday'),
              t('storeDetails.businessHours.sunday'),
            ]}
          />
          <TextPair header={t('storeDetails.fields.website')} subtext={['sharespace.jp']} />
          <TextPair
            lineHeight={5}
            header={t('storeDetails.fields.storeOverview')}
            subtext={[t('storeDetails.content.storeDescription')]}
          />
        </Flex>
      </Flex>
    </Flex>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'admin'])),
      layoutType: 'withSidebar',
    },
  };
}
