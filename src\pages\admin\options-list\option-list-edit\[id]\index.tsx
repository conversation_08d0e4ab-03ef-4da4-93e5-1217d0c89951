import AssignmentIcon from '@/assets/icons/assignment';
import BackIcon from '@/assets/icons/back';
import TrashIcon from '@/assets/icons/trash';
import OptionList from '@/components/forms/option-list';
import StoreBanner from '@/components/ui/store-banner';
import { useTempOptionListStore } from '@/store/temp/admin/tempOptionList';
import {
  Button,
  Flex,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalFooter,
  ModalOverlay,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useRouter } from 'next/router';
import { useState } from 'react';

function OptionListEdit() {
  const { t } = useTranslation('common');
  const deleteOption = useTempOptionListStore(state => state.deleteOption);
  const getOption = useTempOptionListStore(state => state.getOption);

  const page = 'edit';
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [modalStep, setModalStep] = useState(1);
  const router = useRouter();

  const handleSubmit = () => {
    deleteOption(parseInt(router.query.id as string, 10));
    setModalStep(2);
  };

  const handleClose = () => {
    setModalStep(1);
    onClose();
  };

  // console.log(getOption(parseInt(router.query.id as string, 10)));

  const data = getOption(parseInt(router.query.id as string, 10));

  return (
    <Flex flexDirection={'column'} pr={5} w={'100%'}>
      <Flex w={'100%'} gap={4} py={5} alignItems={'center'}>
        <Button
          variant={'ghost'}
          w={'auto'}
          p={0}
          m={0}
          onClick={() => {
            router.push('/admin/options-list');
          }}
        >
          <BackIcon width={30} height={30} color="black" className="my-icon-class" />
        </Button>
        <Text fontSize={'lg'} fontWeight={'bold'}>
          {t('optionslist.edit.title')}
        </Text>
      </Flex>

      <Flex
        bg={'white'}
        p={5}
        pt={{ base: '4rem' }}
        mt={4}
        borderRadius={'md'}
        direction={'column'}
      >
        <Flex justifyContent={{ base: 'center', sm: 'space-between' }}>
          <StoreBanner />
        </Flex>

        <OptionList data={data} onOpen={onOpen} page={page} />
      </Flex>

      <Modal isOpen={isOpen} onClose={handleClose}>
        <ModalOverlay />
        {modalStep === 1 ? (
          <ModalContent>
            <Flex justifyContent={'center'} alignItems={'center'} h={'5rem'}>
              <TrashIcon width={30} height={30} color="black" />
            </Flex>
            <ModalCloseButton />
            <ModalBody fontSize={'sm'} textAlign={'center'}>
              <Text mb={4} fontSize={'md'} fontWeight={'bold'}>
                {t('optionslist.edit.deleteModal.title')}
              </Text>
              <Text whiteSpace="pre-line">{t('optionslist.edit.deleteModal.description')}</Text>
            </ModalBody>
            <ModalFooter gap={4} justifyContent={'center'}>
              <Button px={'3rem'} w={'auto'} variant="rounded" onClick={handleClose}>
                {t('optionslist.edit.deleteModal.backButton')}
              </Button>
              <Button px={'3rem'} w={'auto'} variant="roundedBlue" onClick={handleSubmit}>
                {t('optionslist.edit.deleteModal.deleteButton')}
              </Button>
            </ModalFooter>
          </ModalContent>
        ) : (
          <ModalContent>
            <Flex justifyContent={'center'} alignItems={'center'} h={'5rem'}>
              <AssignmentIcon width={30} height={30} color="black" />
            </Flex>
            <ModalCloseButton />
            <ModalBody fontSize={'sm'} textAlign={'center'}>
              <Text mb={4} fontSize={'md'} fontWeight={'bold'}>
                {t('optionslist.edit.deleteModal.successTitle')}
              </Text>
            </ModalBody>
            <ModalFooter gap={4} justifyContent={'center'}>
              <Button
                px={'3rem'}
                w={'auto'}
                variant="roundedBlue"
                onClick={() => {
                  handleClose();
                  router.push('/admin/options-list');
                }}
              >
                {t('optionslist.edit.deleteModal.closeButton')}
              </Button>
            </ModalFooter>
          </ModalContent>
        )}
      </Modal>
    </Flex>
  );
}

export async function getStaticPaths() {
  return {
    paths: [],
    fallback: 'blocking',
  };
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'admin'])),
      layoutType: 'withSidebar',
    },
  };
}

export default OptionListEdit;
