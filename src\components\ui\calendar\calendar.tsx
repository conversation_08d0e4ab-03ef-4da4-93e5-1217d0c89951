import { constants } from '@/constants/constants';
import { useCalendar } from '@/hooks/useCalendar';
import { formatMonthYear, getDaysInMonth, getFirstDayOfMonth } from '@/utils/dateUtils';
import { ChevronLeftIcon, ChevronRightIcon } from '@chakra-ui/icons';
import { Box, Button, Flex, Grid, Text, useColorModeValue } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import React from 'react';

interface CalendarProps {
  onDateSelect?: (date: Date) => void;
  highlightedDates?: Date[];
  disabledDates?: Date[];
  minDate?: Date;
  maxDate?: Date;
  initialDate?: Date;
}

const Calendar: React.FC<CalendarProps> = ({
  onDateSelect,
  highlightedDates = [],
  disabledDates = [],
  minDate,
  maxDate,
  initialDate,
}) => {
  const { t } = useTranslation('common');

  // Use our custom hook for calendar logic
  const {
    currentDate,
    prevMonth,
    nextMonth,
    handleDateClick,
    isHighlighted,
    isDisabled,
    isToday,
    isSelected,
  } = useCalendar({
    onDateSelect,
    highlightedDates,
    disabledDates,
    minDate,
    maxDate,
    initialDate,
  });

  // UI-specific color values
  const highlightColor = useColorModeValue('blue.500', 'blue.300');
  const disabledColor = useColorModeValue('gray.300', 'gray.600');
  const todayColor = useColorModeValue('green.500', 'green.300');
  const selectedBg = useColorModeValue('green.500', 'blue.700');

  // Get translated short day names
  const shortDayNames = [
    t('calendar.sundayShort'),
    t('calendar.mondayShort'),
    t('calendar.tuesdayShort'),
    t('calendar.wednesdayShort'),
    t('calendar.thursdayShort'),
    t('calendar.fridayShort'),
    t('calendar.saturdayShort'),
  ];

  // Get current month and year for calculations
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();

  /**
   * Get the month name from the constants
   */
  const getMonthName = (month: number) => {
    const monthNames = constants.MONTH_NAMES;
    return monthNames[month];
  };

  /**
   * Generate the calendar day buttons
   */
  const generateCalendarDays = () => {
    const daysInMonth = getDaysInMonth(currentYear, currentMonth);
    const firstDay = getFirstDayOfMonth(currentYear, currentMonth);

    const days = [];

    // Add empty boxes for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(<Box key={`empty-${i}`} />);
    }

    // Generate buttons for each day of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(currentYear, currentMonth, day);
      const disabled = isDisabled(date);
      const highlighted = isHighlighted(date);
      const today = isToday(date);
      const selected = isSelected(date);

      days.push(
        <Button
          key={`day-${day}`}
          onClick={() => handleDateClick(date)}
          isDisabled={disabled}
          variant="outline"
          colorScheme={highlighted ? 'blue' : undefined}
          bg={selected ? selectedBg : 'transparent'}
          color={
            selected
              ? 'white'
              : disabled
              ? disabledColor
              : today
              ? todayColor
              : highlighted
              ? highlightColor
              : undefined
          }
          borderColor={today ? todayColor : 'transparent'}
          _hover={{ opacity: 0.5 }}
          h="40px"
          w="40px"
          p="0"
          my={'1rem'}
          borderRadius={'full'}
        >
          {day}
        </Button>
      );
    }

    return days;
  };

  return (
    <Box borderRadius="lg" bg="white">
      <Flex justify="space-between" align="center" mb={4}>
        <Button variant="roundedNoBorder" onClick={prevMonth} size="sm">
          <ChevronLeftIcon mr={1} />
          {getMonthName(new Date(currentYear, currentMonth - 1, 1).getMonth())}
        </Button>
        <Text fontWeight="bold">{formatMonthYear(currentDate)}</Text>
        <Button variant="roundedNoBorder" onClick={nextMonth} size="sm">
          {getMonthName(new Date(currentYear, currentMonth + 1, 1).getMonth())}
          <ChevronRightIcon ml={1} />
        </Button>
      </Flex>

      <Grid templateColumns="repeat(7, 1fr)" gap={1} mb={2}>
        {shortDayNames.map((day, dayIndex) => (
          <Box key={`day-${dayIndex}`} textAlign="center" color={'gray.400'}>
            {day}
          </Box>
        ))}
      </Grid>

      <Grid templateColumns="repeat(7, 1fr)" gap={1}>
        {generateCalendarDays()}
      </Grid>
    </Box>
  );
};

export default Calendar;
