import React from 'react';
import { Box, Flex, Grid, GridItem, Text, VStack } from '@chakra-ui/react';
import { Calendar, momentLocalizer, View, Views } from 'react-big-calendar';
import moment from 'moment';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { sampleReservations } from '@/.temp/sampleCalendarReservations';
import { sampleRooms } from '@/.temp/sampleRooms';
import { getRoomColor } from '@/utils/getRoomColor';
import { SmallCalendar } from './SmallCalendar';
import { eventStyleGetter } from '@/utils/eventsStyleGetter';
import { getAllRooms } from '@/utils/getAllRooms';
import { getReservationsForDate } from '@/utils/getReservationsForDate';

const localizer = momentLocalizer(moment);

type CalendarDayViewProps = {
  view: View;
  handleViewChange: (view: View) => void;
  handleNavigate: (date: Date) => void;
  handleSelectEvent: (event: any) => void;
  messages: any;
};

const formatReservationDateTime = (start: Date, end: Date) => {
  const startMoment = moment(start);
  const endMoment = moment(end);
  return `${startMoment.format('YYYY.MM.DD')} ${startMoment.format('H:mm')}~${endMoment.format(
    'H:mm'
  )}`;
};

export const CalendarDayView = ({
  view,
  handleViewChange,
  handleNavigate,
  handleSelectEvent,
  messages,
}: CalendarDayViewProps) => {
  const [currentDate, setCurrentDate] = React.useState(new Date());

  return (
    <Flex gap={4} direction={{ base: 'column', lg: 'row' }}>
      <Box
        flex="1"
        maxW={{ lg: '50%' }}
        // border="1px solid"
        // borderColor={borderColor}
        borderRadius="md"
        // overflow="hidden"
        // height="600px"
      >
        {/* @ts-ignore - react-big-calendar type compatibility issue */}
        <Calendar
          localizer={localizer}
          events={sampleReservations}
          startAccessor="start"
          endAccessor="end"
          style={{ height: '100%' }}
          view={view}
          onView={handleViewChange}
          date={currentDate}
          onNavigate={handleNavigate}
          onSelectEvent={handleSelectEvent}
          eventPropGetter={event => eventStyleGetter({ event, rooms: sampleRooms })}
          messages={messages}
          views={[Views.MONTH, Views.WEEK, Views.DAY]}
          step={30}
          showMultiDayTimes
          popup
          toolbar={false}
          onSelectSlot={() => {
            console.log('slot selected');
          }}
        />
      </Box>

      <VStack
        flex="1"
        maxW={{ lg: '50%' }}
        spacing={6}
        align="stretch"
        minW={{ base: '300px', lg: 'auto' }}
      >
        <SmallCalendar currentDate={currentDate} onDateSelect={setCurrentDate} />

        {/* Room Reservation Details Section - Day View Only */}
        <Box>
          {(() => {
            const reservationsByRoom = getReservationsForDate(currentDate);
            const allRooms = getAllRooms();

            if (Object.keys(reservationsByRoom).length === 0) {
              return (
                <Box
                  p={4}
                  border="1px solid"
                  borderColor={'gray.200'}
                  borderRadius="md"
                  textAlign="center"
                  bg="gray.50"
                >
                  <Text color="gray.600" fontSize="sm">
                    この日の予約はありません
                  </Text>
                </Box>
              );
            }

            return (
              <Grid
                templateColumns={{
                  base: '1fr',
                  md: '1fr 1fr',
                }}
                rowGap={'1.5rem'}
              >
                {allRooms.map(room => {
                  const roomReservations = reservationsByRoom[room] || [];

                  if (roomReservations.length === 0) {
                    return null;
                  }

                  return (
                    <GridItem key={room}>
                      <Box
                        // borderBottom="1px solid"
                        // borderColor={borderColor}
                        overflow="hidden"
                        bg="white"
                        h="full"
                        display="flex"
                        flexDirection="column"
                      >
                        <Flex
                          justifyContent={'center'}
                          alignItems={'center'}
                          px={3}
                          py={2}
                          flexShrink={0}
                          gap={'.5rem'}
                        >
                          <Flex
                            bg={getRoomColor({ roomName: room, rooms: sampleRooms })}
                            w="10px"
                            h="10px"
                            borderRadius="full"
                            mr={1}
                          />
                          <Text fontSize="sm" color="gray.800">
                            {room}
                          </Text>
                        </Flex>

                        <Box flex="1">
                          <VStack spacing={2} align="stretch" h="full">
                            {roomReservations.map(reservation => (
                              <Flex
                                flexDirection={'column'}
                                key={reservation.id}
                                px={'1.5rem'}
                                pt={'.5rem'}
                                pb={'1.5rem'}
                                borderBottom="1px solid"
                                borderColor="gray.200"
                                bg="white"
                                gap={'1rem'}
                              >
                                <Text fontWeight="bold">{reservation.groupName}</Text>

                                <Text fontWeight="bold" fontSize={'medium'}>
                                  {formatReservationDateTime(reservation.start, reservation.end)}
                                </Text>

                                <Text fontWeight="bold">
                                  {reservation.peopleCount}人 / {reservation.phoneNumber}
                                </Text>
                              </Flex>
                            ))}
                          </VStack>
                        </Box>
                      </Box>
                    </GridItem>
                  );
                })}
              </Grid>
            );
          })()}
        </Box>
      </VStack>
    </Flex>
  );
};
