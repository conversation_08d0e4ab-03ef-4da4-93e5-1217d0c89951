import React, { useState } from 'react';
import { Flex, Button, Text, Input } from '@chakra-ui/react';
import PenIcon from '@/assets/icons/pen';
import TrashIcon from '@/assets/icons/trash';
import { CheckIcon } from '@chakra-ui/icons';
import { TagType } from '@/types/catTagTypes';
import { useTempTagStore } from '@/store/temp/admin/tempCatTagStore';

type TagProps = {
  tag: TagType;
  // handleEditTag: (tag: TagType) => void;
};

export default function Tag({ tag }: TagProps) {
  const [editMode, setEditMode] = useState(false);
  const [newcategoryName, setNewCategory] = useState('');
  const deleteTag = useTempTagStore(state => state.deleteTag);
  const editTag = useTempTagStore(state => state.editTag);

  return (
    <Flex alignItems={'center'} justifyContent={'space-between'}>
      {editMode ? (
        <Input
          border={'none'}
          borderRadius={'5px'}
          bg={'blue.100'}
          color={'mainColor'}
          py={0}
          px={'.75rem'}
          m={0}
          h={'1.5rem'}
          fontSize={'.70rem'}
          value={newcategoryName}
          name="tag"
          onChange={e => setNewCategory(e.target.value)}
          placeholder={tag.name}
          letterSpacing={'-.05em'}
          lineHeight={'32px'}
          fontWeight={'400'}
        />
      ) : (
        <Text
          variant={'category'}
          px={3}
          py={1}
          borderRadius={'5px'}
          maxW={'7rem'}
          overflow={'hidden'}
          textAlign={'left'}
        >
          {tag.name}
        </Text>
      )}
      {/* <Text variant={'category'} px={3} py={1} borderRadius={'5px'}>
        {tag.name}
      </Text> */}
      <Flex>
        <Button
          variant={'ghost'}
          h={'auto'}
          fontSize={'sm'}
          fontWeight={'normal'}
          m={0}
          p={0}
          w={'auto'}
          display={'flex'}
          gap={4}
          _hover={{ bg: 'transparent' }}
          onClick={() => {
            if (editMode) {
              editTag({ ...tag, name: newcategoryName });
              setNewCategory('');
              setEditMode(!editMode);
            }
            if (!editMode) {
              setNewCategory(tag.name);
              setEditMode(!editMode);
            }
          }}
          disabled={editMode && newcategoryName === ''}
        >
          {editMode ? (
            <CheckIcon color="black" height={15} width={15} />
          ) : (
            <PenIcon color="black" height={15} width={15} />
          )}
        </Button>
        <Button
          variant={'ghost'}
          h={'auto'}
          fontSize={'sm'}
          fontWeight={'normal'}
          m={0}
          p={0}
          w={'auto'}
          display={'flex'}
          gap={4}
          _hover={{ bg: 'transparent' }}
          onClick={() => deleteTag(tag.id)}
        >
          <TrashIcon color="black" height={20} width={20} />
        </Button>
      </Flex>
    </Flex>
  );
}
