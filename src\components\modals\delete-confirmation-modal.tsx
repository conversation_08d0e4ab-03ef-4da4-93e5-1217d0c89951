import AssignmentIcon from '@/assets/icons/assignment';
import TrashIcon from '@/assets/icons/trash';
import {
  Button,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  Modal<PERSON>ooter,
  ModalHeader,
  ModalOverlay,
  Text,
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';

interface DeleteConfirmationModalProps {
  isOpen: boolean;
  deleteStep: number;
  redirect?: string;
  handleDelete: () => void;
  handleClose: () => void;
}

function DeleteConfirmationModal({
  isOpen,
  deleteStep,
  redirect,
  handleDelete,
  handleClose,
}: DeleteConfirmationModalProps) {
  const { t } = useTranslation('admin');
  const router = useRouter();

  const handleRedirect = () => {
    router.push(redirect ? redirect : router.pathname);
    handleClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose}>
      <ModalOverlay />
      <ModalContent>
        {deleteStep === 1 ? (
          <>
            <ModalHeader
              display={'flex'}
              justifyContent={'center'}
              alignItems={'center'}
              h={'5rem'}
            >
              <TrashIcon width={30} height={30} color="black" className="my-icon-class" />
            </ModalHeader>
            <ModalCloseButton />
            <ModalBody textAlign={'center'}>
              <Text variant={'subHeaderNormal'} mb={'1rem'}>
                {t('membersList.deleteConfirmation.title')}
              </Text>
              <Text fontSize={'sm'}>{t('membersList.deleteConfirmation.message')}</Text>
            </ModalBody>
            <ModalFooter justifyContent={'center'} gap={4}>
              <Button variant={'rounded'} onClick={handleClose}>
                {t('membersList.deleteConfirmation.buttons.back')}
              </Button>
              <Button variant={'roundedBlue'} colorScheme="red" onClick={handleDelete}>
                {t('membersList.deleteConfirmation.buttons.delete')}
              </Button>
            </ModalFooter>
          </>
        ) : (
          <>
            <ModalHeader
              display={'flex'}
              justifyContent={'center'}
              alignItems={'center'}
              h={'5rem'}
            >
              <AssignmentIcon width={30} height={30} color="black" className="my-icon-class" />
            </ModalHeader>
            <ModalCloseButton />
            <ModalBody textAlign={'center'}>
              <Text variant={'subHeaderNormal'} mb={'1rem'}>
                {t('membersList.deleteConfirmation.success')}
              </Text>
            </ModalBody>
            <ModalFooter justifyContent={'center'}>
              <Button w={'auto'} variant={'roundedBlue'} px={'2rem'} onClick={handleRedirect}>
                {t('membersList.deleteConfirmation.buttons.close')}
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}

export default DeleteConfirmationModal;
