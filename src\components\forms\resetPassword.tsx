import {
  Button,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  Stack,
  Text,
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import React from 'react';

function ResetPassword() {
  const { t } = useTranslation('auth');
  const router = useRouter();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    router.push('/reset-password-2');
  };

  return (
    <Flex
      borderRadius={'20px'}
      w={{ base: '90dvw', sm: '30rem' }}
      h={{ base: 'auto' }}
      justify={'center'}
      gap="4"
      direction="column"
      pt={'1.8rem'}
      pb={'1rem'}
      bg="white"
    >
      <Flex className="text-center" justify={'center'} direction="column" gap={'1rem'}>
        <Text variant={'brand'}>JUSTYOYAKU</Text>
        <Text fontWeight="bold" fontSize={{ base: '2xl', md: '1.5rem' }}>
          {t('resetPassword.title')}
        </Text>
      </Flex>

      <Flex className="text-center" justify={'center'} direction="column" gap={4}>
        <Text fontSize=".93rem" mx="1.5rem">
          {t('resetPassword.description')}
        </Text>
        <hr />
        <form onSubmit={handleSubmit}>
          <Stack mx="1.5rem" gap="4" align="flex-start" mb={'1rem'}>
            <FormControl>
              <FormLabel fontWeight={'400'}>{t('resetPassword.form.emailAddress')}</FormLabel>
              <Input p={'6'} className="border-gray" type="text" />
              <FormErrorMessage> err</FormErrorMessage>
            </FormControl>
          </Stack>
          <Flex px={'1.5rem'}>
            <Button variant={'roundedBlue'} fontSize={'md'} py={'1.4rem'} type="submit">
              {t('resetPassword.buttons.send')}
            </Button>
          </Flex>
        </form>
      </Flex>
    </Flex>
  );
}

export default ResetPassword;
