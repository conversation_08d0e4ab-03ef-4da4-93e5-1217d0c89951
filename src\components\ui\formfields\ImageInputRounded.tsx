import React, { useState } from 'react';
import {
  Flex,
  Text,
  Image,
  FormControl,
  FormLabel,
  Input,
  FormErrorMessage,
  Box,
} from '@chakra-ui/react';

type ImageInputRoundedType = {
  label?: string;
  secondLabel?: string;
  errors?: string;
  value?: string;
};

export default function ImageInputRounded({
  label,
  secondLabel,
  errors,
  value,
}: ImageInputRoundedType) {
  const [src, setSrc] = useState<string>('/imgs/icons/tempProfile.png');

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    setSrc(file ? URL.createObjectURL(file) : '/imgs/icons/tempProfile.png');
  };

  return (
    <FormControl isInvalid={errors ? !!errors : false}>
      <Text>{label}</Text>
      <Flex gap={'1rem'} pt={'1rem'} alignItems={'center'}>
        <Box
          position={'relative'}
          overflow={'hidden'}
          borderRadius={'100%'}
          height={'60px'}
          width={'60px'}
          bg={'gray.200'}
          objectFit={'cover'}
          objectPosition={'center'}
        >
          <Image
            style={{ borderRadius: '100rem', marginRight: '10px', height: 'auto', width: '100%' }}
            // src="/imgs/icons/tempProfile.png"
            src={src}
            alt=""
          />
        </Box>

        <FormLabel
          fontSize={'small'}
          margin={'0'}
          paddingX={'4'}
          paddingY={'2'}
          borderRadius={'50px'}
          border={'1px solid black'}
        >
          {secondLabel ? secondLabel : '写真を変更'}
        </FormLabel>
        <Input name="profileIcon" type="file" value={value} onChange={onChange} display={'none'} />
        <FormErrorMessage>{errors}</FormErrorMessage>
      </Flex>
    </FormControl>
  );
}
