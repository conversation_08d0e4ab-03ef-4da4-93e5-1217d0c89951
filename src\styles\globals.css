@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@100..900&display=swap');
@import './react-big-calendar-custom.css';

:root {
  --max-width: 1100px;
  --border-radius: 12px;
  --font-mono: Inter;

  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;

  --border-gray: 1px solid #e0e0e0;

  --primary-glow: conic-gradient(
    from 180deg at 50% 50%,
    #16abff33 0deg,
    #0885ff33 55deg,
    #54d6ff33 120deg,
    #0071ff33 160deg,
    transparent 360deg
  );
  --secondary-glow: radial-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));

  --tile-start-rgb: 239, 245, 249;
  --tile-end-rgb: 228, 232, 233;
  --tile-border: conic-gradient(
    #00000080,
    #00000040,
    #00000030,
    #00000020,
    #00000010,
    #00000010,
    #00000080
  );

  --callout-rgb: 238, 240, 241;
  --callout-border-rgb: 172, 175, 176;
  --card-rgb: 180, 185, 188;
  --card-border-rgb: 131, 134, 135;
}

.text-center {
  text-align: center;
}

.btn-m3px {
  width: 100%;
  margin: 3px 0;
  border-radius: 50px;
  padding: 1.5rem;
  font-size: 1.2rem;
  display: flex;
  justify-content: flex-start;
}

.btn-m3px img {
  margin-right: 10px;
}

.border-gray {
  border: var(--border-gray);
}

.top-btm-border {
  border-top: var(--border-gray);
  border-bottom: var(--border-gray);
}

.top-border {
  border-top: var(--border-gray);
}

.btn-border {
  border: var(--border-gray);
}

.no-padding {
  padding: 0;
}

.no-m {
  margin: 0;
}

.m-3px {
  margin: 3px;
}

/* 
@media (prefers-color-scheme: light) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;

    --primary-glow: radial-gradient(rgba(1, 65, 255, 0.4), rgba(1, 65, 255, 0));
    --secondary-glow: linear-gradient(
      to bottom right,
      rgba(1, 65, 255, 0),
      rgba(1, 65, 255, 0),
      rgba(1, 65, 255, 0.3)
    );

    --tile-start-rgb: 2, 13, 46;
    --tile-end-rgb: 2, 5, 19;
    --tile-border: conic-gradient(
      #ffffff80,
      #ffffff40,
      #ffffff30,
      #ffffff20,
      #ffffff10,
      #ffffff10,
      #ffffff80
    );

    --callout-rgb: 20, 20, 20;
    --callout-border-rgb: 108, 108, 108;
    --card-rgb: 100, 100, 100;
    --card-border-rgb: 200, 200, 200;
  }
} */

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  font-family: 'Noto Sans JP', sans-serif;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Noto Sans JP', sans-serif;
}

body {
  background-color: #f1f5f9;
}

.chakra-radio {
  padding: 0.6rem 0;
  width: 100%;
}

/* body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

a {
  color: inherit;
  text-decoration: none;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
} */
