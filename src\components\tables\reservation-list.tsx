import React, { ReactNode } from 'react';
import {
  Box,
  Text,
  Flex,
  Button,
  Menu,
  Grid,
  GridItem,
  Input,
  InputGroup,
  InputLeftElement,
  HStack,
  useDisclosure,
} from '@chakra-ui/react'
import { SearchIcon } from '@chakra-ui/icons'
import RightArrowIcon from '@/assets/icons/right-arrow';
import Router from 'next/router';
import { useTempReservationStore } from '@/store/temp/admin/tempAdminReservationStore';
import TableCell from './cells/TableCell';
import AdminReservationModal from '@/components/modals/AdminReservationModal';
import TableHeader from './heads/TableHeader';
import { getStatusBadge } from '@/utils/getStatusBadge';
import { AdminReservationType } from '@/types/adminReservationsTypes';
import { pushToNextPage } from '@/utils/pushToNextPage';



export default function ReservationList() {
  const reservations = useTempReservationStore(state => state.reservations);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedReservation, setSelectedReservation] = React.useState<AdminReservationType | null>();

  return (
    <Box borderRadius="md" px={{base: 0, lg:0, }} py={{ base: 0, lg: 5 }} w="100%">
      <Text display={{base:'none', lg:'block'}} fontSize="xl" fontWeight="bold">予約リスト</Text>

      <Flex bg="white" p={5} mr={{base:'0', lg:8}} mt={{base:'0', lg:4}} borderRadius="md" gap={4} direction={"column"}>
        
        <Flex flexWrap={'wrap'} justifyContent={{base:'flex-start', md:'flex-end'}} my={-4} py={'1rem'} alignItems={'center'}>
          <Flex flexGrow={5}>
            <Text visibility={{base:'visible', md:'hidden'}} fontSize="xl" fontWeight="bold">予約リスト</Text>
          </Flex>
          
          <HStack spacing={'1rem'} >
            <InputGroup w={'auto'}>
              <InputLeftElement pointerEvents="none" height="100%" display="flex" alignItems="center" justifyContent="center">
                <SearchIcon color="gray" />
              </InputLeftElement>
              <Input placeholder="絞り込み" px={'1rem'} w={'8rem'}  py={2} variant="rounded" border={'1px solid gray'} />
            </InputGroup>

            <Button fontSize={'sm'} py={'1.3rem'} w={'auto'} variant={'roundedBlue'} onClick={() => {
              Router.push('/admin/reservations-list/calendar');
            }}>
            予約カレンダー
            </Button>
          </HStack>
        </Flex>
      
        <Box overflowX="auto">
          <Grid 
            templateColumns={{
              md: "1fr 1fr 1fr 1fr 1fr 50px",
              base: "1fr 1fr 1fr 50px"
            }} 
            display={{base: 'grid'}}
            borderBottom="1px solid" 
            borderColor="gray.200" 
            py={2}
            gap={2}
          >

            <TableHeader textAlign={'left'} pl={{base: '1.3rem'}} display={{base:'none', md:'block'}}>ステータス</TableHeader>
            <TableHeader>予約日時</TableHeader>
            <TableHeader>グループ・ユーザー</TableHeader>
            <TableHeader>オプション</TableHeader>
            <TableHeader display={{base:'none', md:'block'}}>連絡先</TableHeader>
            <GridItem></GridItem>

          </Grid>

          <Box>
            {reservations.map((reservation) => (
              <Grid
                key={reservation.id}
                templateColumns={{
                  md: "1fr 1fr 1fr 1fr 1fr 50px",
                  base: "1fr 1fr 1fr 50px"
                }}
                alignItems="center"
                borderBottom="1px solid"
                borderColor="gray.100"
                gap={2}
                py={'.5rem'}
                bg={reservation.status === 'cancelled' ? "#E2E8F0" : "transparent"}
                _hover={{ 
                  bg: "hoverBG1",
                  "& .status-badge": { backgroundColor: "hoverBG2", color: 'white' }
                }}
                onClick={() => {
                  pushToNextPage({ path: '/detail', id: 123 })
                }}
              >
                <TableCell textAlign={'left'} display={{base:'none', md:'block'}}>
                  {getStatusBadge(reservation.status)}
                </TableCell>
                <TableCell>
                  {reservation.dateTime}
                </TableCell>
                <TableCell>
                  {reservation.user}
                </TableCell>
                <TableCell>
                  {reservation.options}
                </TableCell>
                <TableCell display={{base:'none', md:'block'}}>
                  {reservation.contact}
                </TableCell>
                <GridItem>
                  <Menu>
                    <Button variant="ghost" p={0} height="auto" _hover={{ bg: "transparent" }}>
                      <RightArrowIcon width={25} height={25}/>
                    </Button>
                  </Menu>
                </GridItem>
              </Grid>
            ))}
          </Box>
        </Box>
      </Flex>
      <AdminReservationModal selectedReservation={selectedReservation || null} isOpen={isOpen} onClose={onClose} />
    </Box>
  );
}
