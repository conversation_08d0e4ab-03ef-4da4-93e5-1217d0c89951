import { StatusBadge } from '@/components/ui/statusBadge';
import { newsHeaderKeys } from '@/constants/tableHeaders';
import { NewsItemType } from '@/types/newsItemType';
import { pushToNextPage } from '@/utils/pushToNextPage';
import { SearchIcon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  Flex,
  Grid,
  GridItem,
  HStack,
  Image,
  Input,
  InputGroup,
  InputLeftElement,
  Text,
  useToast,
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import TableCell from './cells/TableCell';

function NewsList() {
  const { t } = useTranslation('common');
  const [newsItems, setNewsItems] = useState<NewsItemType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const router = useRouter();
  const toast = useToast();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        const newsResponse = await fetch('/api/news-list');
        if (!newsResponse.ok) {
          throw new Error('Failed to fetch news items');
        }
        const newsData = await newsResponse.json();
        setNewsItems(newsData);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: t('newsList.errors.fetchFailed'),
          description: t('newsList.errors.fetchFailedDescription'),
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [toast, t]);

  return (
    <Box borderRadius="md" py={{ base: 0, md: 5 }} w="100%">
      <Flex
        px={{ base: 0, md: 8, lg: 0 }}
        flexDirection={'row'}
        justifyContent={'space-between'}
        display={{ base: 'none', md: 'flex' }}
      >
        <Text fontSize="xl" fontWeight="bold">
          {t('newsList.title')}
        </Text>
      </Flex>

      <Box
        bg="white"
        p={5}
        mr={{ base: 0, lg: 8 }}
        mt={{ base: 0, md: 4 }}
        mx={{ base: 0, md: 8, lg: 0 }}
        borderRadius="md"
      >
        <Flex
          justifyContent={{ base: 'flex-start', sm: 'space-between', md: 'flex-start' }}
          mb={4}
          flexWrap={'wrap'}
          alignItems={'center'}
        >
          <Flex
            flexDirection={'row'}
            justifyContent={'space-between'}
            display={{ base: 'flex', md: 'none' }}
          >
            <Text fontSize="xl" fontWeight="bold">
              {t('newsList.title')}
            </Text>
          </Flex>

          <HStack spacing={4}>
            <InputGroup maxW="300px">
              <InputLeftElement
                pointerEvents="none"
                height="100%"
                display="flex"
                alignItems="center"
                justifyContent="center"
                pl={'1.5rem'}
              >
                <SearchIcon color="black" />
              </InputLeftElement>
              <Input
                fontSize={'sm'}
                placeholder={t('newsList.search.placeholder')}
                py={'1.5rem'}
                px={0}
                pl={'3rem'}
                variant="rounded"
                color={'gray'}
                border={'1px solid black'}
                w={'8rem'}
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
              />
            </InputGroup>
          </HStack>
        </Flex>

        <Box overflowX="auto">
          <Grid
            templateColumns={{ base: '9rem 11rem 1fr 50px', sm: '9rem 11rem 1fr 50px' }}
            display={{ base: 'none', sm: 'grid' }}
            borderBottom="1px solid"
            borderColor="gray.200"
            py={2}
            pr={{ base: '0', lg: '4rem' }}
            gap={2}
          >
            {newsHeaderKeys.map((headerKey, index) => {
              return (
                <GridItem key={index} fontSize={'md'} textAlign="center">
                  {t(headerKey)}
                </GridItem>
              );
            })}
          </Grid>

          <Box>
            {isLoading ? (
              <Flex justify="center" py={4}>
                {t('newsList.states.loading')}
              </Flex>
            ) : newsItems.length === 0 ? (
              <Flex justify="center" py={4}>
                {t('newsList.states.empty')}
              </Flex>
            ) : (
              newsItems.map(item => (
                <Grid
                  py={'.4rem'}
                  pr={{ base: '0', lg: '4rem' }}
                  key={item.id}
                  templateColumns={{ sm: '150px 180px 1fr 50px' }}
                  display={{ base: 'grid' }}
                  gridTemplateColumns={{ base: 'repeat(2, 1fr)', sm: '150px 180px 1fr 50px' }}
                  alignItems="center"
                  borderBottom="1px solid"
                  borderColor="gray.100"
                  _hover={{
                    bg: 'hoverBG1',
                    cursor: 'pointer',
                    '& .category-badge': {
                      backgroundColor: 'hoverBG2',
                      color: 'white',
                    },
                  }}
                  onClick={() => pushToNextPage({ path: '/news-list-detail', id: item.id })}
                  gap={2}
                >
                  <TableCell>
                    <StatusBadge status={item.category} />
                  </TableCell>
                  <TableCell fontWeight={'bold'}>{item.date}</TableCell>
                  <TableCell fontWeight={'bold'} textAlign="left">
                    {item.title}
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      p={0}
                      height="auto"
                      _hover={{ bg: 'transparent' }}
                      onClick={e => {
                        e.stopPropagation();
                        router.push(`/admin/news-list/news-list-detail/${item.id}`);
                      }}
                    >
                      <Image src="/imgs/icons/arrowRight.svg" alt="Detail" width={25} height={25} />
                    </Button>
                  </TableCell>
                </Grid>
              ))
            )}
          </Box>
        </Box>
      </Box>
    </Box>
  );
}

export default NewsList;
