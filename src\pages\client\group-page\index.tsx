import { sampleGroupMembers } from '@/.temp/sampleGroupMembers';
import * as sampleReservationHistory from '@/.temp/sampleReservationHistory';
import TrashIcon from '@/assets/icons/trash';
import ChangeRepresentativeModal from '@/components/modals/changeRepresentativeModal';
import GroupDetailsEditingCompletedModal from '@/components/modals/GroupDetailsEditingCompletedModal';
import MemberInvitationModal from '@/components/modals/memberInvitationModal';
import ReservationHistoryTable from '@/components/tables/reservationsHistoryTable';
import InviteMembersTrigger from '@/components/ui/card elements/InviteMembersTrigger';
import CardContainer from '@/components/ui/cards/card-container';
import CardCustomerGroupMembers from '@/components/ui/cards/card-customer-group-members';
import CardMyProfileBanner from '@/components/ui/cards/card-my-profile-banner';
import TextPair from '@/components/ui/TextPair';
import TextPairWithCTA from '@/components/ui/TextPairWithCTA';
import { Button, Flex, Text, useDisclosure } from '@chakra-ui/react';
import { useRef, useState } from 'react';

function GroupPage() {
  const [data, setData] = useState({
    clientData: {
      fullName: '斉藤　翼',
      kana: 'サイトウ ツバサ',
      phoneNumber: '08012345678',
      address: '',
      email: '<EMAIL>',
    },
  });

  const controlData = useRef({
    clientData: {
      fullName: data.clientData.fullName,
      kana: data.clientData.kana,
      phoneNumber: data.clientData.phoneNumber,
      address: data.clientData.address,
      email: data.clientData.email,
    },
  });

  const { isOpen, onOpen, onClose } = useDisclosure();
  const {
    isOpen: isRepresentativeOpen,
    onOpen: onRepresentativeOpen,
    onClose: onRepresentativeClose,
  } = useDisclosure();
  const { isOpen: isGroupOpen, onOpen: onGroupOpen, onClose: onGroupClose } = useDisclosure();
  const {
    isOpen: isMemberInvitationOpen,
    onOpen: onMemberInvitationOpen,
    onClose: onMemberInvitationClose,
  } = useDisclosure();

  const [invitationStep, setInvitationStep] = useState(0);
  const [invites, setInvites] = useState(1);
  const maxInvites = 3;
  const [inviteDisabled, setInviteDisabled] = useState(false);

  const handleModalClose = () => {
    onClose();
  };

  const isFormValid = () => {
    return (
      data.clientData.fullName.trim() !== controlData.current.clientData.fullName ||
      data.clientData.kana.trim() !== controlData.current.clientData.kana ||
      data.clientData.phoneNumber.trim() !== controlData.current.clientData.phoneNumber ||
      data.clientData.email.trim() !== controlData.current.clientData.email
    );
  };

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    let isValid = true;

    if (!data.clientData.fullName.trim()) {
      newErrors.fullName = '氏名を入力してください';
      isValid = false;
    }

    if (!data.clientData.kana.trim()) {
      newErrors.kana = 'カナを入力してください';
      isValid = false;
    }

    if (!data.clientData.phoneNumber.trim()) {
      newErrors.phoneNumber = '電話番号を入力してください';
      isValid = false;
    } else if (!/^[0-9]{10,11}$/.test(data.clientData.phoneNumber.replace(/[-\s]/g, ''))) {
      newErrors.phoneNumber = '有効な電話番号を入力してください';
      isValid = false;
    }

    if (!data.clientData.email.trim()) {
      newErrors.email = 'メールアドレスを入力してください';
      isValid = false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.clientData.email)) {
      newErrors.email = '有効なメールアドレスを入力してください';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSave = () => {
    if (validateForm()) {
      controlData.current = {
        clientData: {
          fullName: data.clientData.fullName,
          kana: data.clientData.kana,
          phoneNumber: data.clientData.phoneNumber,
          address: data.clientData.address,
          email: data.clientData.email,
        },
      };
      handleModalClose();
    }
  };

  return (
    <Flex flexDirection={'column'} gap={'1rem'}>
      <Flex>
        <Text variant={'header'}>グループ詳細</Text>
      </Flex>

      <Flex justifyContent={'flex-end'}>
        <Button
          w={'auto'}
          variant={'rounded'}
          bg={'borderGray'}
          borderColor={'transparent'}
          py={'1.3rem'}
          onClick={() => {
            // setNewsList(!newsList);
          }}
        >
          <TrashIcon height={20} width={20} />

          <Text ml={'.5rem'} fontWeight={'normal'}>
            削除
          </Text>
        </Button>
      </Flex>

      <CardMyProfileBanner data={data.clientData} variant={'group'} onOpen={onOpen} />

      <CardCustomerGroupMembers members={sampleGroupMembers} />

      <CardContainer p={'2rem'}>
        <TextPairWithCTA
          header={'時間'}
          subtext={['残り/ 全体']}
          ctaText={'チャージ'}
          onCTAClick={() => {
            onGroupOpen();
          }}
        />

        <TextPair
          variant="card-element"
          header={'メールアドレス'}
          subtext={['<EMAIL>']}
        />

        <TextPair variant="card-element" header={'連絡先'} subtext={['080-1234-1234']} />

        <TextPairWithCTA
          header={'代表者'}
          subtext={['斉藤　翼']}
          ctaText={'代表者変更'}
          onCTAClick={() => {
            onRepresentativeOpen();
          }}
        />

        <TextPairWithCTA
          variant="memberInfo"
          header={'メンバー'}
          subtext={['斉藤　翼']}
          onDelete={() => {
            onGroupOpen();
          }}
        />

        <TextPairWithCTA
          variant="memberInfo"
          header={'メンバー'}
          subtext={['斉藤　翼']}
          onDelete={() => {
            onGroupOpen();
          }}
        />

        <InviteMembersTrigger onMemberInvitationOpen={onMemberInvitationOpen} />

        <TextPair variant="card-element" header={'事前予約可能な件数'} subtext={['1件']} />
        <TextPair
          variant="card-element"
          header={'備考'}
          subtext={['内容', '※施術に関するお問合わせやご要望等']}
        />

        <ReservationHistoryTable
          title={'予約履歴'}
          data={sampleReservationHistory.sampleReservationHistory}
        />

        <Flex w={'100%'}>
          <Button variant={'roundedBlue'} w={'100%'}>
            閉じる
          </Button>
        </Flex>
      </CardContainer>

      <ChangeRepresentativeModal
        onModalOpen={isRepresentativeOpen}
        onModalClose={onRepresentativeClose}
      />

      <GroupDetailsEditingCompletedModal isModalOpen={isGroupOpen} onModalClose={onGroupClose} />

      <MemberInvitationModal
        isMemberInvitationOpen={isMemberInvitationOpen}
        onMemberInvitationClose={onMemberInvitationClose}
        invitationStep={invitationStep}
        setInvitationStep={setInvitationStep}
        invites={invites}
        setInvites={setInvites}
        maxInvites={maxInvites}
        inviteDisabled={inviteDisabled}
        setInviteDisabled={setInviteDisabled}
      />
    </Flex>
  );
}

GroupPage.getInitialProps = async () => {
  return { layoutType: 'client' };
};

export default GroupPage;
