import React, { ReactNode } from 'react'
import { Flex, FormControl, FormLabel, Input, Textarea, TextareaProps, InputProps } from '@chakra-ui/react'

interface InputFieldProps extends Omit<InputProps & TextareaProps, 'onChange' | 'children'> {
    label?: string;
    variant?: string;
    required?: boolean;
    prefix?: string;
    type?: string;
    name?: string;
    placeholder?: string;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    error?: string;
    helperText?: string;
    children?: ReactNode;
}


export default function UnderlinedInputField({
    label,
    variant = 'default',
    required,
    prefix = 'xxx.com/', 
    type = 'text',
    name = '',
    placeholder,
    value,
    onChange,
    error,
    helperText,
    ...rest
}: InputFieldProps) {
  return (

    <Flex
        flexDirection={'column'}
        gap={2}
        borderBottom={'1px solid #E5EAF1'}
        pb={'1rem'}
        mb={'1rem'}
        >
        <FormControl>
            <FormLabel 
                fontSize={'sm'} 
                fontWeight={'bold'} 
                variant={required ? 'required' : ''} 
                >
                {label}
            </FormLabel>
            {variant === 'textArea' ? 
            <Textarea
                p={'6'}
                name="storeOverview"
                // value={formData.storeOverview}
                // onChange={handleInputChange}
                placeholder="内容"
                {...rest}
            />
            : variant === 'prefixed' ? 
            <Flex gap={0}>
                <FormLabel
                    bg={'#F8FAFC'}
                    color={'#94B2D7'}
                    fontSize={'sm'}
                    m={0}
                    px={4}
                    borderLeftRadius={'10px'}
                    border={'1px solid #E5EAF1'}
                    display={'flex'}
                    alignItems={'center'}
                >
                    {prefix}
                </FormLabel>

                <Input
                    p={'6'}
                    name="storeID"
                    type="text"
                    // value={formData.storeID}
                    // onChange={handleInputChange}
                    placeholder={placeholder}
                    borderLeftRadius={0}
                    {...rest}
                />
            </Flex>
            :
            <Input
            p={'6'}
            type={type}
            name={name}
            // value={formData.storeOverview}
            // onChange={handleInputChange}
            placeholder={placeholder}
            {...rest}
            />}

            
            {/* <FormErrorMessage>{errors.groupname}</FormErrorMessage> */}
        </FormControl>
    </Flex>
  )
}
