import Login from '@/components/forms/login';
import Proto from '@/components/ui/proto';
import { Flex } from '@chakra-ui/react';
import '@fontsource/inter';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

export default function LoginPage() {
  return (
    <>
      <Flex
        w={{ base: '100%', md: 'auto' }}
        flexDirection={'column'}
        alignItems={'center'}
        justifyContent={{ base: 'flex-start', md: 'center' }}
        h={'100vh'}
        fontFamily="Inter, sans-serif"
      >
        <Login context="admin" />
        <Flex
          display={{ base: 'none', md: 'flex' }}
          position={'fixed'}
          bottom={0}
          w={'100%'}
          justifyContent={'center'}
          alignItems={'center'}
        >
          <Proto />
        </Flex>
      </Flex>
    </>
  );
}
export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'auth'])),
      layoutType: 'blank',
    },
  };
}
