import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { sampleOptionList } from '@/.temp/optionList/tempOptionList';
import { OptionListType } from '@/types/optionListType';
import { optionListObj } from '@/constants/optionListObj';

type OptionList = {
  optionList: OptionListType[];
  getOption: (id: number) => OptionListType | undefined;
  addOption: (option: OptionListType) => Promise<void>;
  updateOption: (option: OptionListType) => void;
  deleteOption: (id: number) => void;
};

type OptionListData = {
  optionList: OptionListType;
  setOptionList: (option: OptionListType) => Promise<void>;
  // updateOption: (option: OptionListType) => void;
  // deleteOption: (id: number) => void;
};

export const useTempOptionListStore = create<OptionList>()(
  immer(set => ({
    optionList: sampleOptionList,
    addOption: async (option: OptionListType) => {
      await new Promise(resolve => setTimeout(resolve, 1000));
      set(state => {
        state.optionList.push(option);
      });
    },
    getOption: (id: number) => {
      return sampleOptionList.find(o => o.id === id);
    },
    updateOption: (option: OptionListType) => {
      set(state => {
        const index = state.optionList.findIndex(o => o.id === option.id);
        if (index !== -1) {
          state.optionList[index] = option;
        }
      });
    },
    deleteOption: (id: number) => {
      set(state => {
        state.optionList = state.optionList.filter(o => o.id !== id);
      });
    },
  }))
);

export const useOptionListData = create<OptionListData>()(
  immer(set => ({
    optionList: optionListObj,
    setOptionList: async (option: OptionListType) => {
      set(state => {
        state.optionList = option;
      });
    },
  }))
);
