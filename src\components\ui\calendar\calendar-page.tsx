import React, { useState, useEffect } from 'react';
import { 
  Flex, Box, Text, Grid, Button, HStack, 
  useColorModeValue,
  IconButton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  GridItem
} from '@chakra-ui/react';
import { ChevronLeftIcon, ChevronRightIcon, AddIcon } from '@chakra-ui/icons';
import CircleIcon from '@/assets/icons/circle';
import TriangleIcon from '@/assets/icons/triangle-icon';
import { constants } from '@/constants/constants';
import { mainModule } from 'process';
import Router from 'next/router';
import XIcon from '@/assets/icons/xIcon';

const dummyEvents = [
  { id: 1, title: "予約: 鈴木様", start: new Date(new Date(new Date().setDate(new Date().getDate() -10)).setHours(9, 0, 0, 0)).toISOString(), end: new Date(new Date(new Date().setDate(new Date().getDate() - 10)).setHours(10, 30, 0, 0)).toISOString(), room: "Room 01" },
  { id: 2, title: "予約: 鈴木様", start: new Date(new Date(new Date().setDate(new Date().getDate() - 15)).setHours(9, 0, 0, 0)).toISOString(), end: new Date(new Date(new Date().setDate(new Date().getDate() - 15)).setHours(10, 30, 0, 0)).toISOString(), room: "Room 01" },
  { id: 3, title: "予約: 田中様", start: new Date(new Date().setHours(10, 0, 0, 0)).toISOString(), end: new Date(new Date().setHours(11, 30, 0, 0)).toISOString(), room: "Room 01" },
  { id: 4, title: "予約: 佐藤様", start: new Date(new Date().setHours(14, 0, 0, 0)).toISOString(), end: new Date(new Date().setHours(19, 0, 0, 0)).toISOString(), room: "Room 02" },
  { id: 5, title: "予約: 鈴木様", start: new Date(new Date(new Date().setDate(new Date().getDate() + 1)).setHours(9, 0, 0, 0)).toISOString(), end: new Date(new Date(new Date().setDate(new Date().getDate() + 1)).setHours(10, 30, 0, 0)).toISOString(), room: "Room 01" },
  { id: 6, title: "予約: 山田様", start: new Date(new Date(new Date().setDate(new Date().getDate() + 1)).setHours(11, 0, 0, 0)).toISOString(), end: new Date(new Date(new Date().setDate(new Date().getDate() + 1)).setHours(12, 0, 0, 0)).toISOString(), room: "Room 02" },
  { id: 7, title: "予約: 伊藤様", start: new Date(new Date(new Date().setDate(new Date().getDate() + 2)).setHours(15, 0, 0, 0)).toISOString(), end: new Date(new Date(new Date().setDate(new Date().getDate() + 2)).setHours(16, 30, 0, 0)).toISOString(), room: "Room 01" },
];

const getEventsForDate = (date: Date) => {
  const dateString = date.toISOString().split('T')[0];
  // console.log("Looking for events on:", dateString);
  const filteredEvents = dummyEvents.filter(event => {
    const eventDate = new Date(event.start).toISOString().split('T')[0];
    return eventDate === dateString;
  });
  // console.log("Found events:", filteredEvents.length);
  return filteredEvents;
};

const getEventsForHour = (date: Date, hour: number) => {
  const dateString = date.toISOString().split('T')[0];
  
  return dummyEvents.filter(event => {
    const eventDate = new Date(event.start).toISOString().split('T')[0];
    const eventStart = new Date(event.start);
    const eventEnd = new Date(event.end);
    
    return eventDate === dateString && 
           (eventStart.getHours() === hour || 
            (eventStart.getHours() <= hour && eventEnd.getHours() > hour));
  });
};

export default function CalendarPage() {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [view, setView] = useState<'day' | 'week' | 'month'>('month');
  const [events, setEvents] = useState<any[]>([]);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [columns, setColumns] = useState(0);

  // Add this useEffect to log when events should be displayed
  useEffect(() => {
    // console.log("Current date:", currentDate);
    // console.log("Events for current date:", getEventsForDate(currentDate));
  }, [currentDate]);

  const todayBg = useColorModeValue('mainColor', 'blue.900');
  const selectedBg = useColorModeValue('blue.100', 'blue.800');
  const eventBg = useColorModeValue('blue.500', 'blue.400');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const { isOpen, onOpen, onClose } = useDisclosure();

  const goToToday = () => setCurrentDate(new Date());
  
  const goToPrevious = () => {
    const newDate = new Date(currentDate);
    if (view === 'day') {
      newDate.setDate(newDate.getDate() - 1);
    } else if (view === 'week') {
      newDate.setDate(newDate.getDate() - 7);
    } else {
      newDate.setMonth(newDate.getMonth() - 1);
    }
    setCurrentDate(newDate);
  };
  
  const goToNext = () => {
    const newDate = new Date(currentDate);
    if (view === 'day') {
      newDate.setDate(newDate.getDate() + 1);
    } else if (view === 'week') {
      newDate.setDate(newDate.getDate() + 7);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };


  const formatMonthYear = (date: Date) => {
    return `${date.getFullYear()}年${date.getMonth() + 1}月`;
  };

  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (year: number, month: number) => {
    return new Date(year, month, 1).getDay();
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.getDate() === today.getDate() &&
           date.getMonth() === today.getMonth() &&
           date.getFullYear() === today.getFullYear();
  };

  const formatFullDate = (date: Date): string => {
    return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
  };
  
  const generateMonthCalendar = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const daysInMonth = getDaysInMonth(year, month);
    const firstDay = getFirstDayOfMonth(year, month);
    
    const calendar = [];
    let week = [];
    

    for (let i = 0; i < firstDay; i++) {
      const prevMonthDate = new Date(year, month, 0 - i);
      week.unshift({
        date: prevMonthDate,
        isCurrentMonth: false
      });
    }
    
   
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      week.push({
        date,
        isCurrentMonth: true
      });
      
      if (week.length === 7) {
        calendar.push(week);
        week = [];
      }
    }
    
   
    if (week.length > 0) {
      const daysToAdd = 7 - week.length;
      for (let i = 1; i <= daysToAdd; i++) {
        const nextMonthDate = new Date(year, month + 1, i);
        week.push({
          date: nextMonthDate,
          isCurrentMonth: false
        });
      }
      calendar.push(week);
    }
    
    return calendar;
  };

  
  const generateWeekView = () => {
    const date = new Date(currentDate);
    const day = date.getDay();
    date.setDate(date.getDate() - day);
    
    const week = [];
    for (let i = 0; i < 7; i++) {
      const currentDay = new Date(date);
      week.push(currentDay);
      date.setDate(date.getDate() + 1);
    }
    
    return week;
  };

 
  const generateHours = () => {
    const hours = [];
    for (let i = 0; i < 24; i++) {
      hours.push(`${i}:00`);
    }
    return hours;
  };


  const handleClick = (date: Date) => {
    setEvents(getEventsForDate(date));
    console.log("Events for clicked date:", events);
    console.log(events.length);
    setColumns(events.length);
    setSelectedDate(date);
    getEventsForDate(date).length > 0 ? onOpen() : null;
  };



  return (
    <Flex direction="column" h="100%" w="100%">
      
      <Flex flexDirection={'column'} align="center" mb={4}>
        <Flex w={'100%'} justifyContent={'center'}>
            <HStack>
            {/* <Button onClick={goToToday} size="sm" colorScheme="blue">今日</Button> */}
            <IconButton 
                aria-label="Previous" 
                icon={<ChevronLeftIcon  height={25} width={25} />} 
                onClick={goToPrevious}
                size="xs"
                borderRadius={'50rem'}
                bg={'mainColor'}
                color={'white'}
            />
            <Text fontWeight="bold" fontSize="xl">{formatMonthYear(currentDate)}</Text>
            <IconButton 
                aria-label="Next" 
                icon={<ChevronRightIcon height={25} width={25}  />} 
                onClick={goToNext}
                size="xs"
                borderRadius={'50rem'}
                bg={'mainColor'}
                color={'white'}
            />
            </HStack>
        </Flex>
        
        <Flex w={'100%'} alignItems={'flex-end'} justifyContent={'space-between'}>

            <HStack alignItems={'baseline'} gap={'1rem'} p={1}>    
                <Flex alignItems={'center'}>
                    <Flex bg={'mainColor'} w={'10px'} h={'10px'} borderRadius={'full'} mr={1}></Flex>
                    <Text fontWeight={'medium'} fontSize={'sm'}>
                        Room 01
                    </Text>
                </Flex>
                <Flex alignItems={'center'}>
                    <Flex bg={eventBg} w={'10px'} h={'10px'} borderRadius={'full'} mr={1}></Flex>
                    <Text fontWeight={'medium'} fontSize={'sm'}>
                        Room 02
                    </Text>
                </Flex>
                <Flex alignItems={'center'}>
                    <Flex bg={'#845FE1'} w={'10px'} h={'10px'} borderRadius={'full'} mr={1}></Flex>
                    <Text fontWeight={'medium'} fontSize={'sm'}>
                        Room 03
                    </Text>
                </Flex>
                <Flex alignItems={'center'}>
                    <Flex bg={'#78CA2B'} w={'10px'} h={'10px'} borderRadius={'full'} mr={1}></Flex>
                    <Text fontWeight={'medium'} fontSize={'sm'}>
                        Room 04
                    </Text>
                </Flex>
                <Flex alignItems={'center'}>
                    <Flex bg={'#F49751'} w={'10px'} h={'10px'} borderRadius={'full'} mr={1}></Flex>
                    <Text fontWeight={'medium'} fontSize={'sm'}>
                        Room 05
                    </Text>
                </Flex>
            </HStack>

            <HStack border={"1px solid"} borderColor={'mainColor'} gap={1} borderRadius="md" p={1}>
              <Button 
                  size="sm" 
                  fontWeight={'normal'}
                  variant={view === 'day' ? 'blue' : 'ghost'} 
                  onClick={() => setView('day')}
              >
                  日
              </Button>
              <Button 
                  size="sm" 
                  fontWeight={'normal'}
                  variant={view === 'week' ? 'blue' : 'ghost'} 
                  onClick={() => setView('week')}
              >
                  週
              </Button>
              <Button 
                  size="sm" 
                  fontWeight={'normal'}
                  variant={view === 'month' ? 'blue' : 'ghost'} 
                  onClick={() => setView('month')}
              >
                  月
              </Button>
            </HStack>
        </Flex>
      </Flex>

      
      {view === 'month' && (
        <Box border="1px solid" borderColor={borderColor} borderRadius="md" overflow="hidden">
          <Grid templateColumns="repeat(7, 1fr)" bg="gray.50">
            {constants.DAY_NAMES.map((day, i) => (
              <Box key={i} p={2} textAlign="center" fontWeight="bold" borderBottom="1px solid" borderColor={borderColor}>
                {day}
              </Box>
            ))}
          </Grid>
          
          <Grid templateRows={`repeat(${generateMonthCalendar().length}, 1fr)`}>
            {generateMonthCalendar().map((week, weekIndex) => (
              <Grid key={weekIndex} templateColumns="repeat(7, 1fr)">
                {week.map((day, dayIndex) => (
                  <Flex 
                    key={dayIndex}
                    h="9rem"
                    flexDirection={'column'}
                    borderRight={dayIndex < 6 ? "1px solid" : "none"}
                    borderBottom="1px solid"
                    borderColor={borderColor}
                    opacity={day.isCurrentMonth ? 1 : 0.5}
                    cursor="pointer"
                    _hover={{ bg: selectedBg }}
                    onClick={() => handleClick(day.date)}
                    
                  >
                    <Text textAlign={"center"} py={1} color={isToday(day.date)  ? 'white' : 'gray.700'} bg={isToday(day.date) ? todayBg : undefined}>
                      {day.date.getDate()}
                    </Text>
                    
                    <Flex h={'100%'} flexDirection={'column'} alignItems={'center'}>
                      <Box h={'100%'} display={'flex'} flexDirection={'column'} alignItems={'center'} w="100%">
                        {day.isCurrentMonth && getEventsForDate(day.date).slice(0, 2).map((event, idx) => (
                          <Box 
                            key={idx}
                            mt={1} 
                            px={1} 
                            py={0}
                            bg={event.room === "Room 01" ? "mainColor" : eventBg} 
                            color="white" 
                            fontSize="xs" 
                            borderRadius="full"
                            width="8.5rem"
                            overflow="hidden"
                            textOverflow="ellipsis"
                            whiteSpace="nowrap"
                            textAlign="center"
                          >
                            {new Date(event.start).getHours()}:00-{new Date(event.end).getHours()}:00 {event.title}
                          </Box>
                        ))}
                      </Box>

                      {/* {day.isCurrentMonth && getEventsForDate(day.date).length > 2 && (
                        <Flex pb={'.5rem'}>
                          <Text color={'gray.400'} fontSize={'xs'}>
                            他 {getEventsForDate(day.date).length - 2}件
                          </Text>
                        </Flex>
                      )} */}
                      <Flex pb={'.5rem'}>
                          <Text color={'gray.400'} fontSize={'xs'}>
                            他 {getEventsForDate(day.date).length - 2}件
                          </Text>
                        </Flex>
                    </Flex>
                  </Flex>
                ))}
              </Grid>
            ))}
          </Grid>
        </Box>
      )}

      
      {view === 'week' && (
        <Box borderColor={borderColor} borderRadius="md" overflow="hidden">
          <Grid templateColumns="100px repeat(7, 1fr)" bg="gray.50">
            <Box p={2} bg={'white'}></Box>
            {generateWeekView().map((day, i) => (
              <Flex 
                key={i} 
                p={2} 
                justifyContent={"center"}
                fontWeight="bold" 
                borderBottom="1px solid" 
                // borderRight={i < 6 ? "1px solid" : "none"} 
                borderColor={borderColor}
                bg={isToday(day) ? todayBg : 'white'}
                

              >
                <Text color={isToday(day) ? 'white' : 'gray.700'}>{day.getDate()}</Text>
                <Text color={isToday(day) ? 'white' : 'gray.700'}>{constants.DAY_NAMES[i]}</Text>
              </Flex>
            ))}
          </Grid>
          
          <Grid templateColumns="100px repeat(7, 1fr)" maxH="100%" >
            {generateHours().map((hour, hourIndex) => (
              <React.Fragment key={hourIndex}>
                <Box 
                  p={2} 
                  borderBottom="1px solid" 
                  borderRight="1px solid" 
                  borderColor={borderColor}
                  textAlign="right"
                >
                  {hour}
                </Box>
                {generateWeekView().map((day, dayIndex) => (
                  <Grid 
                    key={dayIndex}

                    templateColumns="repeat(5, 1fr)"
                    p={1}
                    borderRight={dayIndex < 6 ? "1px solid" : "none"}
                    borderColor={borderColor}
                    minH="50px"
                    position="relative"
                  >
                    {getEventsForHour(day, parseInt(hour)).map((event, idx) => (
                      <Box 
                        key={idx}
                        position="absolute"
                        // top={`${idx * 25}px`}
                        top={'0'}
                        bottom={'0'}
                        left={event.room === "Room 02" ? "0" : "2rem"}
                        // right="0"
                        p={'1rem'} 
                        bg={event.room === "Room 01" ? "mainColor" : eventBg} 
                        color="white" 
                        fontSize="xs" 
                        // borderRadius="sm"
                        zIndex={1}
                      >
                        {/* {event.title} ({new Date(event.start).getHours()}:00-{new Date(event.end).getHours()}:00) */}
                      </Box>
                    ))}
                  </Grid>
                ))}
              </React.Fragment>
            ))}
          </Grid>
        </Box>
      )}

      
      {view === 'day' && (
        <Grid templateColumns="1fr 1fr" gap={4}>
          <Box borderColor={borderColor} overflow="hidden">
            
            <Box w={"200px"}>
              <Box p={2} color={'white'} textAlign="center" fontWeight="bold" bg={isToday(currentDate) ? todayBg : "gray.50"} borderBottom="1px solid" borderColor={borderColor}>
                {constants.DAY_NAMES[currentDate.getDay()]} {currentDate.getDate()}日
              </Box>
            </Box>
            
            <Box>
              {generateHours().map((hour, hourIndex) => (
                <Flex key={hourIndex} borderBottom="1px solid" borderColor={borderColor}>
                  <Box p={2} w="100px" borderColor={borderColor} textAlign="right">
                    {hour}
                  </Box>
                  <Box p={1} flex="1" minH="50px" position="relative">
                    {getEventsForHour(currentDate, parseInt(hour)).map((event, idx) => (
                      <Box 
                        key={idx}
                        position="absolute"
                        top={`${idx * 25}px`}
                        left="0"
                        right="0"
                        p={1} 
                        bg={event.room === "Room 01" ? "mainColor" : eventBg} 
                        color="white" 
                        fontSize="sm" 
                        borderRadius="sm"
                        zIndex={1}
                      >
                        {event.title} ({new Date(event.start).getHours()}:00-{new Date(event.end).getHours()}:00)
                      </Box>
                    ))}
                  </Box>
                </Flex>
              ))}
            </Box>
          </Box>

          <Flex flexDirection={'column'}>
            <Flex p={'1.5rem'} display={'flex'} flexDirection={'column'} gap={'1rem'}>
              <Box mt={4}>
                <Grid templateColumns="repeat(7, 1fr)" gap={2}>
                  {['月', '火', '水', '木', '金', '土', '日'].map((day, i) => (
                    <Text key={i} textAlign="center" fontSize="sm" color="gray.600">
                      {day}
                    </Text>
                  ))}
                  
                  {[30, 31, 1, 2, 3, 4, 5].map((date, i) => (
                    <Flex key={`week1-${i}`} direction="column" align="center" mb={2}>
                      <Text fontSize="sm" mb={1}>{date}</Text>
                      <CircleIcon color='blue.500'/>
                    </Flex>
                  ))}
                  
                  {[6, 7, 8, 9, 10, 11, 12].map((date, i) => (
                    <Flex key={`week2-${i}`} direction="column" align="center" mb={2}>
                      <Text fontSize="sm" mb={1}>{date}</Text>
                      <CircleIcon color='blue.500'/>
                    </Flex>
                  ))}
                  
                  {[13, 14, 15, 16, 17, 18, 19].map((date, i) => (
                    <Flex key={`week3-${i}`} direction="column" align="center" mb={2}>
                      <Text fontSize="sm" mb={1}>{date}</Text>
                      {date === 16 ? (
                        <XIcon color='red.500'/>
                      ) : (
                        <CircleIcon color='blue.500'/>
                      )}
                    </Flex>
                  ))}
                  
                  {[20, 21, 22, 23, 24, 25, 26].map((date, i) => (
                    <Flex key={`week4-${i}`} direction="column" align="center" mb={2}>
                      <Text fontSize="sm" mb={1}>{date}</Text>
                      {date === 23 ? (
                        <TriangleIcon color='gray.500'/>
                      ) : (
                        <CircleIcon color='blue.500'/>
                      )}
                    </Flex>
                  ))}
                  
                  {[27, 28, 29, 30, 1, 2, 3].map((date, i) => (
                    <Flex key={`week5-${i}`} direction="column" align="center" mb={2}>
                      <Text fontSize="sm" mb={1}>{date}</Text>
                      {i === 3 || i === 4 ? (
                        <XIcon color='red.500'/>
                      ) : i === 6 ? (
                        <TriangleIcon color='gray.500'/>
                      ) : (
                        <CircleIcon color='blue.500'/>
                      )}
                    </Flex>
                  ))}
                </Grid>
                
                <Flex mt={4} justify="space-around">
                  <Flex align="center">
                    <Box w="12px" h="12px" borderRadius="full" bg="blue.700" mr={2}></Box>
                    <Text fontSize="sm">Room 02</Text>
                  </Flex>
                  <Flex align="center">
                    <Box w="12px" h="12px" borderRadius="full" bg="blue.400" mr={2}></Box>
                    <Text fontSize="sm">Room 02</Text>
                  </Flex>
                </Flex>
              </Box>
            </Flex>
            
            <Grid templateColumns={'repeat(2, 1fr)'}>
                <GridItem borderBottom={'1px solid'} borderColor={'borderGray'} p={'1.5rem'} display={'flex'} flexDirection={'column'} gap={'1rem'}>
                  <Text fontWeight={'bold'}>
                    グループ名
                  </Text>
                  <Text fontWeight={'bold'}>
                    2025.03.03 9:00~12:00
                  </Text>
                  <Text fontWeight={'bold'}>
                    4人 / 080-1234-1234
                  </Text>
                </GridItem>
                
                <GridItem borderBottom={'1px solid'} borderColor={'borderGray'} p={'1.5rem'} display={'flex'} flexDirection={'column'} gap={'1rem'}>
                  <Text fontWeight={'bold'}>
                    グループ名
                  </Text>
                  <Text fontWeight={'bold'}>
                    2025.03.03 9:00~12:00
                  </Text>
                  <Text fontWeight={'bold'}>
                    4人 / 080-1234-1234
                  </Text>
                </GridItem>
                <GridItem borderBottom={'1px solid'} borderColor={'borderGray'} p={'1.5rem'} display={'flex'} flexDirection={'column'} gap={'1rem'}>
                  <Text fontWeight={'bold'}>
                    グループ名
                  </Text>
                  <Text fontWeight={'bold'}>
                    2025.03.03 9:00~12:00
                  </Text>
                  <Text fontWeight={'bold'}>
                    4人 / 080-1234-1234
                  </Text>
                </GridItem>
                <GridItem borderBottom={'1px solid'} borderColor={'borderGray'} p={'1.5rem'} display={'flex'} flexDirection={'column'} gap={'1rem'}>
                  <Text fontWeight={'bold'}>
                    グループ名
                  </Text>
                  <Text fontWeight={'bold'}>
                    2025.03.03 9:00~12:00
                  </Text>
                  <Text fontWeight={'bold'}>
                    4人 / 080-1234-1234
                  </Text>
                </GridItem>
            </Grid>
          </Flex>
        </Grid>
      )}



      <Modal isOpen={isOpen} onClose={onClose} isCentered>
        <ModalOverlay />
        <ModalContent borderRadius="1rem" maxW="90%" w="400px">
          <ModalHeader borderBottom="1px solid" borderColor="gray.200" p={4}>
            {selectedDate && formatFullDate(selectedDate)}
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            
            <Box>
              <Flex justifyContent="center" mb={4}>
                {/* <Text fontWeight="bold">{selectedDate && formatFullDate(selectedDate)}</Text> */}
              </Flex>
              
              {/* <Flex mb={3}>
                <Box mr={2}>
                  <Flex alignItems="center">
                    <Box w="12px" h="12px" borderRadius="full" bg="mainColor" mr={1}></Box>
                    <Text fontSize="sm">Room 01</Text>
                  </Flex>
                </Box>
                <Box>
                  <Flex alignItems="center">
                    <Box w="12px" h="12px" borderRadius="full" bg="blue.400" mr={1}></Box>
                    <Text fontSize="sm">Room 02</Text>
                  </Flex>
                </Box>
              </Flex> */}

              
              {generateHours().slice(9, 23).map((hour, idx) => (
                <Box key={idx} height={'3rem'} borderBottom="1px solid" w={"100%"} borderColor="gray.200">
                  <Flex>
                    <Box w="50px" py={2} textAlign="right" pr={3}>
                      <Text fontSize="sm" color="gray.600">{hour}</Text>
                    </Box>
                    {/* <Grid gridTemplateColumns={`repeat(${events.length}, 1fr)`} gap={2} > */}
                    <Grid bg={'red.100'} w={'100%'} h={'100%'} gridTemplateColumns={'repeat(2, 1fr)'} gap={2} >

                      {events.filter(event => new Date(event.start).getHours() === parseInt(hour)).map((event, eventIdx) => (
                        <GridItem 
                          key={eventIdx}
                          mx={1}
                          flex={1}
                          bg={event.room === "Room 01" ? "mainColor" : "blue.400"}
                          color="white"
                          borderRadius="md"
                          p={2}
                          textAlign="center"
                          w={'100%'} 
                          _hover={{ cursor: "pointer" }}
                          onClick={() => {                            
                            // console.log("event", event);
                            Router.push(`/admin/reservations-list/detail/123`);
                          }}>
                          <Text fontWeight="bold">Just Group</Text>
                          <Text>{event.title.includes("人") ? event.title : `4人`}</Text>
                          <Text>Room {event.room.split(" ")[1]}</Text>
                        </GridItem>
                      ))}
                    </Grid>
                  </Flex>
                </Box>
              ))}
              
              <Flex justifyContent="center" mt={4}>
                <Button variant={"roundedBlue"} w={'auto'} py={'.5rem'} px={'2rem'} onClick={onClose} >
                  閉じる
                </Button>
              </Flex>
            </Box>
            
          </ModalBody>
        </ModalContent>
      </Modal>
    </Flex>
  );
}
