import { Button, Flex, FormControl, FormLabel, Input, Link, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';

export default function MembershipRegistrationForm() {
  const { t } = useTranslation('auth');
  return (
    <form action="">
      <Flex bg={'white'} p={5} borderRadius={'1rem'} gap={4} direction={'column'}>
        <Text fontWeight={'bold'} variant={'subHeaderMedium'}>
          {t('membershipRegistration.form.reserverInfo')}
        </Text>

        <FormControl>
          <FormLabel fontSize={'xs'} fontWeight={'normal'} variant={'required'}>
            {t('membershipRegistration.form.reserverName')}
          </FormLabel>
          <Input
            p={'6'}
            name="username"
            type="text"
            placeholder={t('membershipRegistration.form.reserverNamePlaceholder')}
          />
        </FormControl>

        <FormControl>
          <FormLabel fontSize={'xs'} fontWeight={'normal'} variant={'required'}>
            {t('membershipRegistration.form.reserverNameKana')}
          </FormLabel>
          <Input
            p={'6'}
            name="username"
            type="text"
            placeholder={t('membershipRegistration.form.reserverNameKanaPlaceholder')}
          />
        </FormControl>

        <FormControl>
          <FormLabel fontSize={'xs'} fontWeight={'normal'} variant={'required'}>
            {t('membershipRegistration.form.phoneNumber')}
          </FormLabel>
          <Input
            p={'6'}
            name="username"
            type="number"
            placeholder={t('membershipRegistration.form.phoneNumberPlaceholder')}
          />
        </FormControl>

        <FormControl>
          <FormLabel fontSize={'xs'} fontWeight={'normal'} variant={'required'}>
            {t('membershipRegistration.form.emailAddress')}
          </FormLabel>
          <Input
            p={'6'}
            name="username"
            type="text"
            placeholder={t('membershipRegistration.form.emailPlaceholder')}
          />
        </FormControl>

        <FormControl>
          <FormLabel fontSize={'xs'} fontWeight={'normal'} variant={'required'}>
            {t('membershipRegistration.form.initialPassword')}
          </FormLabel>
          <Input
            p={'6'}
            name="username"
            type="text"
            placeholder={t('membershipRegistration.form.passwordPlaceholder')}
          />
        </FormControl>
      </Flex>

      <Flex flexDirection={'column'} gap={4} pt={'1rem'}>
        <Link fontSize={'xs'} variant={'underline'} href={'#'} textAlign={'center'}>
          {t('membershipRegistration.links.forgotPassword')}
        </Link>
        <Button variant={'roundedBlue'} px={'3rem'} w={'auto'}>
          {t('membershipRegistration.buttons.register')}
        </Button>
      </Flex>
    </form>
  );
}
