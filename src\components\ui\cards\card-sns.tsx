import { Flex, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import SnsButton from '../sns-button';

function CardSns() {
  const { t } = useTranslation('profile');

  // TODO: Replace with actual user account linking status from API/store
  // These values should come from user's profile data
  const accountLinkingStatus = {
    google: false,
    apple: false,
    line: true,
  };

  return (
    <Flex flexDirection={'column'} gap={'1rem'}>
      <Text variant={'subHeaderNormal'}>{t('account.linkSnsAccounts')}</Text>
      <Flex
        bg={'white'}
        flexDirection={'column'}
        py={{ base: '1rem', md: '2rem' }}
        px={'1rem'}
        gap={'1rem'}
        borderRadius={' 0 0 1rem 1rem'}
      >
        <SnsButton variant="google" isLinked={accountLinkingStatus.google} />
        <SnsButton variant="apple" isLinked={accountLinkingStatus.apple} />
        <SnsButton variant="line" isLinked={accountLinkingStatus.line} />
      </Flex>
    </Flex>
  );
}

export default CardSns;
