import AssignmentIcon from '@/assets/icons/assignment';
import ProfileIcon from '@/components/ui/profile-icon';
import {
  Button,
  Flex,
  FormControl,
  FormLabel,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalFooter,
  ModalOverlay,
  Select,
  Text,
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';

export default function ChangeChargeModal({
  chargeModalOpen,
  chargeStep,
  chargeAmount,
  setChargeAmount,
  handleChargeSubmit,
  handleCloseChargeModal,
}: {
  chargeModalOpen: boolean;
  chargeStep: number;
  chargeAmount: string;
  setChargeAmount: (value: string) => void;
  handleChargeSubmit: () => void;
  handleCloseChargeModal: () => void;
}) {
  const { t } = useTranslation('admin');
  return (
    <Modal isOpen={chargeModalOpen} onClose={handleCloseChargeModal}>
      <ModalOverlay />
      <ModalContent>
        {chargeStep === 1 ? (
          <>
            <Flex
              pt={'3rem'}
              flexDirection={'column'}
              justifyContent={'center'}
              alignItems={'center'}
            >
              <Text variant={'subHeaderNormal'} textAlign={'center'}>
                {t('membersList.chargeModal.title')}
              </Text>

              <Flex w={'100%'} p={'1.5rem'} pb={0}>
                <ProfileIcon />
              </Flex>
            </Flex>
            <ModalCloseButton />
            <ModalBody py={0} fontSize={'sm'} textAlign={'center'}>
              <Flex flexDirection={'column'} textAlign={'left'}>
                <Text fontSize={'sm'} fontWeight={'bold'}>
                  {t('membersList.chargeModal.remaining')}
                </Text>
                <Text mb={4} fontSize={'lg'} fontWeight={'bold'}>
                  {t('membersList.chargeModal.numCredit')}
                </Text>
              </Flex>
              <FormControl>
                <FormLabel fontSize={'xs'}>{t('membersList.chargeModal.charge')}</FormLabel>
                <Select
                  placeholder={t('membersList.chargeModal.placeholder')}
                  value={chargeAmount}
                  onChange={e => setChargeAmount(e.target.value)}
                  textAlign="left"
                >
                  <option value="1000">1000円</option>
                  <option value="3000">3000円</option>
                  <option value="5000">5000円</option>
                  <option value="10000">10000円</option>
                </Select>
              </FormControl>
            </ModalBody>
            <ModalFooter gap={4} justifyContent={'center'}>
              <Button px={'3rem'} w={'auto'} variant="rounded" onClick={handleCloseChargeModal}>
                {t('membersList.chargeModal.buttons.cancel')}
              </Button>
              <Button
                px={'3rem'}
                w={'auto'}
                variant="roundedBlue"
                onClick={handleChargeSubmit}
                isDisabled={!chargeAmount}
              >
                {t('membersList.chargeModal.buttons.charge')}
              </Button>
            </ModalFooter>
          </>
        ) : (
          <>
            <Flex justifyContent={'center'} alignItems={'center'} h={'5rem'}>
              <AssignmentIcon width={30} height={30} color="black" />
            </Flex>
            <ModalCloseButton />
            <ModalBody fontSize={'sm'} textAlign={'center'}>
              <Text fontSize={'md'} fontWeight={'bold'}>
                {t('membersList.chargeModal.success')}
              </Text>
            </ModalBody>
            <ModalFooter gap={4} justifyContent={'center'}>
              <Button px={'3rem'} w={'auto'} variant="roundedBlue" onClick={handleCloseChargeModal}>
                {t('membersList.chargeModal.buttons.close')}
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}
