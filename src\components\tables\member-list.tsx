import PlusIcon from '@/assets/icons/plus-icon';
import ThreeDotsIcon from '@/assets/icons/three-dots';
import { StatusBadge } from '@/components/ui/statusBadge';
import { SearchIcon } from '@chakra-ui/icons';
import {
  Box,
  Button,
  Flex,
  Grid,
  GridItem,
  HStack,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import React, { useState } from 'react';
import AddMemberList from '../modals/add-member-list';
import ChangeChargeModal from '../modals/change-charge-modal';
import DeleteConfirmationModal from '../modals/delete-confirmation-modal';
import FilterModal from '../modals/filter-modal';

import { sampleMembers } from '@/.temp/sampleMembers';
import { memberListHeaders } from '@/constants/tableHeaders';
import { pushToNextPage } from '@/utils/pushToNextPage';
import TableCell from './cells/TableCell';

function MemberList() {
  const { t } = useTranslation('admin');
  const [members, setMembers] = useState(sampleMembers);
  const [filteredMembers, setFilteredMembers] = useState(sampleMembers);
  const { isOpen, onOpen, onClose } = useDisclosure();

  const newMemberDefault = {
    status: '',
    group: '',
    remainingTime: 0,
    totalTime: 0,
    chargeEnabled: true,
    contact: '',
    email: '',
    availableTime: '',
    capacity: 0,
    maxAdvanceReservations: 0,
    advanceReservationDays: 0,
    remarks: '',
  };
  const [newMember, setNewMember] = useState(newMemberDefault);

  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [deleteStep, setDeleteStep] = useState(1);
  const [memberToDelete, setMemberToDelete] = useState<number | null>(null);

  const [chargeModalOpen, setChargeModalOpen] = useState(false);
  const [chargeStep, setChargeStep] = useState(1);
  const [memberToCharge, setMemberToCharge] = useState<number | null>(null);
  const [chargeAmount, setChargeAmount] = useState('');

  const [filterModalOpen, setFilterModalOpen] = useState(false);
  const [filters, setFilters] = useState({
    keyword: '',
    status: '',
    groupType: '',
  });

  const handleChargeClick = (memberId: number) => {
    setMemberToCharge(memberId);
    setChargeStep(1);
    setChargeAmount('');
    setChargeModalOpen(true);
  };

  const handleChargeSubmit = () => {
    if (memberToCharge !== null && chargeAmount) {
      setChargeStep(2);
    }
  };

  const handleCloseChargeModal = () => {
    setChargeStep(1);
    setChargeModalOpen(false);
  };

  const handleFilterChange = (newFilters: {
    keyword: string;
    status: string;
    groupType: string;
  }) => {
    setFilters(newFilters);
  };

  const handleApplyFilters = () => {
    let filtered = [...members];

    // Filter by keyword (search in group name and contact)
    if (filters.keyword.trim()) {
      filtered = filtered.filter(
        member =>
          member.group.toLowerCase().includes(filters.keyword.toLowerCase()) ||
          member.contact.includes(filters.keyword)
      );
    }

    // Filter by status
    if (filters.status) {
      filtered = filtered.filter(member => member.status === filters.status);
    }

    // Filter by group type
    if (filters.groupType) {
      filtered = filtered.filter(member => member.group === filters.groupType);
    }

    setFilteredMembers(filtered);
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setNewMember({
      ...newMember,
      [name]: value,
    });
  };

  const handleAddMember = () => {
    const newId = members.length > 0 ? Math.max(...members.map(m => m.id)) + 1 : 1;
    const memberToAdd = {
      id: newId,
      ...newMember,
    };

    const updatedMembers = [...members, memberToAdd];
    setMembers(updatedMembers);
    setFilteredMembers(updatedMembers);
    setNewMember(newMemberDefault);
  };

  const [steps, setSteps] = React.useState(0);

  const nextStep = () => {
    if (newMember === newMemberDefault) return;
    console.log(newMember);

    setSteps(steps + 1);
  };

  const handleDeleteClick = (memberId: number) => {
    setMemberToDelete(memberId);
    setDeleteStep(1);
    setDeleteModalOpen(true);
  };

  const handleDelete = () => {
    if (memberToDelete !== null) {
      const updatedMembers = members.filter(member => member.id !== memberToDelete);
      setMembers(updatedMembers);
      setFilteredMembers(updatedMembers);
      setDeleteStep(2);
    }
  };

  const handleCloseDeleteModal = () => {
    setDeleteStep(1);
    setDeleteModalOpen(false);
  };

  return (
    <Box borderRadius="md" py={{ base: 0, md: 5 }} w="100%">
      <Flex
        flexDirection={'row'}
        justifyContent={'space-between'}
        pb={{ base: 0, md: 5, lg: 0 }}
        px={{ base: '1rem', lg: 0 }}
        display={{ base: 'none', md: 'flex' }}
      >
        <Text fontSize="xl" fontWeight="bold">
          {t('membersList.title')}
        </Text>
        <Button
          h={'auto'}
          fontSize={'sm'}
          w={'auto'}
          m={0}
          mr={{ base: '0', lg: '3rem' }}
          py={'.7rem'}
          px={'.9rem'}
          fontWeight={'normal'}
          variant={'roundedBlue'}
          display={'flex'}
          gap={'.5rem'}
          onClick={onOpen}
        >
          <PlusIcon width={20} height={20} color="white" />
          {t('membersList.buttons.addMember')}
        </Button>
      </Flex>

      <Flex
        bg="white"
        p={5}
        mr={{ base: '0', lg: 8 }}
        mt={{ base: '0', lg: 4 }}
        borderRadius="md"
        gap={4}
        direction={'column'}
      >
        <Flex
          p={{ base: 0, md: 5 }}
          direction={{ base: 'row', md: 'column' }}
          justifyContent={'space-between'}
        >
          <Flex display={{ base: 'flex', md: 'none' }}>
            <Text fontSize="xl" fontWeight="bold">
              {t('membersList.title')}
            </Text>
          </Flex>
          <Flex justifyContent="flex-end" my={-4}>
            <HStack spacing={4}>
              <Button
                maxW={{ base: '8rem', md: '8rem' }}
                py={2}
                px={3}
                variant="rounded"
                border={'1px solid gray'}
                bg="white"
                fontSize="sm"
                fontWeight="normal"
                leftIcon={<SearchIcon color="gray" />}
                onClick={() => setFilterModalOpen(true)}
              >
                {t('membersList.filter.buttonText')}
              </Button>
            </HStack>
          </Flex>
        </Flex>

        <Box overflowX="hidden">
          <Grid
            templateColumns={{
              md: '1fr 1fr 1fr 1fr 1fr 50px',
              base: '1fr 1fr 1fr 50px',
            }}
            display={{ base: 'grid' }}
            borderBottom="1px solid"
            borderColor="gray.200"
            py={2}
            gap={2}
          >
            {memberListHeaders.map((header, index) => (
              <GridItem
                key={header}
                fontSize={'sm'}
                textAlign={index === 0 ? 'left' : 'center'}
                display={index === 0 || index === 5 ? { base: 'none', md: 'block' } : 'block'}
              >
                {index === 0
                  ? t('membersList.table.headers.status')
                  : index === 1
                  ? t('membersList.table.headers.groupName')
                  : index === 2
                  ? t('membersList.table.headers.time')
                  : index === 3
                  ? t('membersList.table.headers.charge')
                  : index === 4
                  ? t('membersList.table.headers.contact')
                  : ''}
              </GridItem>
            ))}
          </Grid>

          <Box>
            {filteredMembers.map(member => (
              <Grid
                key={member.id}
                templateColumns={{
                  md: '1fr 1fr 1fr 1fr 1fr 50px',
                  base: '1fr 1fr 1fr 50px',
                }}
                alignItems="center"
                borderBottom="1px solid"
                borderColor="gray.100"
                gap={2}
                bg={member.status === 'deleted' ? '#E2E8F0' : 'transparent'}
                _hover={{
                  bg: 'hoverBG1',
                  '& .status-badge': { backgroundColor: 'hoverBG2', color: 'white' },
                }}
              >
                <TableCell textAlign={'left'} display={{ base: 'none', md: 'block' }}>
                  <StatusBadge status={member.status} />
                </TableCell>
                <TableCell textAlign={'center'}>{member.group}</TableCell>
                <TableCell
                  textAlign={'center'}
                >{`${member.remainingTime} / ${member.totalTime}`}</TableCell>
                <TableCell textAlign={'center'}>
                  <Button
                    fontSize={'xs'}
                    py={'.7rem'}
                    px={'2rem'}
                    variant="roundedBlue"
                    w={'auto'}
                    h={'auto'}
                    m={0}
                    onClick={() => handleChargeClick(member.id)}
                    isDisabled={!member.chargeEnabled}
                  >
                    {t('membersList.table.actions.charge')}
                  </Button>
                </TableCell>
                <TableCell textAlign={'center'} display={{ base: 'none', md: 'block' }}>
                  {member.contact}
                </TableCell>

                <GridItem>
                  <Menu>
                    <MenuButton
                      as={Button}
                      variant="ghost"
                      fontSize={'2xs'}
                      p={0}
                      height="auto"
                      _hover={{ bg: 'transparent' }}
                    >
                      <ThreeDotsIcon height={25} width={25} />
                    </MenuButton>
                    <MenuList minW="100px">
                      <MenuItem
                        fontSize="xs"
                        onClick={() =>
                          pushToNextPage({ path: '/member-details-main', id: member.id })
                        }
                      >
                        {t('membersList.table.actions.details')}
                      </MenuItem>
                      <MenuItem fontSize="xs" onClick={() => handleDeleteClick(member.id)}>
                        {t('membersList.table.actions.delete')}
                      </MenuItem>
                    </MenuList>
                  </Menu>
                </GridItem>
              </Grid>
            ))}
          </Box>
        </Box>
      </Flex>

      <AddMemberList
        isOpen={isOpen}
        onClose={onClose}
        newMember={newMember}
        handleInputChange={handleInputChange}
        handleAddMember={handleAddMember}
        nextStep={nextStep}
        steps={steps}
        setSteps={setSteps}
      />

      <DeleteConfirmationModal
        isOpen={deleteModalOpen}
        deleteStep={deleteStep}
        handleDelete={handleDelete}
        handleClose={handleCloseDeleteModal}
      />

      <ChangeChargeModal
        chargeModalOpen={chargeModalOpen}
        chargeStep={chargeStep}
        chargeAmount={chargeAmount}
        setChargeAmount={setChargeAmount}
        handleChargeSubmit={handleChargeSubmit}
        handleCloseChargeModal={handleCloseChargeModal}
      />

      <FilterModal
        isOpen={filterModalOpen}
        onClose={() => setFilterModalOpen(false)}
        filters={filters}
        onFilterChange={handleFilterChange}
        onApplyFilters={handleApplyFilters}
      />
    </Box>
  );
}

export default MemberList;
