{"buttons": {"back": "Back", "change": "Change", "showAll": "Show All", "reserve": "Reserve", "requestReservation": "Request Reservation", "addPeople": "Add People", "checkReservation": "Check Reservation", "cancelReservation": "Cancel Reservation"}, "navigation": {"previousWeek": "Prev", "nextWeek": "Next", "weekView": "Week", "monthView": "Month"}, "calendar": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "mondayShort": "M", "tuesdayShort": "T", "wednesdayShort": "W", "thursdayShort": "T", "fridayShort": "F", "saturdayShort": "S", "sundayShort": "S", "closedDay": "Closed", "dateSelection": "Date Selection"}, "formats": {"yearMonth": "{{year}}/{{month}}", "yearMonthDay": "{{year}}/{{month}}/{{day}}", "perPerson": "/person", "coursePrice": "¥{{price}}", "people": "people"}, "store": {"shareSpaceInfo": "Share Space Otemachi Basic Information", "address": "Address", "addressValue": "Protobuilding, 1-10-5 <PERSON><PERSON><PERSON>, Chiyoda-ku, Tokyo", "businessHours": "Business Hours", "officialWebsite": "Official Website", "storeInformation": "Store Information", "storeLocation": "{{location}} within {{distance}}", "storePricing": "Lunch ¥{{lunch}} · Dinner ¥{{dinner}}", "storeHoursToday": "Today ({{day}}) {{hours}}", "shopDescription": "{{shopName}} is an authentic yakiniku restaurant located in {{location}}, Tokyo. With carefully selected high-quality meats prepared by experienced chefs, we bring out the deep flavors and tender textures. The sophisticated and relaxing atmosphere...", "membersOnly": "Members Only"}, "reservations": {"viewAllReservationMenus": "View all reservation menus (4)", "reservationPlan": "Reservation Plan", "visitDateTime": "Visit Date/Time", "numberOfVisitors": "Number of Visitors", "reservationNumber": "Reservation Number", "requestReservationNumber": "Request Reservation Number", "reservationChangeComplete": "Reservation Change Complete", "reservationComplete": "Your reservation is complete.", "reservationHistory": "Reservation History", "precautions": "Precautions", "reservationPrecautions": "Reservation Precautions", "contactInfoMessage": "We may contact you to confirm your reservation. Please register a contact number that is easy to reach.", "cancellationPolicy": "Cancellation Policy", "cancellationPolicyDetails": "Please cancel your reservation by the day before your visit to avoid cancellation fees. Same-day cancellations are possible, but we may restrict reservations for those who repeatedly cancel on the same day. Please be aware of this in advance.", "confirmCancellation": "Do you want to cancel your reservation?", "cancellationComplete": "Your reservation has been cancelled.", "viewCancellationDetails": "View cancellation details here."}, "courses": {"beefFeastCourse": "Celebration Beef Feast - Gorgeous Meat Festival Course", "courseDescription": "The \"Celebration Beef Feast - Gorgeous Meat Festival Course\" delivers luxury worthy of a special day...", "courseImage": "Course Image"}, "status": {"visited": "Visited", "cancelled": "Cancelled", "usage": "Usage"}, "messages": {"visitedThanks": "Thank you for your visit.", "cancelMessage": "We look forward to your next reservation."}, "withdrawal": {"withdraw": "Withdraw", "withdrawal": "<PERSON><PERSON><PERSON>", "cancel": "Cancel", "leave_now_warning": "If you leave now,", "benefits_lost": "you will lose your benefits.", "points_warning": "If you withdraw, you will lose your points and benefits.", "points": "Points", "points_value": "{{points}}P", "immediate_loss": "Immediate Loss"}, "creditCharge": {"title": "Time", "creditUnit": "Credit", "buttons": {"charge": "Charge"}}, "faq": {"title": "Frequently Asked Questions", "questions": {"question1": {"title": "How can I change my reservation?", "answer": "You can easily change your reservation within the app. First, access your My Page and select the reservation you want to change. Then, enter the new date, time, or details you want to modify and press the confirmation button. Once the change is complete, a confirmation email will be sent. If you encounter any issues, please contact our support center."}, "question2": {"title": "How can I cancel my reservation?", "answer": "To cancel your reservation, go to your My Page and find the reservation you want to cancel. Click on the reservation details and select the 'Cancel' option. Please note that cancellation policies may apply depending on the timing of your cancellation. A confirmation email will be sent once the cancellation is processed."}, "question3": {"title": "What payment methods are accepted?", "answer": "We accept various payment methods including credit cards (Visa, MasterCard, JCB, American Express), debit cards, and digital wallets. You can also use our credit system for convenient payments. All transactions are securely processed to protect your financial information."}, "question4": {"title": "How do I earn and use credits?", "answer": "Credits can be earned through various activities such as making reservations, referring friends, or participating in promotional events. You can use credits to pay for services or get discounts on future reservations. Check your credit balance in your My Page and apply them during the checkout process."}, "question5": {"title": "What should I do if I forgot my password?", "answer": "If you forgot your password, click on the 'Forgot Password' link on the login page. Enter your registered email address and we'll send you instructions to reset your password. Follow the link in the email to create a new password. If you don't receive the email, please check your spam folder or contact support."}, "question6": {"title": "How can I contact customer support?", "answer": "You can contact our customer support team through multiple channels: email <NAME_EMAIL>, use the in-app chat feature, or call our support hotline during business hours. Our team is available to help you with any questions or issues you may have."}}}, "newsList": {"title": "News List", "search": {"placeholder": "Filter"}, "table": {"headers": {"category": "Category", "date": "Registration Date", "title": "Title", "actions": ""}}, "states": {"loading": "Loading...", "empty": "No news available"}, "errors": {"fetchFailed": "An error occurred", "fetchFailedDescription": "An error occurred while fetching data."}, "category": {"important": "Important", "news": "News", "event": "Event"}}, "optionslist": {"title": "Options Management", "buttons": {"addOption": "Add Option"}, "filters": {"category": "Category", "categoryOptions": {"course": "Course", "single": "Single Item"}, "tag": "Tag", "tagOptions": {"requestReservation": "Request Reservation", "membersOnly": "Members Only"}}, "empty": {"title": "No options available yet", "description": "There are currently no registered options.\nWhen you add options, they will be displayed here.", "addButton": "Add Option"}, "card": {"priceFormat": "¥{{price}} / {{people}} people", "editButton": "Edit"}, "edit": {"title": "News Registration", "deleteModal": {"title": "Do you want to delete this option?", "description": "Please note that deleted options\ncannot be recovered.", "backButton": "Back", "deleteButton": "Delete", "successTitle": "Option has been deleted.", "closeButton": "Close"}, "form": {"deleteOptionButton": "Option Management", "coverImage": "Cover Image", "categoryTagManagement": "Category & Tag Management", "menuTitle": "Menu Title", "menuTitlePlaceholder": "<PERSON>u Name", "price": "Price", "pricePlaceholder": "¥ 00000", "numberOfPeople": "Number of People", "numberOfPeoplePlaceholder": "No relation, 1 person, etc.", "menuDetails": "<PERSON><PERSON>", "menuDetailsPlaceholder": "Please enter content", "detailedOptions": "Detailed Options", "reservationTimeSlot": "Reservation Time Slot", "reservationTimeDescription": "You can set the reservation time per session.", "fixed": "Fixed", "variable": "Variable", "reservationAvailability": "Reservation Availability", "reservationAvailabilityDescription": "Specify the times when reservations are available.", "businessHours": "Follow business hours", "customSetting": "Set individually", "maxReservations": "Maximum Reservations", "reservationPeriod": "Reservation Period", "reservationPeriodDescription": "You can set the reservation start and cancellation acceptance periods.", "reservationStartSetting": "Set reservation start", "afterReservation": "After reservation", "submitButton": "Register", "reservationInterval": "Set reservation interval", "reservationIntervalDescription1": "Set an interval after the reservation usage time,", "reservationIntervalDescription2": "to automatically insert unavailable time slots."}, "options": {"noRelation": "No relation", "onePerson": "1 person", "twoOrMore": "2 or more", "threePeople": "3 people", "fourOrMore": "4 or more", "fivePeople": "5 people", "sixOrMore": "6 or more", "twoHours": "2 hours", "minutes00": "00 min", "minutes15": "15 min", "minutes30": "30 min", "minutes45": "45 min"}}}, "privacyPolicy": {"title": "Privacy Policy", "sections": {"introduction": {"title": "1. Introduction", "content": "This application (hereinafter referred to as \"this app\") establishes the following privacy policy to properly protect and manage users' personal information."}, "informationCollection": {"title": "2. Information We Collect", "content": "This app may collect the following information:", "items": {"contact": "Contact information (email address, phone number)", "reservationHistory": "Reservation history", "locationInfo": "Location information (when necessary for service provision)", "otherInfo": "Other information necessary for service provision"}}, "usagePurpose": {"title": "3. Purpose of Use", "content": "The collected personal information is used for the following purposes:", "items": {"reservationService": "Providing reservation services", "customerSupport": "Responding to inquiries", "serviceImprovement": "Analysis for service improvement", "legalRequirements": "When legally required"}}, "thirdPartySharing": {"title": "4. Third Party Disclosure", "content": "We will not provide personal information to third parties except when there is consent from the individual or when required by law."}, "securityManagement": {"title": "5. Security Management", "content": "We implement appropriate security measures to prevent leakage, loss, or damage of personal information."}, "dataRights": {"title": "6. Disclosure, Correction, and Deletion Requests", "content": "We will appropriately respond to requests from individuals for disclosure, correction, deletion, etc. of their personal information."}, "contact": {"title": "7. Contact Information", "content": "For inquiries regarding this privacy policy, please contact us at:", "email": "Email: <EMAIL>", "phone": "Phone: 000-0000-0000"}}}}