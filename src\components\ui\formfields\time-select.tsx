import { Flex, SelectField } from '@chakra-ui/react';
import React from 'react';

function TimeSelect({ width }: any) {
  return (
    <Flex w={'100%'} justifyContent={'center'} alignItems={'center'} gap={2}>
      <SelectField
        fontSize={'md'}
        borderRadius={'10px'}
        border={'1px solid #E5EAF1'}
        w={width ? width : 'auto'}
        px={3}
        py={3}
      >
        <option value="option1">9:00</option>
        <option value="option2">10:00</option>
        <option value="option3">11:00</option>
        <option value="option3">12:00</option>
        <option value="option3">13:00</option>
        <option value="option3">14:00</option>
        <option value="option3">15:00</option>
      </SelectField>
      -
      <SelectField
        fontSize={'md'}
        borderRadius={'10px'}
        border={'1px solid #E5EAF1'}
        w={width ? width : 'auto'}
        px={3}
        py={3}
      >
        <option value="option1">9:00</option>
        <option value="option2">10:00</option>
        <option value="option3">11:00</option>
        <option value="option3">12:00</option>
        <option value="option3">13:00</option>
        <option value="option3">14:00</option>
        <option value="option3">15:00</option>
      </SelectField>
    </Flex>
  );
}

export default TimeSelect;
