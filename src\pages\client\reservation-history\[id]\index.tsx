import BackIcon from '@/assets/icons/back';
import ReservationCancellationModal from '@/components/modals/ReservationCancellationModal';
import CardText from '@/components/ui/cards/card-text';
import { useTempReservationStore } from '@/store/temp/tempReservationStore';
import { Button, Flex, Text, useDisclosure } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useRouter } from 'next/router';

function ReservationHistory() {
  const { t } = useTranslation('common');
  const { isOpen, onOpen, onClose } = useDisclosure();
  const router = useRouter();

  const data = useTempReservationStore(state => state.reservations);
  const reservationId = Number(router.query.id);
  const reservation = data.find(res => res.id === reservationId);

  return (
    <Flex flexDirection={'column'} gap={'.5rem'}>
      <Flex bg={{ base: 'white', md: 'unset' }} w={'100%'} alignItems={'center'}>
        <Button
          variant={'ghost'}
          w={'auto'}
          p={0}
          m={0}
          onClick={() => {
            router.back();
          }}
        >
          <BackIcon width={30} height={30} color="black" className="my-icon-class" />
        </Button>
        <Text fontSize={'lg'} fontWeight={'bold'}>
          {t('reservations.reservationHistory')}
        </Text>
      </Flex>

      {reservation ? (
        <CardText variant={'reservation-single'} data={reservation} onOpen={onOpen} />
      ) : (
        <Text>{t('messages.reservationNotFound')}</Text>
      )}
      {reservation && (
        <ReservationCancellationModal
          isOpen={isOpen}
          onClose={onClose}
          onOpen={onOpen}
          data={reservation}
        />
      )}
    </Flex>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'profile', 'auth'])),
      layoutType: 'client',
    },
  };
}

export async function getStaticPaths() {
  return {
    paths: [],
    fallback: 'blocking',
  };
}

export default ReservationHistory;
