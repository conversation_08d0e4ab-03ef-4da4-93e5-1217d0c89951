import React from 'react'
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  FormControl,
  FormLabel,
  Input,
  ModalFooter,
  <PERSON><PERSON>,
  Text,
  Flex,
} from '@chakra-ui/react'
import CirclePlusIcon from '@/assets/icons/circle-plus'

export default function MemberInvitationModal({
    isMemberInvitationOpen,
    inviteDisabled,
    invitationStep,
    maxInvites,
    invites,
    setInvites,
    setInvitationStep,
    setInviteDisabled,
    onMemberInvitationClose,
}: {
    isMemberInvitationOpen: boolean;
    inviteDisabled: boolean;
    invitationStep: number;
    maxInvites: number;
    invites: number;
    setInvites: (value: number) => void;
    setInvitationStep: (value: number) => void;
    setInviteDisabled: (value: boolean) => void;
    onMemberInvitationClose: () => void;

}) {
  return (
    <Modal
    isOpen={isMemberInvitationOpen}
    onClose={() => {
        onMemberInvitationClose();
        setInvitationStep(0);
        setInvites(1);
        setInviteDisabled(false);
    }}
    >
    <ModalOverlay />
    <ModalContent>
        {invitationStep === 0 && (
        <ModalHeader display={'flex'} flexDirection={'column'} gap={'2rem'}>
            <Text variant={'header'}>メンバー招待</Text>
            {inviteDisabled && <Text variant={'error'}>これ以上の追加はできません。</Text>}
        </ModalHeader>
        )}

        {invitationStep === 1 && (
        <ModalHeader display={'flex'} flexDirection={'column'} gap={'2rem'}>
            <Text textAlign={'center'} variant={'brand'}>
            JUSTYOYAKU
            </Text>
        </ModalHeader>
        )}
        {invitationStep === 0 && (
        <ModalBody>
            <FormLabel>メールアドレス</FormLabel>
            <FormControl display={'flex'} flexDirection={'column'} gap={'1rem'}>
            {Array.from({ length: invites }, (_, index) => (
                <Input
                key={index}
                p={'6'}
                className="border-gray"
                type="email"
                placeholder="<EMAIL>"
                />
            ))}

            {invitationStep === 0 && (
                <Button
                w={'100%'}
                variant={'normal'}
                onClick={() => {
                    if (invites < maxInvites) {
                    setInvites(invites + 1);
                    } else {
                    setInviteDisabled(true);
                    }
                }}
                >
                <Flex py={'.6rem'} alignItems={'center'} justifyContent={'center'} gap={'1rem'}>
                    <Text
                    textDecoration={'underline'}
                    color={inviteDisabled ? 'disabledBlue' : 'mainColor'}
                    fontWeight={'bold'}
                    >
                    メンバー追加
                    </Text>
                    <CirclePlusIcon
                    width={25}
                    height={25}
                    color={inviteDisabled ? '#5C6ED3' : '#2B42CA'}
                    />
                </Flex>
                </Button>
            )}
            </FormControl>
        </ModalBody>
        )}

        {invitationStep === 1 && (
        <ModalBody>
            <Text textAlign={'center'} fontWeight={'bold'} variant={'subHeaderMedium'}>
            メールが送信されました。
            </Text>
        </ModalBody>
        )}
        <ModalFooter justifyContent={'center'}>
        {invitationStep === 0 && (
            <Button
            w={'100%'}
            fontSize={'md'}
            px={'3rem'}
            fontWeight={'normal'}
            variant={'roundedBlue'}
            onClick={() => {
                setInvitationStep(1);
            }}
            >
            送信
            </Button>
        )}

        {invitationStep === 1 && (
            <Button
            w={'100%'}
            px={'3rem'}
            fontWeight={'bold'}
            bg={'gray.100'}
            borderColor={'transparent'}
            variant={'rounded'}
            onClick={() => {
                setInvites(1);
                setInvitationStep(0);
                setInviteDisabled(false);
                onMemberInvitationClose();
            }}
            >
            閉じる
            </Button>
        )}
        </ModalFooter>
    </ModalContent>
    </Modal>
  )
}
