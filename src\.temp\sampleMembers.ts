export const sampleMembers = [
  {
    id: 1,
    status: 'pending',
    group: 'グループA',
    remainingTime: 5,
    totalTime: 10,
    chargeEnabled: true,
    contact: '080-1234-1234',
    email: '<EMAIL>',
    availableTime: '09:00 - 18:00',
    capacity: 5,
    maxAdvanceReservations: 3,
    advanceReservationDays: 7,
    remarks: 'First-time group',
  },
  {
    id: 2,
    status: 'active',
    group: 'グループB',
    remainingTime: 7,
    totalTime: 10,
    chargeEnabled: true,
    contact: '080-1234-1234',
    email: '<EMAIL>',
    availableTime: '10:00 - 19:00',
    capacity: 6,
    maxAdvanceReservations: 4,
    advanceReservationDays: 10,
    remarks: '',
  },
  {
    id: 3,
    status: 'deleted',
    group: 'グループC',
    remainingTime: 0,
    totalTime: 8,
    chargeEnabled: false,
    contact: '080-1234-1234',
    email: '<EMAIL>',
    availableTime: '08:00 - 17:00',
    capacity: 4,
    maxAdvanceReservations: 2,
    advanceReservationDays: 5,
    remarks: 'Disabled due to inactivity',
  },
  {
    id: 4,
    status: 'pending',
    group: 'グループD',
    remainingTime: 6,
    totalTime: 10,
    chargeEnabled: true,
    contact: '080-1234-1234',
    email: '<EMAIL>',
    availableTime: '11:00 - 20:00',
    capacity: 7,
    maxAdvanceReservations: 5,
    advanceReservationDays: 14,
    remarks: '',
  },
  {
    id: 5,
    status: 'active',
    group: 'グループE',
    remainingTime: 9,
    totalTime: 10,
    chargeEnabled: true,
    contact: '080-1234-1234',
    email: '<EMAIL>',
    availableTime: '07:00 - 16:00',
    capacity: 8,
    maxAdvanceReservations: 6,
    advanceReservationDays: 30,
    remarks: 'Popular group',
  },
  {
    id: 6,
    status: 'deleted',
    group: 'グループF',
    remainingTime: 0,
    totalTime: 6,
    chargeEnabled: false,
    contact: '080-1234-1234',
    email: '<EMAIL>',
    availableTime: '12:00 - 21:00',
    capacity: 3,
    maxAdvanceReservations: 1,
    advanceReservationDays: 3,
    remarks: 'Archived',
  },
];
