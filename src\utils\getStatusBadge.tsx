import { BadgeStatusType } from '@/constants/badgeStatusType';
import { Badge } from '@chakra-ui/react';

export const getStatusBadge = (status: string) => {
  type ReservationStatus = BadgeStatusType['BADGE_STATUS'];

  const statusMap: Record<ReservationStatus, { bg: string; color: string; text: string }> = {
    confirmed: { bg: '#ECEFF1', color: 'black', text: '確認済み' },
    pending: { bg: '#DEE7FB', color: '#2B42CA', text: '承認待ち' },
    cancelled: { bg: '#607D8B', color: '#ECEFF1', text: 'キャンセル' },
    active: { bg: '#ECEFF1', color: 'black', text: '正常' },
    deleted: { bg: '#607D8B', color: 'white', text: '削除済み' },
    important: { bg: 'alert', color: 'white', text: '重 要' },
    news: { bg: 'stroke', color: 'black', text: 'News' },
    event: { bg: '#E8F5E8', color: '#2E7D32', text: 'Event' },
  };

  const { bg, color, text } =
    status in statusMap
      ? statusMap[status as ReservationStatus]
      : { bg: status, color: 'gray', text: status };

  return (
    <Badge
      className="status-badge"
      color={color}
      bg={bg}
      px={2}
      py={1}
      minW={'5rem'}
      textAlign={'center'}
      borderRadius="md"
      fontWeight={'normal'}
      textTransform={'none'}
    >
      {text}
    </Badge>
  );
};
