import ResetPassword from '@/components/forms/resetPassword';
import LanguageSwitcher from '@/components/ui/language-switcher';
import { Flex } from '@chakra-ui/react';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { Inter } from 'next/font/google';

const inter = Inter({ subsets: ['latin'] });

export default function ResetPasswordPage() {
  return (
    <>
      <Flex justifyContent="flex-end" width="100%" m={2} p={2}>
        <LanguageSwitcher />
      </Flex>
      <Flex
        flexDirection={'column'}
        alignItems={'center'}
        justifyContent={{ base: 'flex-start', md: 'center' }}
        h={'100vh'}
        className={inter.className}
      >
        <ResetPassword />
      </Flex>
    </>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'auth'])),
      layoutType: 'blank',
    },
  };
}
