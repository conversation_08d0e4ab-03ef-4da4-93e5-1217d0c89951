import { sampleRooms } from '@/.temp/sampleRooms';
import { formatFullDate } from '@/utils/formatDates';
import { getRoomColor } from '@/utils/getRoomColor';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  VStack,
  Box,
  Flex,
  Button,
  Text,
} from '@chakra-ui/react';
import moment from 'moment';
import { useRouter } from 'next/router';
import React from 'react';
import { ReservationEvent } from '@/types/ReservationEventType';

type CalendarReservationModalProps = {
  isOpen: boolean;
  onClose: () => void;
  selectedEvent: ReservationEvent | null;
};

export const CalendarReservationModal = ({
  isOpen,
  onClose,
  selectedEvent,
}: CalendarReservationModalProps) => {
  const Router = useRouter();

  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered>
      <ModalOverlay />
      <ModalContent borderRadius="1rem" maxW="90%" w="400px">
        <ModalHeader borderBottom="1px solid" borderColor="gray.200" p={4}>
          {selectedEvent && formatFullDate(selectedEvent.start)}
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody p={6}>
          {selectedEvent && (
            <VStack align="stretch" spacing={4}>
              <Box>
                <Text fontSize="lg" fontWeight="bold" color="mainColor">
                  {selectedEvent.groupName}
                </Text>
                <Text fontSize="md" color="gray.700">
                  {selectedEvent.customerName}
                </Text>
              </Box>

              <Box>
                <Text fontSize="sm" color="gray.600" mb={1}>
                  時間
                </Text>
                <Text fontWeight="medium">
                  {moment(selectedEvent.start).format('HH:mm')} -{' '}
                  {moment(selectedEvent.end).format('HH:mm')}
                </Text>
              </Box>

              <Box>
                <Text fontSize="sm" color="gray.600" mb={1}>
                  部屋
                </Text>
                <Flex alignItems="center">
                  <Box
                    w="12px"
                    h="12px"
                    borderRadius="full"
                    bg={getRoomColor({ roomName: selectedEvent.room, rooms: sampleRooms })}
                    mr={2}
                  />
                  <Text fontWeight="medium">{selectedEvent.room}</Text>
                </Flex>
              </Box>

              <Box>
                <Text fontSize="sm" color="gray.600" mb={1}>
                  人数
                </Text>
                <Text fontWeight="medium">{selectedEvent.peopleCount}人</Text>
              </Box>

              <Box>
                <Text fontSize="sm" color="gray.600" mb={1}>
                  電話番号
                </Text>
                <Text fontWeight="medium">{selectedEvent.phoneNumber}</Text>
              </Box>

              <Box>
                <Text fontSize="sm" color="gray.600" mb={1}>
                  ステータス
                </Text>
                <Text
                  fontWeight="medium"
                  color={
                    selectedEvent.status === 'confirmed'
                      ? 'green.500'
                      : selectedEvent.status === 'pending'
                      ? 'orange.500'
                      : 'red.500'
                  }
                >
                  {selectedEvent.status === 'confirmed'
                    ? '確定'
                    : selectedEvent.status === 'pending'
                    ? '保留中'
                    : 'キャンセル'}
                </Text>
              </Box>

              <Flex justifyContent="center" mt={4}>
                <Button
                  variant="roundedBlue"
                  w="auto"
                  py=".5rem"
                  px="2rem"
                  onClick={() => {
                    Router.push(`/admin/reservations-list/detail/${selectedEvent.id}`);
                  }}
                  mr={2}
                >
                  詳細を見る
                </Button>
                <Button variant="roundedBlue" w="auto" py=".5rem" px="2rem" onClick={onClose}>
                  閉じる
                </Button>
              </Flex>
            </VStack>
          )}
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};
