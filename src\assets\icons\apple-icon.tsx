import React from 'react';

interface AppleIconProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

const AppleIcon: React.FC<AppleIconProps> = ({
  width = 18,
  height = 18,
  color = '#1E293B',
  className,
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.2022 7.07696C12.9586 7.07696 13.9068 6.54988 14.4714 5.84711C14.9828 5.21023 15.3556 4.32079 15.3556 3.43135C15.3556 3.31056 15.345 3.18977 15.3237 3.09094C14.4821 3.12388 13.47 3.67292 12.8627 4.40863C12.3833 4.96865 11.9466 5.84711 11.9466 6.74753C11.9466 6.8793 11.9679 7.01107 11.9785 7.055C12.0318 7.06598 12.117 7.07696 12.2022 7.07696ZM9.53889 20.3637C10.5723 20.3637 11.0304 19.6499 12.3194 19.6499C13.6298 19.6499 13.9174 20.3417 15.068 20.3417C16.1973 20.3417 16.9537 19.2656 17.6674 18.2114C18.4664 17.0036 18.7967 15.8176 18.818 15.7627C18.7434 15.7408 16.5808 14.8294 16.5808 12.2709C16.5808 10.0527 18.2853 9.05349 18.3812 8.97663C17.252 7.30755 15.5368 7.26363 15.068 7.26363C13.8002 7.26363 12.7669 8.05424 12.117 8.05424C11.4139 8.05424 10.487 7.30755 9.38974 7.30755C7.30167 7.30755 5.18164 9.08643 5.18164 12.4465C5.18164 14.5329 5.96999 16.74 6.93945 18.1675C7.77042 19.3754 8.49485 20.3637 9.53889 20.3637Z"
        fill={color}
      />
    </svg>
  );
};

export default AppleIcon;
