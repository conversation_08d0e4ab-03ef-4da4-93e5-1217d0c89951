import ClientFooter from '@/components/ui/client-footer';
import ClientHeader from '@/components/ui/client-header';
import { Box, Container, Flex } from '@chakra-ui/react';
import '@fontsource/inter';
import { ReactNode } from 'react';

type ClientLayoutProps = {
  children: ReactNode;
};

export default function ClientLayout({ children }: ClientLayoutProps) {
  return (
    <Flex position={'relative'} direction="column" minH="100vh" fontFamily="Inter, sans-serif">
      <ClientHeader />
      <Box
        as="main"
        flex="1"
        flexDirection={'column'}
        alignItems={'center'}
        justifyContent={'center'}
        h={'100vh'}
        bg={'#F1F5F9'}
        px={{ base: 0, md: 8 }}
        py={6}
      >
        <Container maxW="25rem" pt={{ base: '4rem' }}>
          {children}
        </Container>
      </Box>
      <ClientFooter />
    </Flex>
  );
}
