import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

// Define the Reservation type
interface Reservation {
  id: string;
  date: string;
  time: string;
  userId: string;
  storeId: string;
  status: 'pending' | 'confirmed' | 'cancelled';
  options?: string[];
}

// Define the store state type
interface ReservationState {
  reservations: Reservation[];
  selectedReservation: Reservation | null;
  isLoading: boolean;
  error: string | null;
}

// Define the store actions
interface ReservationActions {
  fetchReservations: () => Promise<void>;
  fetchReservationById: (id: string) => Promise<void>;
  createReservation: (reservation: Omit<Reservation, 'id' | 'status'>) => Promise<void>;
  updateReservation: (id: string, data: Partial<Reservation>) => Promise<void>;
  cancelReservation: (id: string) => Promise<void>;
  clearSelectedReservation: () => void;
  clearError: () => void;
}

// Create the store with immer middleware for easier state updates
export const useReservationStore = create<ReservationState & ReservationActions>()(
  immer(set => ({
    // Initial state
    reservations: [],
    selectedReservation: null,
    isLoading: false,
    error: null,

    // Actions
    fetchReservations: async () => {
      try {
        set(state => {
          state.isLoading = true;
          state.error = null;
        });

        // Replace with your actual API call
        const response = await fetch('/api/reservations');

        if (!response.ok) {
          throw new Error('Failed to fetch reservations');
        }

        const data = await response.json();

        set(state => {
          state.reservations = data;
          state.isLoading = false;
        });
      } catch (error) {
        set(state => {
          state.isLoading = false;
          state.error = error instanceof Error ? error.message : 'An unknown error occurred';
        });
      }
    },

    fetchReservationById: async (id: string) => {
      try {
        set(state => {
          state.isLoading = true;
          state.error = null;
        });

        // Replace with your actual API call
        const response = await fetch(`/api/reservations/${id}`);

        if (!response.ok) {
          throw new Error('Failed to fetch reservation');
        }

        const data = await response.json();

        set(state => {
          state.selectedReservation = data;
          state.isLoading = false;
        });
      } catch (error) {
        set(state => {
          state.isLoading = false;
          state.error = error instanceof Error ? error.message : 'An unknown error occurred';
        });
      }
    },

    createReservation: async reservation => {
      try {
        set(state => {
          state.isLoading = true;
          state.error = null;
        });

        // Replace with your actual API call
        const response = await fetch('/api/reservations', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(reservation),
        });

        if (!response.ok) {
          throw new Error('Failed to create reservation');
        }

        const newReservation = await response.json();

        set(state => {
          state.reservations.push(newReservation);
          state.isLoading = false;
        });
      } catch (error) {
        set(state => {
          state.isLoading = false;
          state.error = error instanceof Error ? error.message : 'An unknown error occurred';
        });
      }
    },

    updateReservation: async (id: string, data: Partial<Reservation>) => {
      try {
        set(state => {
          state.isLoading = true;
          state.error = null;
        });

        // Replace with your actual API call
        const response = await fetch(`/api/reservations/${id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });

        if (!response.ok) {
          throw new Error('Failed to update reservation');
        }

        const updatedReservation = await response.json();

        set(state => {
          const index = state.reservations.findIndex((res: { id: string }) => res.id === id);
          if (index !== -1) {
            state.reservations[index] = updatedReservation;
          }

          if (state.selectedReservation?.id === id) {
            state.selectedReservation = updatedReservation;
          }

          state.isLoading = false;
        });
      } catch (error) {
        set(state => {
          state.isLoading = false;
          state.error = error instanceof Error ? error.message : 'An unknown error occurred';
        });
      }
    },

    cancelReservation: async (id: string) => {
      try {
        set(state => {
          state.isLoading = true;
          state.error = null;
        });

        // Replace with your actual API call
        const response = await fetch(`/api/reservations/${id}/cancel`, {
          method: 'POST',
        });

        if (!response.ok) {
          throw new Error('Failed to cancel reservation');
        }

        const updatedReservation = await response.json();

        set(state => {
          const index = state.reservations.findIndex((res: { id: string }) => res.id === id);
          if (index !== -1) {
            state.reservations[index] = updatedReservation;
          }

          if (state.selectedReservation?.id === id) {
            state.selectedReservation = updatedReservation;
          }

          state.isLoading = false;
        });
      } catch (error) {
        set(state => {
          state.isLoading = false;
          state.error = error instanceof Error ? error.message : 'An unknown error occurred';
        });
      }
    },

    clearSelectedReservation: () => {
      set(state => {
        state.selectedReservation = null;
      });
    },

    clearError: () => {
      set(state => {
        state.error = null;
      });
    },
  }))
);
