import PenIcon from '@/assets/icons/pen';
import TrashIcon from '@/assets/icons/trash';
import { Button, Flex } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';

export default function EditDeleteDesktopBtns({
  editMode,
  setEditMode,
  onDeleteOpen,
}: {
  editMode: boolean;
  setEditMode: (value: boolean) => void;
  onDeleteOpen: () => void;
}) {
  const { t } = useTranslation('admin');
  return (
    <Flex
      display={{ base: 'none', md: 'flex' }}
      justifyContent={{ base: 'space-between', sm: 'flex-end' }}
      alignItems={'center'}
      gap={'1rem'}
    >
      <Button
        variant={'rounded'}
        color={'black'}
        bg={'borderGray'}
        fontSize={'xs'}
        fontWeight={'normal'}
        gap={2}
        py={'.5rem'}
        w={'auto'}
        onClick={onDeleteOpen}
      >
        <TrashIcon width={20} height={20} color="black" className="my-icon-class" />
        {t('memberDetails.buttons.delete')}
      </Button>

      {editMode ? null : (
        <Button
          variant={'roundedBlue'}
          fontSize={'xs'}
          fontWeight={'normal'}
          gap={2}
          py={'.5rem'}
          w={'auto'}
          onClick={() => {
            setEditMode(true);
          }}
        >
          <PenIcon width={12} height={12} color="white" className="my-icon-class" />
          {t('memberDetails.buttons.edit')}
        </Button>
      )}
    </Flex>
  );
}
