import ArrowDownIcon from '@/assets/icons/arrow-down';
import { ReservationType } from '@/types/reservarionType';
import { Button, Flex, Image, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import Router from 'next/router';

function CardText({
  text,
  variant,
  data,
  onOpen,
}: {
  text?: string;
  variant?: string;
  data?: ReservationType;
  onOpen?: () => void;
}) {
  const { t } = useTranslation('common');

  return (
    <Flex
      bg={'white'}
      flexDirection={'column'}
      p={{ base: '1rem', md: '1.5rem' }}
      borderRadius={'1rem'}
    >
      {variant === 'note' ? (
        <Flex flexDirection={'column'} bg={'mainBackGroundColor'} gap={'2px'}>
          <Flex gap={'1rem'} pb={'1.5rem'} flexDirection={'column'} bg={'white'}>
            <Text fontSize={'md'} fontWeight={'medium'} mb={'1rem'}>
              {t('reservations.precautions')}
            </Text>
            <Text fontSize={'md'} fontWeight={'medium'}>
              {t('reservations.reservationPrecautions')}
            </Text>
            <Text fontSize={'sm'}>{t('reservations.contactInfoMessage')}</Text>
          </Flex>

          <Flex py={'2rem'} flexDirection={'column'} bg={'white'}>
            <Text fontSize={'md'} fontWeight={'medium'} mb={'1rem'}>
              {t('reservations.cancellationPolicy')}
            </Text>
            <Text fontSize={'sm'}>{t('reservations.cancellationPolicyDetails')}</Text>
          </Flex>
        </Flex>
      ) : variant === 'store-info' ? (
        <Text fontSize={'sm'} fontWeight={'bold'} textAlign={'center'}>
          {t('store.storeInformation')}
        </Text>
      ) : variant === 'reservation-history' || variant === 'reservation-single' ? (
        <Flex flexDirection={'column'} gap={'1rem'}>
          {variant === 'reservation-history' ? (
            <Flex gap={'.5rem'}>
              <Text variant={`${data?.status === 'visited' ? 'category' : 'categoryGray'}`}>
                {data?.status === 'visited' ? t('status.visited') : t('status.cancelled')}
              </Text>
              <Text fontSize={'xs'}>
                {data?.status === 'visited'
                  ? t('messages.visitedThanks')
                  : t('messages.cancelMessage')}
              </Text>
            </Flex>
          ) : null}

          <Flex
            justifyContent={'center'}
            alignItems={'center'}
            position={'relative'}
            gap={'1rem'}
            borderBottom={'1px dashed'}
            borderColor={'gray.400'}
            pb={'1rem'}
          >
            {variant === 'reservation-single' ? (
              <Flex
                justifyContent={'center'}
                alignItems={'center'}
                position={'relative'}
                overflow={'hidden'}
                borderRadius={'.8rem'}
                height={'7rem'}
                w={'10rem'}
              >
                <Image
                  style={{
                    objectFit: 'cover',
                    height: '100%',
                    objectPosition: 'center',
                  }}
                  src={'/imgs/icons/profileIcon.svg'}
                  alt="option"
                />
              </Flex>
            ) : null}

            <Flex flexDirection={'column'} gap={'.5rem'}>
              <Text variant={'brand'} fontSize={'1.5rem'}>
                {data?.storeName}
              </Text>
              <Text fontWeight={'bold'}>{data?.storeKana}</Text>
              <Text fontSize={'xs'}>{data?.location}</Text>
            </Flex>
          </Flex>

          <Flex flexDirection={'column'} gap={'1rem'}>
            <Flex gap={'.5rem'} alignItems={'center'}>
              <Text variant={'category'}>{t('reservations.visitDateTime')}</Text>
              <Text fontSize={'md'} fontWeight={'bold'}>
                {data?.dateTime}
              </Text>
            </Flex>
            <Flex gap={'.5rem'} alignItems={'center'}>
              <Text variant={'category'}>{t('reservations.numberOfVisitors')}</Text>
              <Text fontSize={'md'} fontWeight={'bold'}>
                {data?.people}
                {` ${t('formats.people')}`}
              </Text>
            </Flex>
            <Flex gap={'.5rem'} alignItems={'center'}>
              <Text variant={'category'}>{t('reservations.reservationNumber')}</Text>
              <Text fontSize={'md'} fontWeight={'bold'}>
                {data?.reservationNumber}
              </Text>
            </Flex>
          </Flex>

          {variant === 'reservation-single' ? (
            <Flex gap={'1rem'}>
              <Button
                variant={'rounded'}
                w={'100%'}
                onClick={() => {
                  Router.back();
                }}
              >
                {t('buttons.back')}
              </Button>
              <Button
                variant={'roundedBlue'}
                w={'100%'}
                onClick={() => {
                  onOpen?.();
                }}
              >
                {t('buttons.change')}
              </Button>
            </Flex>
          ) : null}
        </Flex>
      ) : (
        <>
          <Text fontSize={'sm'}>{text}</Text>

          <Flex w={'100%'} justifyContent={'flex-end'}>
            <Button w={'auto'} p={0} variant={'ghost'} fontWeight={'normal'} color={'mainColor'}>
              {t('buttons.showAll')}
              <ArrowDownIcon width={20} height={20} color="blue" />
            </Button>
          </Flex>
        </>
      )}
    </Flex>
  );
}

export default CardText;
