import PlusIcon from '@/assets/icons/plus-icon';
import OptionSettings from '@/components/tables/option-settings';
import Empty from '@/components/ui/empty';
import { Button, Flex, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import router from 'next/router';
import { useState } from 'react';

export default function OptionsSettingsPage() {
  const { t } = useTranslation('common');
  const [optionList, setOptionList] = useState(true);

  return (
    <Flex w={'100%'} flexDirection={'column'} pr={{ base: '0', sm: '6' }}>
      <Flex
        bg={{ base: 'white', sm: 'unset' }}
        p={{ base: '1rem', sm: 0 }}
        justifyContent={'space-between'}
        alignItems={'center'}
        height={'5rem'}
        w={'100%'}
      >
        <Text fontSize={'lg'} fontWeight={'bold'}>
          {t('optionslist.title')}
        </Text>

        <Button
          w={'auto'}
          variant={'roundedBlue'}
          fontWeight={'normal'}
          p={'1rem'}
          display={'flex'}
          gap={2}
          onClick={() => {
            router.push('/admin/options-list/option-list-add');
          }}
        >
          <PlusIcon width={20} height={20} color="white" />
          {t('optionslist.buttons.addOption')}
        </Button>
      </Flex>

      {optionList ? <OptionSettings /> : <Empty setOptionList={setOptionList} />}
    </Flex>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'admin'])),
      layoutType: 'withSidebar',
    },
  };
}
