import React from 'react';
import { Flex, Text, SelectField } from '@chakra-ui/react';

type DropDownProps = {
  label?: string;
  subtext?: string[];
  dropDownLabel?: string;
  hourOptions?: string[];
  options?: string[];
};

type dataProps = {
  data?: DropDownProps[];
};

export default function DropDownFormCard({ data }: dataProps) {
  return (
    <Flex flexDirection={'column'} borderRadius={'10px'} border={'1px solid #e2e8f0'} p={'1rem'}>
      {data?.map((item, index) => (
        <>
          <Flex flexDirection={'column'}>
            <Text variant={'subHeaderNormal'}>{item.label}</Text>

            {item.subtext?.map((text, index) => (
              <Text fontSize={'sm'} lineHeight={'1.2'} key={index}>
                {text}
              </Text>
            ))}

            {/* <Text fontSize={'sm'} lineHeight={'1.2'}>
              予約の利用時間の後にインターバルを設定して、
              <br />
              自動的に予約不可となる時間を挿入することができます。
            </Text> */}
          </Flex>
          <Flex alignItems={'center'} gap={2} my={'1rem'}>
            <Text fontSize={'sm'}>{item.dropDownLabel}</Text>

            <SelectField
              border={'1px solid #E5EAF1'}
              borderRadius={'10px'}
              px={'.5rem'}
              py={'.7rem'}
            >
              {item.hourOptions?.map(option => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </SelectField>
            <SelectField
              border={'1px solid #E5EAF1'}
              borderRadius={'10px'}
              pl={'.5rem'}
              pr={'1.5rem'}
              py={'.7rem'}
            >
              {item.options?.map(option => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </SelectField>
          </Flex>
        </>
      ))}
    </Flex>
  );
}
