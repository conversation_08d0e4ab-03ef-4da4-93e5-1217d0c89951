import React from 'react'
import { Flex, Text, Box } from '@chakra-ui/react'
import DropDownIcon from '@/assets/icons/dropdown'

export default function DropdownFAQ({ 
  question, 
  answer, 
  isOpen, 
  setIsOpen }: { 
    question?: string; 
    answer?: string; 
    isOpen: boolean; 
    setIsOpen: (value: boolean) => void 
  }) {
  return (
    <>
      <Flex
        justifyContent="space-between"
        alignItems="center"
        p="1rem"
        border="1px solid #E5EAF1"
        cursor="pointer"
        onClick={() => setIsOpen(!isOpen)}
        bg="white"
      >
        <Flex alignItems={'center'} bg="white" gap={2}>
          <Text fontWeight="bold" fontSize={'xs'} color={'mainColor'}>
            Q.{' '}
          </Text>
          <Text fontWeight="bold" fontSize={'xs'}>
            {question}
          </Text>
        </Flex>
        <Box transform={isOpen ? 'rotate(180deg)' : 'rotate(0deg)'} transition="transform 0.2s">
          <DropDownIcon />
        </Box>
      </Flex>

      {isOpen && (
        <Box p="1rem" pl="1.5rem">
          <Flex gap={2}>
            <Text fontWeight="bold" fontSize={'md'} color={'mainColor'}>
              A.{' '}
            </Text>
            <Text fontSize={'xs'}>
              {answer}
            </Text>
          </Flex>
        </Box>
      )}
    </>
  )
}
