import NewsList from '@/components/tables/news-list';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

export default function NewsListPage() {
  return <NewsList />;
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'admin'])),
      layoutType: 'withSidebar',
    },
  };
}
