import PenIcon from '@/assets/icons/pen';
import TrashIcon from '@/assets/icons/trash';
import { optionListObj } from '@/constants/optionListObj';
import { useTempCatStore, useTempTagStore } from '@/store/temp/admin/tempCatTagStore';
import { useOptionListData, useTempOptionListStore } from '@/store/temp/admin/tempOptionList';
import { OptionListType } from '@/types/optionListType';
import { CheckIcon } from '@chakra-ui/icons';
import { Button, Flex, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import Router from 'next/router';
import React, { useEffect } from 'react';
import CatTag from '../ui/formfields/CatTag';
import DropDown from '../ui/formfields/DropDown';
import DropDownFormCard from '../ui/formfields/DropDownFormCard';
import ImageInput from '../ui/formfields/ImageInput';
import InputField from '../ui/formfields/InputField';
import RadioSelectWithTwoOptions from '../ui/formfields/radio-select-two-options';

type OptionListProps = {
  page: 'add' | 'edit';
  onOpen: () => void;
  isCatAndTagOpen?: boolean;
  onCatAndTagOpen?: () => void;
  data?: OptionListType | undefined;
};

function OptionList({ page, onOpen, onCatAndTagOpen, data }: OptionListProps) {
  const { t } = useTranslation('common');
  const [fixed, setFixed] = React.useState(true);

  const setOptionList = useOptionListData(state => state.setOptionList);
  const updateOption = useTempOptionListStore(state => state.updateOption);
  const addOption = useTempOptionListStore(state => state.addOption);
  const deleteOption = useTempOptionListStore(state => state.deleteOption);
  const optionListData = useOptionListData(state => state.optionList);

  const categories = useTempCatStore(state => state.categories);
  const tags = useTempTagStore(state => state.tags);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    setOptionList({ ...optionListData, [e.target.name]: e.target.value });
  };

  const handleTagsChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedOptions = Array.from(e.target.selectedOptions).map(option => option.value);
    setOptionList({ ...optionListData, tag: selectedOptions });
  };

  const handleDelete = () => {
    onOpen();
  };

  const handleEdit = () => {
    setOptionList({ ...optionListData, ...data });
    console.log(optionListData);
  };

  useEffect(() => {
    if (page === 'edit' && data) {
      setOptionList(data);
    }
  }, [data, page, setOptionList]);

  return (
    <form>
      <Flex gap={4} direction={'column'}>
        {page === 'edit' ? (
          <Flex justifyContent={'flex-end'} mt={{ base: '2rem' }}>
            <Button
              variant={'rounded'}
              bg={'borderGray'}
              fontSize={'sm'}
              fontWeight={'normal'}
              px={'1.2rem'}
              m={0}
              w={'auto'}
              display={'flex'}
              gap={4}
              onClick={() => {
                handleDelete();
              }}
            >
              <TrashIcon color="black" /> {t('optionslist.edit.form.deleteOptionButton')}
            </Button>
          </Flex>
        ) : null}

        <ImageInput
          page={page}
          variant={'bottom-label'}
          label={t('optionslist.edit.form.coverImage')}
        />

        <Flex flexWrap={'wrap'} justifyContent={'space-between'}>
          <CatTag
            onChange={handleChange}
            onTagsChange={handleTagsChange}
            select1Name="category"
            select2Name="tag"
            categoryOptions={categories.map(category => category.name)}
            tagOptions={tags.map(tag => tag.name)}
          />
          <Flex justifyContent={'center'}>
            <Button
              variant={'roundedBlue'}
              fontSize={'sm'}
              fontWeight={'normal'}
              px={'1.2rem'}
              py={'.7rem'}
              m={0}
              mt={{ base: '1rem', md: 0 }}
              w={'auto'}
              display={'flex'}
              gap={4}
              onClick={onCatAndTagOpen}
            >
              <PenIcon color="white" height={15} width={15} />
              {t('optionslist.edit.form.categoryTagManagement')}
            </Button>
          </Flex>
        </Flex>

        <InputField
          name="menuTitle"
          required
          label={t('optionslist.edit.form.menuTitle')}
          placeholder={t('optionslist.edit.form.menuTitlePlaceholder')}
          onChange={handleChange}
          value={optionListData.menuTitle}
        />

        <Flex flexDirection={'row'} gap={4}>
          <InputField
            required
            label={t('optionslist.edit.form.price')}
            onChange={handleChange}
            value={optionListData.price}
            name="price"
            placeholder={t('optionslist.edit.form.pricePlaceholder')}
          />
          <DropDown
            label={t('optionslist.edit.form.numberOfPeople')}
            placeholder={t('optionslist.edit.form.numberOfPeoplePlaceholder')}
            options={[
              t('optionslist.edit.options.noRelation'),
              t('optionslist.edit.options.onePerson'),
              t('optionslist.edit.options.twoOrMore'),
            ]}
            width={'12rem'}
            required
          />
        </Flex>

        <InputField
          label={t('optionslist.edit.form.menuDetails')}
          placeholder={t('optionslist.edit.form.menuDetailsPlaceholder')}
          variant={'textArea'}
          required
          name="menuDetails"
          onChange={handleChange}
          value={optionListData.menuDetails}
        />

        <Text variant={'subHeaderLarge'}>{t('optionslist.edit.form.detailedOptions')}</Text>
        <Text variant={'subHeaderNormal'}>{t('optionslist.edit.form.reservationTimeSlot')}</Text>
        <Text fontSize={'sm'}>{t('optionslist.edit.form.reservationTimeDescription')}</Text>

        <Flex
          flexDirection={'row'}
          p={'2px'}
          borderRadius={'10px'}
          border={'1px solid #e2e8f0'}
          gap={1}
        >
          <Button
            variant={'SquaredHighlight'}
            bg={fixed ? 'gray.100' : 'white'}
            px={'3rem'}
            gap={2}
            onClick={() => setFixed(true)}
          >
            {/* {fixed ? <CheckIcon height={25} width={25} color={'blue'} /> : null} */}
            <CheckIcon
              visibility={fixed ? 'visible' : 'hidden'}
              height={25}
              width={25}
              color={'blue'}
            />
            {t('optionslist.edit.form.fixed')}
          </Button>
          <Button
            variant={'SquaredHighlight'}
            bg={fixed ? 'white' : 'gray.100'}
            px={'3rem'}
            onClick={() => setFixed(false)}
          >
            <CheckIcon
              visibility={fixed ? 'hidden' : 'visible'}
              height={25}
              width={25}
              color={'blue'}
            />
            {t('optionslist.edit.form.variable')}
          </Button>
        </Flex>

        {fixed && (
          <Flex
            flexDirection={'column'}
            gap={'1rem'}
            pb={'1rem'}
            borderBottom={'1px solid #e2e8f0'}
          >
            <DropDown
              placeholder={t('optionslist.edit.options.twoHours')}
              options={[
                t('optionslist.edit.options.noRelation'),
                t('optionslist.edit.options.onePerson'),
                t('optionslist.edit.options.threePeople'),
                t('optionslist.edit.options.fourOrMore'),
                t('optionslist.edit.options.fivePeople'),
                t('optionslist.edit.options.sixOrMore'),
              ]}
              width={'7rem'}
            />

            <DropDownFormCard
              data={[
                {
                  label: t('optionslist.edit.form.reservationInterval'),
                  subtext: [
                    t('optionslist.edit.form.reservationIntervalDescription1'),
                    t('optionslist.edit.form.reservationIntervalDescription2'),
                  ],
                  dropDownLabel: t('optionslist.edit.form.afterReservation'),
                  hourOptions: [
                    t('optionslist.edit.options.twoHours'),
                    t('optionslist.edit.options.noRelation'),
                    t('optionslist.edit.options.onePerson'),
                    t('optionslist.edit.options.twoOrMore'),
                    t('optionslist.edit.options.threePeople'),
                    t('optionslist.edit.options.fourOrMore'),
                    t('optionslist.edit.options.fivePeople'),
                    t('optionslist.edit.options.sixOrMore'),
                  ],
                  options: [
                    t('optionslist.edit.options.minutes00'),
                    t('optionslist.edit.options.minutes15'),
                    t('optionslist.edit.options.minutes30'),
                    t('optionslist.edit.options.minutes45'),
                  ],
                },
              ]}
            />
          </Flex>
        )}

        <Flex flexDirection={'column'} p={'1rem'}>
          <RadioSelectWithTwoOptions
            head={t('optionslist.edit.form.reservationAvailability')}
            subHead={t('optionslist.edit.form.reservationAvailabilityDescription')}
            option1={t('optionslist.edit.form.businessHours')}
            option2={t('optionslist.edit.form.customSetting')}
          />

          <Flex flexDirection={'column'} py={'1rem'}>
            <Text variant={'subHeaderNormal'}>{t('optionslist.edit.form.maxReservations')}</Text>

            <DropDown
              placeholder={t('optionslist.edit.options.twoHours')}
              options={[
                t('optionslist.edit.options.twoHours'),
                t('optionslist.edit.options.noRelation'),
                t('optionslist.edit.options.onePerson'),
                t('optionslist.edit.options.twoOrMore'),
                t('optionslist.edit.options.threePeople'),
                t('optionslist.edit.options.fourOrMore'),
                t('optionslist.edit.options.fivePeople'),
                t('optionslist.edit.options.sixOrMore'),
              ]}
              width={'7rem'}
            />
          </Flex>

          <Flex flexDirection={'column'} borderTop={'1px solid #e2e8f0'} pt={'1rem'}>
            <Text variant={'subHeaderNormal'}>{t('optionslist.edit.form.reservationPeriod')}</Text>
            <Text fontSize={'sm'} lineHeight={'1.2'}>
              {t('optionslist.edit.form.reservationPeriodDescription')}
            </Text>
          </Flex>
        </Flex>

        <DropDownFormCard
          data={[
            {
              label: t('optionslist.edit.form.reservationStartSetting'),
              dropDownLabel: t('optionslist.edit.form.afterReservation'),
              hourOptions: [
                t('optionslist.edit.options.twoHours'),
                t('optionslist.edit.options.noRelation'),
                t('optionslist.edit.options.onePerson'),
                t('optionslist.edit.options.twoOrMore'),
                t('optionslist.edit.options.threePeople'),
                t('optionslist.edit.options.fourOrMore'),
                t('optionslist.edit.options.fivePeople'),
                t('optionslist.edit.options.sixOrMore'),
              ],
              options: [
                t('optionslist.edit.options.minutes00'),
                t('optionslist.edit.options.minutes15'),
                t('optionslist.edit.options.minutes30'),
                t('optionslist.edit.options.minutes45'),
              ],
            },
            {
              label: t('optionslist.edit.form.reservationStartSetting'),
              dropDownLabel: t('optionslist.edit.form.afterReservation'),
              hourOptions: [
                t('optionslist.edit.options.twoHours'),
                t('optionslist.edit.options.noRelation'),
                t('optionslist.edit.options.onePerson'),
                t('optionslist.edit.options.twoOrMore'),
                t('optionslist.edit.options.threePeople'),
                t('optionslist.edit.options.fourOrMore'),
                t('optionslist.edit.options.fivePeople'),
                t('optionslist.edit.options.sixOrMore'),
              ],
              options: [
                t('optionslist.edit.options.minutes00'),
                t('optionslist.edit.options.minutes15'),
                t('optionslist.edit.options.minutes30'),
                t('optionslist.edit.options.minutes45'),
              ],
            },
          ]}
        />

        <Flex justifyContent={'center'}>
          <Button
            variant={'roundedBlue'}
            px={'3rem'}
            py={'.7rem'}
            w={'auto'}
            onClick={() => {
              // setOptionList(optionListData);
              if (page === 'add') {
                addOption(optionListData);
                console.log(optionListData);
                Router.push('/admin/options-list');
                setOptionList(optionListObj);
              }
              if (page === 'edit') {
                updateOption(optionListData);
                console.log(optionListData);
                console.log(data?.menuTitle);
                Router.push('/admin/options-list');
                setOptionList(optionListObj);
              }
            }}
          >
            {t('optionslist.edit.form.submitButton')}
          </Button>
        </Flex>
      </Flex>
    </form>
  );
}

export default OptionList;
