import React from 'react';
import { Flex, Text } from '@chakra-ui/react';
import Image from 'next/image';

function ProfileIcon() {
  return (
    <Flex justifyContent={'center'} w={'100%'} alignItems={'center'} gap={2} mb={'2rem'}>
      <Flex
        flexDirection={'column'}
        justifyContent={'center'}
        alignItems={'center'}
        w={'100%'}
        h={'12rem'}
        borderRadius={'10px'}
        boxShadow={'0px 0px 10px 0px rgba(0, 0, 0, 0.10)'}
      >
        <Flex overflow={'hidden'} borderRadius={'100%'} height={'6rem'} w={'6rem'} mb={'1rem'}>
          <Image
            style={{ objectFit: 'cover', width: '100%' }}
            src={'/imgs/icons/profileIcon.svg'}
            alt="option"
            width={180}
            height={180}
          />
        </Flex>
        <Text variant={'subHeaderXL'}><PERSON><PERSON><PERSON> taro</Text>
      </Flex>
    </Flex>
  );
}

export default ProfileIcon;
