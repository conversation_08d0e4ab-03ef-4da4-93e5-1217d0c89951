import CardText from '@/components/ui/cards/card-text';
import { useTempReservationStore } from '@/store/temp/tempReservationStore';
import { Flex, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

function ReservationHistory() {
  const { t } = useTranslation('common');
  const data = useTempReservationStore(state => state.reservations);

  return (
    <Flex flexDirection={'column'} gap={'2rem'}>
      <Text variant={'header'}>{t('reservations.reservationHistory')}</Text>

      {data.map(item => (
        <CardText key={item.id} variant={'reservation-history'} data={item} />
      ))}
    </Flex>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'profile', 'auth'])),
      layoutType: 'client',
    },
  };
}

export default ReservationHistory;
