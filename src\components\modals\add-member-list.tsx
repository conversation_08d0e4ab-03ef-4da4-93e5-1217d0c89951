import {
  Button,
  Flex,
  FormControl,
  FormLabel,
  Modal,
  ModalBody,
  ModalCloseButton,
  Modal<PERSON>ontent,
  <PERSON>dal<PERSON><PERSON>er,
  <PERSON>dal<PERSON>eader,
  ModalOverlay,
  Select,
  Text,
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import Image from 'next/image';
import React from 'react';
import InputField from '../ui/formfields/InputField';

interface AddMemberListProps {
  isOpen: boolean;
  onClose: () => void;
  newMember: {
    status: string;
    group: string;
    remainingTime: number;
    totalTime: number;
    chargeEnabled: boolean;
    contact: string;
  };
  handleInputChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => void;
  handleAddMember: () => void;
  nextStep: () => void;
  steps: number;
  setSteps: React.Dispatch<React.SetStateAction<number>>;
}

function AddMemberList({
  isOpen,
  onClose,
  newMember,
  handleInputChange,
  handleAddMember,
  nextStep,
  steps,
  setSteps,
}: AddMemberListProps) {
  const { t } = useTranslation('admin');
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      {steps === 0 && (
        <ModalContent>
          <ModalHeader fontSize={'sm'} textAlign={'center'}>
            {t('membersList.addMember.title')}
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody display={'flex'} flexDirection={'column'} gap={'.5rem'} pb={6}>
            <InputField
              label={t('membersList.addMember.form.email')}
              name={'status'}
              value={''}
              placeholder={t('membersList.addMember.form.emailPlaceholder')}
              onChange={handleInputChange}
              required
            />

            <InputField
              label={'グループ名'}
              name={'name'}
              value={''}
              placeholder={'justyoyaku team'}
              onChange={handleInputChange}
              required
            />

            <FormControl>
              <FormLabel fontSize={'sm'}>予約可能時間</FormLabel>
              <Select
                fontSize={'sm'}
                name="status"
                value={newMember.status}
                onChange={handleInputChange}
                h={'3.1rem'}
                borderRadius={'lg'}
              >
                <option value="">{t('membersList.addMember.form.groupSelectPlaceholder')}</option>
                <option value="alpha">Alpha</option>
                <option value="group">Group</option>
                <option value="proto">Proto</option>
              </Select>
            </FormControl>
            <InputField
              label={'招待可能人数'}
              name={'name'}
              value={''}
              placeholder={'3'}
              onChange={handleInputChange}
              required
              type="number"
            />

            <InputField
              label={'事前予約可能な件数'}
              name={'name'}
              value={''}
              placeholder={'件数'}
              onChange={handleInputChange}
              required
              subHelperText="日以降の1日に事前予約できる最大件数です"
            />
            <InputField
              label={'事前予約可能日数'}
              name={'name'}
              value={''}
              placeholder={'日数'}
              onChange={handleInputChange}
              required
              subHelperText="ご予約の際に事前に予約できる最大日数です"
            />

            <InputField
              label={'備考'}
              name={'name'}
              value={''}
              placeholder={'Placeholder'}
              onChange={handleInputChange}
              required
              variant={'textArea'}
            />
          </ModalBody>

          <ModalFooter justifyContent={'center'}>
            <Button
              px={'4rem'}
              //   p={0}
              w={'auto'}
              variant="roundedBlue"
              fontWeight={'light'}
              onClick={nextStep}
            >
              {t('membersList.addMember.buttons.add')}
            </Button>
          </ModalFooter>
        </ModalContent>
      )}

      {steps === 1 && (
        <ModalContent>
          <Flex justifyContent={'center'} alignItems={'center'} h={'5rem'}>
            <Image src="/imgs/icons/mail.svg" alt="Vercel Logo" width={25} height={25} />
          </Flex>
          <ModalCloseButton />
          <ModalBody fontSize={'sm'} textAlign={'center'}>
            <Text mb={4} fontSize={'md'} fontWeight={'bold'}>
              {t('membersList.addMember.confirmation.title')}
            </Text>
            <Text>{t('membersList.addMember.confirmation.message1')}</Text>
            <Text>{t('membersList.addMember.confirmation.message2')}</Text>
          </ModalBody>

          <ModalFooter gap={4} justifyContent={'center'}>
            <Button
              px={'3rem'}
              //   p={0}
              w={'auto'}
              variant="rounded"
              fontWeight={'light'}
              bg={'gray.100'}
              onClick={() => {
                setSteps(steps - 1);
              }}
            >
              {t('membersList.addMember.buttons.back')}
            </Button>
            <Button
              px={'3rem'}
              //   p={0}
              w={'auto'}
              variant="roundedBlue"
              fontWeight={'light'}
              onClick={() => {
                handleAddMember();
                nextStep();
              }}
            >
              {t('membersList.addMember.buttons.send')}
            </Button>
          </ModalFooter>
        </ModalContent>
      )}

      {steps === 2 && (
        <ModalContent>
          <Flex justifyContent={'center'} alignItems={'center'} h={'5rem'}>
            <Image src="/imgs/icons/mail.svg" alt="Vercel Logo" width={25} height={25} />
          </Flex>
          <ModalCloseButton />
          <ModalBody fontSize={'sm'} textAlign={'center'}>
            <Text mb={4} fontSize={'md'} fontWeight={'bold'}>
              {t('membersList.addMember.success.message')}
            </Text>
          </ModalBody>

          <ModalFooter gap={4} justifyContent={'center'}>
            <Button
              px={'3rem'}
              //   p={0}
              w={'auto'}
              variant="roundedBlue"
              fontWeight={'light'}
              onClick={() => {
                setSteps(0);
                onClose();
              }}
            >
              {t('membersList.addMember.buttons.close')}
            </Button>
          </ModalFooter>
        </ModalContent>
      )}
    </Modal>
  );
}

export default AddMemberList;
