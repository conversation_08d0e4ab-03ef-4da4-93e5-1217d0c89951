import {
  Button,
  Flex,
  FormControl,
  FormLabel,
  Modal,
  Modal<PERSON>ody,
  Modal<PERSON>lose<PERSON>utton,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalOverlay,
  Select,
  Text,
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import Image from 'next/image';
import React from 'react';
import InputField from '../ui/formfields/InputField';

interface AddMemberListProps {
  isOpen: boolean;
  onClose: () => void;
  newMember: {
    status: string;
    group: string;
    remainingTime: number;
    totalTime: number;
    chargeEnabled: boolean;
    contact: string;
    email: string;
    availableTime: string;
    capacity: number;
    maxAdvanceReservations: number;
    advanceReservationDays: number;
    remarks: string;
  };

  handleInputChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => void;
  handleAddMember: () => void;
  nextStep: () => void;
  steps: number;
  setSteps: React.Dispatch<React.SetStateAction<number>>;
}

function AddMemberList({
  isOpen,
  onClose,
  newMember,
  handleInputChange,
  handleAddMember,
  nextStep,
  steps,
  setSteps,
}: AddMemberListProps) {
  const { t } = useTranslation('admin');
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      {steps === 0 && (
        <ModalContent>
          <ModalHeader fontSize={'sm'} textAlign={'center'}>
            {t('membersList.addMember.title')}
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody display={'flex'} flexDirection={'column'} gap={'.5rem'} pb={6}>
            <InputField
              label={t('membersList.addMember.form.email')}
              name={'contact'}
              value={newMember.contact}
              placeholder={t('membersList.addMember.form.emailPlaceholder')}
              onChange={handleInputChange}
              required
            />

            <InputField
              label={t('membersList.addMember.form.groupName')}
              name={'group'}
              value={newMember.group}
              placeholder={t('membersList.addMember.form.groupNamePlaceholder')}
              onChange={handleInputChange}
              required
            />

            <FormControl>
              <FormLabel fontSize={'sm'}>
                {t('membersList.addMember.form.reservationAvailableTime')}
              </FormLabel>
              <Select
                fontSize={'sm'}
                name={'availableTime'}
                value={newMember.availableTime}
                onChange={handleInputChange}
                h={'3.1rem'}
                placeholder="{Time Select}"
                borderRadius={'lg'}
              >
                {/* <option value="">
                  {t('membersList.addMember.form.reservationAvailableTimePlaceholder')}
                </option> */}
                <option value="alpha">1</option>
                <option value="group">2</option>
                <option value="proto">3</option>
              </Select>
            </FormControl>
            <InputField
              label={t('membersList.addMember.form.possibleNumberOfPeople')}
              name={'capacity'}
              value={newMember.capacity}
              placeholder={'3'}
              onChange={handleInputChange}
              required
              type="number"
            />

            <InputField
              label={t('membersList.addMember.form.NumberOfAdvanceReservationsPossible')}
              name={'maxAdvanceReservations'}
              value={newMember.maxAdvanceReservations}
              placeholder={'件数'}
              onChange={handleInputChange}
              type="number"
              required
              subHelperText={t(
                'membersList.addMember.form.NumberOfAdvanceReservationsPossibleHelper'
              )}
            />
            <InputField
              label={t('membersList.addMember.form.NumberOfAdvanceReservationsDays')}
              name={'advanceReservationDays'}
              value={newMember.advanceReservationDays}
              placeholder={'日数'}
              onChange={handleInputChange}
              type="number"
              required
              subHelperText={t('membersList.addMember.form.NumberOfAdvanceReservationsDaysHelper')}
            />

            <InputField
              label={t('membersList.addMember.form.notes')}
              name={'remarks'}
              value={newMember.remarks}
              placeholder={'Placeholder'}
              onChange={handleInputChange}
              required
              variant={'textArea'}
            />
          </ModalBody>

          <ModalFooter justifyContent={'center'}>
            <Button
              px={'4rem'}
              //   p={0}
              w={'auto'}
              variant="roundedBlue"
              fontWeight={'light'}
              onClick={nextStep}
            >
              {t('membersList.addMember.buttons.add')}
            </Button>
          </ModalFooter>
        </ModalContent>
      )}

      {steps === 1 && (
        <ModalContent>
          <Flex justifyContent={'center'} alignItems={'center'} h={'5rem'}>
            <Image src="/imgs/icons/mail.svg" alt="Vercel Logo" width={25} height={25} />
          </Flex>
          <ModalCloseButton />
          <ModalBody fontSize={'sm'} textAlign={'center'}>
            <Text mb={4} fontSize={'md'} fontWeight={'bold'}>
              {t('membersList.addMember.confirmation.title')}
            </Text>
            <Text>{t('membersList.addMember.confirmation.message1')}</Text>
            <Text>{t('membersList.addMember.confirmation.message2')}</Text>
          </ModalBody>

          <ModalFooter gap={4} justifyContent={'center'}>
            <Button
              px={'3rem'}
              //   p={0}
              w={'auto'}
              variant="rounded"
              fontWeight={'light'}
              bg={'gray.100'}
              onClick={() => {
                setSteps(steps - 1);
              }}
            >
              {t('membersList.addMember.buttons.back')}
            </Button>
            <Button
              px={'3rem'}
              //   p={0}
              w={'auto'}
              variant="roundedBlue"
              fontWeight={'light'}
              onClick={() => {
                handleAddMember();
                nextStep();
              }}
            >
              {t('membersList.addMember.buttons.send')}
            </Button>
          </ModalFooter>
        </ModalContent>
      )}

      {steps === 2 && (
        <ModalContent>
          <Flex justifyContent={'center'} alignItems={'center'} h={'5rem'}>
            <Image src="/imgs/icons/mail.svg" alt="Vercel Logo" width={25} height={25} />
          </Flex>
          <ModalCloseButton />
          <ModalBody fontSize={'sm'} textAlign={'center'}>
            <Text mb={4} fontSize={'md'} fontWeight={'bold'}>
              {t('membersList.addMember.success.message')}
            </Text>
          </ModalBody>

          <ModalFooter gap={4} justifyContent={'center'}>
            <Button
              px={'3rem'}
              //   p={0}
              w={'auto'}
              variant="roundedBlue"
              fontWeight={'light'}
              onClick={() => {
                setSteps(0);
                onClose();
              }}
            >
              {t('membersList.addMember.buttons.close')}
            </Button>
          </ModalFooter>
        </ModalContent>
      )}
    </Modal>
  );
}

export default AddMemberList;
