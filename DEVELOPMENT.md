# Just Yoyaku Development Documentation

## Table of Contents

1. [Project Overview](#project-overview)
2. [Technology Stack](#technology-stack)
3. [Project Structure](#project-structure)
4. [Component Library](#component-library)
5. [Styling System](#styling-system)
6. [Localization](#localization)
7. [Development Workflow](#development-workflow)
8. [Best Practices](#best-practices)

## Project Overview

Just Yoyaku is a reservation system application built with Next.js. It provides functionality for managing reservations, members, news, and store options. The application has two main sections:

- **Admin Section**: For store owners to manage reservations, members, news, and store settings
- **Client Section**: For customers to make reservations and view store information

## Technology Stack

- **Framework**: Next.js 13.4.7
- **Language**: TypeScript
- **UI Library**: Chakra UI v2.10.7
- **Localization**: next-i18next
- **State Management**: Zustand
- **Form Handling**: react-hook-form
- **API Communication**: REST API
- **Styling**: Emotion (via Chakra UI)
- **Code Quality**: ESLint, Prettier

## Project Structure

```
just-yoyaku/
├── public/                  # Static assets and localization files
│   ├── locales/             # Internationalization JSON files
│   │   ├── en/              # English translations
│   │   └── ja/              # Japanese translations
├── src/                     # Source code
│   ├── assets/              # SVG icons and other assets
│   │   └── icons/           # SVG icon components
│   ├── components/          # Reusable components
│   │   ├── forms/           # Form components
│   │   ├── layouts/         # Layout components
│   │   ├── modals/          # Modal components
│   │   ├── navigation/      # Navigation components
│   │   ├── tables/          # Table components
│   │   └── ui/              # UI components
│   ├── constants/           # Constants and configuration
│   ├── hooks/               # Custom React hooks
│   ├── pages/               # Next.js pages
│   │   ├── admin/           # Admin section pages
│   │   ├── api/             # API routes
│   │   └── client/          # Client section pages
│   ├── styles/              # Global styles
│   ├── theme/               # Chakra UI theme configuration
│   ├── types/               # TypeScript type definitions
│   └── utils/               # Utility functions
├── .eslintrc.json          # ESLint configuration
├── .prettierrc             # Prettier configuration
├── next.config.js          # Next.js configuration
├── next-i18next.config.js  # Internationalization configuration
└── tsconfig.json           # TypeScript configuration
```

## Component Library

### Layout Components

- **Layout**: Main layout component that wraps all pages
- **BlankLayout**: Simple layout without navigation
- **ClientLayout**: Layout for client-facing pages with header and footer

### UI Components

- **Cards**: Various card components for displaying information
- **Calendar**: Date selection component
- **Buttons**: Various button components
- **Forms**: Form input components

### Navigation Components

- **SideBar**: Admin section navigation sidebar
- **ClientHeader**: Header for client section
- **ClientFooter**: Footer for client section

## Styling System

The project uses Chakra UI for styling, which is built on top of Emotion. The theme configuration is located in the `src/theme` directory.

### Theme Structure

- **colors.ts**: Color palette definitions
- **buttons.ts**: Button variant styles
- **inputs.ts**: Input variant styles
- **links.ts**: Link variant styles
- **text.ts**: Text variant styles
- **index.tsx**: Main theme configuration

### Usage Example

```tsx
import { Button, Text, Flex } from '@chakra-ui/react';

function MyComponent() {
  return (
    <Flex direction="column" gap={4}>
      <Text variant="header">Header Text</Text>
      <Button variant="rounded">Rounded Button</Button>
    </Flex>
  );
}
```

## Localization

The project uses next-i18next for localization. Translation files are located in the `public/locales` directory.

### Configuration

The localization configuration is in `next-i18next.config.js`:

```js
module.exports = {
  i18n: {
    defaultLocale: 'ja',
    locales: ['en', 'ja'],
    localeDetection: true,
  },
  localePath: './public/locales',
  reloadOnPrerender: process.env.NODE_ENV === 'development',
  debug: process.env.NODE_ENV === 'development',
  ns: ['common', 'login'],
  defaultNS: 'common',
  react: {
    useSuspense: false,
  },
};
```

### Localization Usage Example

```tsx
import { useTranslation } from 'next-i18next';

function MyComponent() {
  const { t } = useTranslation('common');

  return <p>{t('greeting')}</p>;
}
```

### Adding New Translations

1. Create a new JSON file in the appropriate namespace directory
2. Add the translation key-value pairs
3. Update the `ns` array in `next-i18next.config.js` if adding a new namespace

## Development Workflow

### Setup

1. Clone the repository
2. Install dependencies with `yarn install`
3. Start the development server with `yarn dev`

### Scripts

- `yarn dev`: Start the development server
- `yarn build`: Build the production version
- `yarn start`: Start the production server
- `yarn lint`: Run ESLint
- `yarn lint:fix`: Run ESLint with auto-fix
- `yarn format`: Run Prettier to format code

### Code Quality

- ESLint is configured to enforce code quality rules
- Prettier is used for code formatting
- Husky and lint-staged are set up to run linting and formatting on commit

## Best Practices

### React Components

- Use functional components with hooks
- Keep components small and focused on a single responsibility
- Extract reusable logic into custom hooks
- Use TypeScript interfaces for component props

### State Management

- Use React state for local component state
- Use Zustand for global state management
- Create separate stores for different domains (e.g., user, reservations)
- Avoid prop drilling by using Zustand stores

### Styling

- Use Chakra UI's style props for component styling
- Define custom variants in the theme for consistent styling
- Use responsive values for responsive design

### Localization Best Practices

- Always use translation keys instead of hardcoded text
- Organize translations by feature (e.g., login.json, common.json)
- Use namespaces to separate translations by domain

### API Communication

- Use fetch or axios for REST API calls
- Create API service modules for different resources
- Handle errors consistently across API calls
- Use TypeScript interfaces for API request and response types

### Performance

- Use Next.js's built-in optimizations (Image, Link, etc.)
- Implement code splitting with dynamic imports
- Optimize images and assets
- Use memoization for expensive calculations
