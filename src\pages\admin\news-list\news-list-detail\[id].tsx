import BackIcon from '@/assets/icons/back';
import PenIcon from '@/assets/icons/pen';
import TrashIcon from '@/assets/icons/trash';
import DeleteConfirmationModal from '@/components/modals/delete-confirmation-modal';
import { StatusBadge } from '@/components/ui/statusBadge';
import { Button, Flex, Image, Text, useDisclosure } from '@chakra-ui/react';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';

import { NewsItemType } from '@/types/newsItemType';

export default function NewsListDetailPage() {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const router = useRouter();
  const [deleteStep, setDeleteStep] = useState(1);

  const [newsItem, setNewsItem] = useState<NewsItemType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { id } = router.query;

  useEffect(() => {
    const fetchNewsItem = async () => {
      if (!id) return;

      try {
        setIsLoading(true);
        const response = await fetch(`/api/news-list/${id}`);

        if (!response.ok) {
          throw new Error('Failed to fetch news item');
        }

        const data = await response.json();
        setNewsItem(data);
      } catch (error) {
        console.error('Error fetching news item:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchNewsItem();
  }, [id]);

  const handleDelete = async () => {
    try {
      const response = await fetch(`/api/news-list/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete news item');
      }

      setDeleteStep(2);
    } catch (error) {
      console.error('Error deleting news item:', error);
    }
  };

  const handleClose = () => {
    setDeleteStep(1);
    onClose();
  };

  return (
    <Flex
      w={'100%'}
      flexDirection={'column'}
      pr={{ base: 0, md: 6 }}
      pl={{ base: 0, md: 6, lg: 0 }}
    >
      <Flex
        flexWrap={{ base: 'wrap', sm: 'nowrap' }}
        bg={{ base: 'white', md: 'unset' }}
        p={{ base: '1rem', sm: '1rem', lg: 0 }}
        justifyContent={{ base: 'space-between' }}
        alignItems={'center'}
        height={{ base: 'unset', sm: '5rem' }}
        w={'100%'}
      >
        <Flex alignItems={'center'}>
          <Button
            variant={'ghost'}
            w={'auto'}
            p={0}
            m={0}
            onClick={() => {
              router.back();
            }}
          >
            <BackIcon width={30} height={30} color="black" className="my-icon-class" />
          </Button>
          <Text fontSize={'lg'} fontWeight={'bold'}>
            店舗情報
          </Text>
        </Flex>

        <Flex
          w={{ base: '100%', sm: 'unset' }}
          display={{ base: 'flex', md: 'none' }}
          justifyContent={{ base: 'space-between', md: 'flex-end' }}
          gap={4}
        >
          <Button
            variant={'roundedBlue'}
            color={'black'}
            bg={'borderGray'}
            fontSize={'xs'}
            fontWeight={'normal'}
            gap={2}
            w={'auto'}
            onClick={onOpen}
          >
            <TrashIcon width={20} height={20} color="black" className="my-icon-class" />
            削除
          </Button>
          <Button
            variant={'roundedBlue'}
            fontSize={'xs'}
            fontWeight={'normal'}
            gap={2}
            w={'auto'}
            onClick={() => {
              router.push(`/admin/news-list/news-list-detail-edit/${newsItem?.id}`);
            }}
          >
            <PenIcon width={15} height={15} color="white" className="my-icon-class" />
            編集
          </Button>
        </Flex>
      </Flex>

      <Flex flexDirection={'column'} bg={'white'} gap={4} p={5} borderRadius={'1rem'}>
        <Flex
          flexDirection={{ base: 'column' }}
          justifyContent={'space-between'}
          alignItems={'center'}
        >
          <Flex
            w={'100%'}
            display={{ base: 'none', md: 'flex' }}
            justifyContent={'flex-end'}
            gap={4}
          >
            <Button
              variant={'roundedBlue'}
              color={'black'}
              bg={'borderGray'}
              fontSize={'sm'}
              fontWeight={'normal'}
              gap={2}
              w={'auto'}
              p={'1.3rem'}
              onClick={onOpen}
            >
              <TrashIcon width={20} height={20} color="black" className="my-icon-class" />
              削除
            </Button>
            <Button
              variant={'roundedBlue'}
              fontSize={'sm'}
              fontWeight={'normal'}
              gap={2}
              w={'auto'}
              p={'1.3rem'}
              onClick={() => {
                router.push(`/admin/news-list/news-list-detail-edit/${newsItem?.id}`);
              }}
            >
              <PenIcon width={15} height={15} color="white" className="my-icon-class" />
              編集
            </Button>
          </Flex>
          <Flex w={'100%'} pl={'1rem'} justifyContent={'flex-start'} alignItems={'center'}>
            <StatusBadge status={typeof newsItem?.category === 'string' ? newsItem.category : ''} />
          </Flex>
        </Flex>

        <Flex px={'1rem'} flexDirection={'column'}>
          <Flex flexDirection={'column'} gap={2} pb={'1rem'} mb={'1rem'}>
            <Text variant={'subHeaderXL'}>{newsItem?.title}</Text>
            <Text fontSize={'sm'} color={'black600'}>
              {newsItem?.date}
            </Text>

            <Flex justifyContent={'center'}>
              <Flex
                bg={'red.100'}
                overflow={'hidden'}
                borderRadius={'10px'}
                height={'15rem'}
                w={'20rem'}
              >
                <Image
                  style={{ objectFit: 'cover', width: '100%', objectPosition: 'center' }}
                  src={newsItem?.image}
                  alt="option"
                />
              </Flex>
            </Flex>
          </Flex>
          <Flex flexDirection={'column'} gap={4} pb={'1rem'} mb={'1rem'}>
            <Text variant={'subHeaderXL'}>{newsItem?.title}</Text>
            <Text fontSize={'sm'} color={'black600'}>
              {newsItem?.body?.map((item, index) => (
                <React.Fragment key={index}>
                  {item}
                  <br />
                </React.Fragment>
              ))}
            </Text>
          </Flex>

          <Flex justifyContent={'center'} gap={4} pb={'1rem'} mb={'1rem'}>
            <Button
              variant={'roundedBlue'}
              fontSize={'sm'}
              fontWeight={'normal'}
              gap={2}
              py={'1rem'}
              px={'2rem'}
              w={'auto'}
              onClick={() => {
                router.push('/admin/news-list');
              }}
            >
              リストへ
            </Button>
          </Flex>
        </Flex>
        <DeleteConfirmationModal
          isOpen={isOpen}
          deleteStep={deleteStep}
          handleDelete={handleDelete}
          handleClose={handleClose}
          redirect={'/admin/news-list'}
        />
      </Flex>
    </Flex>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'admin'])),
      layoutType: 'withSidebar',
    },
  };
}

export async function getStaticPaths() {
  return {
    paths: [],
    fallback: 'blocking',
  };
}
