import { Button, FlexProps } from '@chakra-ui/react';
import { ReactNode } from 'react';

interface CardContainerProps extends Omit<FlexProps, 'children'> {
  children: ReactNode;
  variant?: string;
}

export default function CardButton({
  children,
  variant = 'default',
  gap = '1.5rem',
  borderRadius = '1rem',
  ...rest
}: CardContainerProps) {
  return (
    <Button variant={'card'} gap={gap} borderRadius={borderRadius} {...(rest as any)}>
      {children}
    </Button>
  );
}
