import { Button, Flex, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';

interface ReservationData {
  shopName: string;
  shopBranch: string;
  address: string;
  courseName: string;
  price: string;
  memberOnly: boolean;
}

function CardSelectedReservation({ reservationData }: { reservationData?: ReservationData }) {
  const { t } = useTranslation('common');

  //TODO: Change this to default data
  const defaultData: ReservationData = {
    shopName: 'やきにくや YAKINIKU-YA',
    shopBranch: '麻布十番店',
    address: t('store.addressValue'),
    courseName: t('courses.beefFeastCourse'),
    price: '12,000',
    memberOnly: true,
  };

  const data = reservationData || defaultData;

  return (
    <Flex
      bg={'white'}
      flexDirection={'column'}
      p={{ base: '1rem', md: '2rem' }}
      gap={'1rem'}
      borderRadius={'1rem'}
    >
      <Text fontSize={'sm'} fontWeight={'bold'}>
        {data.shopName} {data.shopBranch}
      </Text>

      <Text fontSize={'xs'}>{data.address}</Text>

      <Flex
        border={'1px solid'}
        borderColor={'borderGray'}
        p={'1rem'}
        borderRadius={'1rem'}
        gap={'.5rem'}
      >
        <Flex w={'100%'} flexDirection={'column'} gap={'.5rem'}>
          <Text fontSize={'md'}>{data.courseName}</Text>
          <Text fontSize={'sm'} fontWeight={'bold'}>
            {t('formats.coursePrice', { price: data.price })}
            {t('formats.perPerson')}
          </Text>
          <Flex>
            {data.memberOnly && <Text variant={'category'}>{t('store.membersOnly')}</Text>}
          </Flex>
        </Flex>
        <Flex justifyContent={'flex-end'}>
          <Button
            border={'none'}
            bg={'borderGray'}
            px={'1.5rem'}
            variant={'rounded'}
            fontSize={'sm'}
            fontWeight={'normal'}
            gap={2}
            w={'auto'}
          >
            {t('buttons.change')}
          </Button>
        </Flex>
      </Flex>
    </Flex>
  );
}

export default CardSelectedReservation;
