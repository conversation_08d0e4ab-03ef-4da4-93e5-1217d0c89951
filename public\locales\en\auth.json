{"login": {"title": "<PERSON><PERSON>", "loginWithEmail": "Login with <PERSON><PERSON>", "loginWithGoogle": "Register with Google", "loginWithApple": "Register with Apple", "loginWithLine": "Register with LINE"}, "account": {"createAccount": "Create Account", "dontHaveAccount": "Don't have an account?", "forgotPassword": "Forgot Password?"}, "form": {"emailAddress": "Email Address", "password": "Password", "hidePassword": "Hide password", "showPassword": "Show password"}, "errors": {"invalidCredentials": "Email address or password is incorrect"}, "createAccount": {"stepProgress": "Step {{current}}/{{total}}", "requiredFieldsNotice": "Please fill in all required fields.", "steps": {"userInfo": {"title": "User Settings", "description": "Set up user-related information."}, "storeInfo": {"title": "Store Information", "description": "Set up store-related information."}, "externalCalendar": {"title": "External Calendar Connection", "description": "Connect with external calendar services to sync your schedule."}, "businessHours": {"title": "Business Hours", "description": "Set the time range for reservations.", "customizableNote": "This setting can be changed and customized later."}}, "form": {"profileIcon": "Profile Icon", "storeIcon": "Store Icon", "changePhoto": "Change Photo", "username": "Username", "usernamePlaceholder": "<PERSON>", "groupName": "Group Name", "groupNamePlaceholder": "Proto", "storeName": "Store Name", "storeNamePlaceholder": "My Restaurant", "storeId": "Store ID", "storeIdPlaceholder": "example-shop", "storeIdNote": "The store ID will become the URL for your reservation page", "storeOverview": "Store Overview", "storeOverviewPlaceholder": "Restaurant", "googleCalendar": "Google Calendar", "connect": "Connect"}, "validation": {"usernameRequired": "Please enter a username", "passwordRequired": "Please enter a password", "passwordMismatch": "Passwords do not match", "firstNameRequired": "Please enter your first name", "lastNameRequired": "Please enter your last name", "phoneNumberRequired": "Please enter your phone number"}, "buttons": {"next": "Next", "back": "Go Back", "complete": "Complete"}}, "resetPassword": {"title": "Send Reset Email", "description": "We will send you a password reset URL. Please enter your registered email address and click the 'Send' button.", "form": {"emailAddress": "Email Address"}, "buttons": {"send": "Send"}}, "resetPasswordConfirm": {"title": "Reset Password", "description": "Please enter your new password", "form": {"password": "Password", "passwordPlaceholder": "password"}, "buttons": {"login": "<PERSON><PERSON>"}}, "searchPasswordEmail": {"title": "Send Reset Email", "description": "We will send you a password reset URL. Please enter your registered email address and click the 'Send' button.", "form": {"emailAddress": "Email Address", "emailPlaceholder": "Enter your email address"}, "buttons": {"send": "Send"}}, "membershipRegistration": {"title": "Membership Registration Form", "termsAgreement": "Please agree to the terms of service.", "form": {"reserverInfo": "Reserver Information", "reserverName": "Reserver Name", "reserverNamePlaceholder": "<PERSON>", "reserverNameKana": "Reserver Name (Kana)", "reserverNameKanaPlaceholder": "<PERSON>", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "08012345678", "emailAddress": "Email Address", "emailPlaceholder": "<EMAIL>", "initialPassword": "Initial Password Setup", "passwordPlaceholder": "abcde"}, "links": {"forgotPassword": "Forgot your password?"}, "buttons": {"register": "Register"}}}