import { Flex } from '@chakra-ui/react';
import '@fontsource/inter';
import { ReactNode } from 'react';

type BlankLayoutProps = {
  children: ReactNode;
};

export default function BlankLayout({ children }: BlankLayoutProps) {
  return (
    <Flex
      bg={'mainBackGroundColor'}
      flexDirection={'column'}
      alignItems={'center'}
      justifyContent={'center'}
      h={'100vh'}
      fontFamily="Inter, sans-serif"
    >
      {children}
    </Flex>
  );
}
