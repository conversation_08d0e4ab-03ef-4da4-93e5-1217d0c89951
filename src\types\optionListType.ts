export type OptionListCategoryType = {
  id: number;
  name: string;
  description?: string;
};

export type OptionListType = {
  id: number;
  image?: string;
  category?: OptionListCategoryType['name'];
  tag?: string[];
  menuTitle?: string;
  price?: number | string;
  numberOfPeople?: number | string;
  menuDetails?: string;
  reservationTimeSlot?: string;
  duration?: string | number;
  kana?: string;
  bookingInterval?: string | number;
  reservationDateAndTime?: string;
  maximumNumberOfReservations?: number;
  reservationAcceptancePeriod?: {
    StartTimeSetting?: {
      hour?: string | number;
      minute?: string | number;
    };
    CancellationTimeSetting?: {
      hour?: string | number;
      minute?: string | number;
    };
  };
};
