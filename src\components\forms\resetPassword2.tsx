import {
  Button,
  <PERSON>lex,
  <PERSON>ack,
  Text,
  FormControl,
  FormLabel,
  Input,
  InputGroup,
  InputRightElement,
  IconButton,
  Image,
  FormErrorMessage,
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import React, { useState } from 'react';

function ResetPassword2() {
  const { t } = useTranslation('auth');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <Flex
      borderRadius={'20px'}
      w={{ base: '90dvw', sm: '30rem' }}
      justify={'center'}
      gap="4"
      direction="column"
      py="6"
      bg="white"
    >
      <Flex className="text-center" justify={'center'} direction="column" gap={4}>
        <Text variant={'brand'}>JUSTYOYAKU</Text>
        <Text fontWeight="bold" fontSize={'2xl'}>
          {t('resetPasswordConfirm.title')}
        </Text>
      </Flex>

      <Flex className="text-center" justify={'center'} direction="column" gap={4}>
        <Text textStyle="1xl">{t('resetPasswordConfirm.description')}</Text>
        <hr />
        <form action="/login" method="post">
          <FormControl px={'1.5rem'}>
            <FormLabel>{t('form.password')}</FormLabel>
            <InputGroup>
              <Input
                p={'6'}
                placeholder={t('form.password')}
                size="sm"
                type={showPassword ? 'text' : 'password'}
                id="password"
                name="password"
                value={password}
                onChange={e => setPassword(e.target.value)}
              />
              <InputRightElement h="full" w={'4rem'}>
                <IconButton
                  aria-label={showPassword ? t('form.hidePassword') : t('form.showPassword')}
                  icon={
                    <Image
                      src={
                        showPassword
                          ? '/imgs/icons/hide-password.svg'
                          : '/imgs/icons/show-password.svg'
                      }
                      alt={showPassword ? t('form.hidePassword') : t('form.showPassword')}
                      width={25}
                      height={25}
                    />
                  }
                  variant="ghost"
                  size="sm"
                  onClick={togglePasswordVisibility}
                />
              </InputRightElement>
            </InputGroup>
            <FormErrorMessage> err</FormErrorMessage>
          </FormControl>
          <Flex py={'1.4rem'} px={'1.5rem'}>
            <Button variant={'roundedBlue'} type="submit">
              {t('resetPasswordConfirm.buttons.login')}
            </Button>
          </Flex>
        </form>
      </Flex>
    </Flex>
  );
}

export default ResetPassword2;
