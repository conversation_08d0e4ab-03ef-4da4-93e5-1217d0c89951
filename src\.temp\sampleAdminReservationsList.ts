export const sampleAdminReservationsList = [
  {
    id: 1,
    status: 'confirmed',
    dateTime: '2023-10-15 18:00',
    user: '田中 太郎',
    group: '4名',
    options: 'コース料理A',
    contact: '090-1234-5678',
  },
  {
    id: 2,
    status: 'pending',
    dateTime: '2023-10-16 19:30',
    user: 'グループ名・ユーザー名',
    group: '2名',
    options: 'オプション名',
    contact: '080-8765-4321',
  },
  {
    id: 3,
    status: 'pending',
    dateTime: '2023-10-16 19:30',
    user: 'グループ名・ユーザー名',
    group: '2名',
    options: 'オプション名',
    contact: '080-8765-4321',
  },
  {
    id: 4,
    status: 'pending',
    dateTime: '2023-10-16 19:30',
    user: 'グループ名・ユーザー名',
    group: '2名',
    options: 'オプション名',
    contact: '080-8765-4321',
  },
  {
    id: 5,
    status: 'pending',
    dateTime: '2023-10-16 19:30',
    user: 'グループ名・ユーザー名',
    group: '2名',
    options: 'オプション名',
    contact: '080-8765-4321',
  },
  {
    id: 6,
    status: 'pending',
    dateTime: '2023-10-16 19:30',
    user: 'グループ名・ユーザー名',
    group: '2名',
    options: 'オプション名',
    contact: '080-8765-4321',
  },
  {
    id: 7,
    status: 'cancelled',
    dateTime: '2023-10-17 12:00',
    user: '鈴木 一郎',
    group: '6名',
    options: '誕生日ケーキ',
    contact: '070-5555-6666',
  },
];

export const sampleAdminReservationDetails = [
  {
    header: ['予約者情報', '氏名'],
    subtext: ['斉藤　翼'],
  },
  {
    header: '氏名(カナ)',
    subtext: ['サイトウ ツバサ'],
  },
  {
    header: '電話番号',
    subtext: ['08012345678'],
  },
  {
    header: 'メールアドレス',
    subtext: ['<EMAIL>'],
  },
  {
    header: ['予約プラン', 'お客様の人数'],
    subtext: ['4名様'],
  },
  {
    header: '予約プラン',
    subtext: ['Meeting room 1'],
  },
  {
    header: '時間',
    subtext: ['2時間'],
  },
  {
    header: '備考',
    subtext: ['※施術に関するお問合わせやご要望等'],
  },

];

