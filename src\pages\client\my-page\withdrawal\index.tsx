import CardContainer from '@/components/ui/cards/card-container';
import { Box, Button, Flex, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Router from 'next/router';

function Withdrawal() {
  const { t } = useTranslation('common');
  // TODO: get points from API
  const points_value = 0;

  return (
    <form>
      <Flex flexDirection={'column'} gap={'1rem'}>
        <Text mb={'1rem'} variant={'header'}>
          {t('withdrawal.withdrawal')}
        </Text>

        <CardContainer>
          <Box>
            <Text textAlign={'center'} variant={'subHeaderXL'}>
              {t('withdrawal.leave_now_warning')}
            </Text>
            <Text textAlign={'center'} variant={'subHeaderXL'} color={'mainColor'}>
              {t('withdrawal.benefits_lost')}
            </Text>
          </Box>

          <Text textAlign={'center'} variant={'subHeaderSmall'}>
            {t('withdrawal.points_warning')}
          </Text>
        </CardContainer>

        <CardContainer>
          <Flex
            justifyContent={'space-between'}
            p={'1rem'}
            borderRadius={'.5rem'}
            boxShadow={'0px 0px 10px 0px rgba(0, 0, 0, 0.1)'}
            w={'100%'}
          >
            <Flex gap={'.4rem'}>
              <Text fontWeight={'bold'}>{t('withdrawal.points')}</Text>
              <Text color={'mainColor'} fontWeight={'bold'}>
                {t('withdrawal.points_value', { points: points_value })}
              </Text>
            </Flex>

            <Text color={'red.600'}>{t('withdrawal.immediate_loss')}</Text>
          </Flex>

          <Flex gap={'1rem'} w={'100%'}>
            <Button
              py={'1.3rem'}
              fontWeight={'normal'}
              borderColor={'black'}
              variant={'rounded'}
              w={'100%'}
              onClick={() => {
                Router.back();
              }}
            >
              {t('withdrawal.cancel')}
            </Button>
            <Button py={'1.3rem'} fontWeight={'normal'} variant={'roundedBlue'} w={'100%'}>
              {t('withdrawal.withdraw')}
            </Button>
          </Flex>
        </CardContainer>
      </Flex>
    </form>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'auth'])),
      layoutType: 'client',
    },
  };
}

export default Withdrawal;
