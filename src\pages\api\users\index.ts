import type { NextApiRequest, NextApiResponse } from 'next';

type User = {
  id: number;
  username: string;
  email: string;
  password: string;
  fullName: string;
  kana: string;
  snsLink: string;
  phoneNumber: string;
  address: string;
  role: string;
};

let users: User[] = [
  {
    id: 1,
    username: 'user1',
    email: '<EMAIL>',
    password: 'password1',
    fullName: '太郎　山田',
    kana: 'タロウ　ヤマダ',
    snsLink: 'https://twitter.com/example',
    phoneNumber: '090-1234-5678',
    address: '東京都港区麻布十番2丁目2-9 4F',
    role: 'storeAdmin',
  },
  {
    id: 2,
    username: 'user2',
    email: '<EMAIL>',
    password: 'password2',
    fullName: '花子　鈴木',
    kana: 'ハナコ　スズキ',
    snsLink: 'https://instagram.com/hanako',
    phoneNumber: '080-2345-6789',
    address: '東京都渋谷区神宮前1-1-1',
    role: 'customer',
  },
  {
    id: 3,
    username: 'user3',
    email: '<EMAIL>',
    password: 'password3',
    fullName: '健二　佐藤',
    kana: 'ケンジ　サトウ',
    snsLink: 'https://facebook.com/kenji',
    phoneNumber: '070-3456-7890',
    address: '大阪府大阪市北区梅田3-3-3',
    role: 'storeAdmin',
  },
  {
    id: 4,
    username: 'user4',
    email: '<EMAIL>',
    password: 'password4',
    fullName: '雪　田中',
    kana: 'ユキ　タナカ',
    snsLink: 'https://twitter.com/yukitanaka',
    phoneNumber: '090-4567-8901',
    address: '福岡県福岡市博多区博多駅前4-4-4',
    role: 'customer',
  },
  {
    id: 5,
    username: 'user5',
    email: '<EMAIL>',
    password: 'password5',
    fullName: '明　伊藤',
    kana: 'アキラ　イトウ',
    snsLink: 'https://linkedin.com/in/akira',
    phoneNumber: '080-5678-9012',
    address: '北海道札幌市中央区北5条西5-5-5',
    role: 'storeAdmin',
  },
  {
    id: 6,
    username: 'user6',
    email: '<EMAIL>',
    password: 'password6',
    fullName: '芽衣　高橋',
    kana: 'メイ　タカハシ',
    snsLink: 'https://instagram.com/mei_taka',
    phoneNumber: '070-6789-0123',
    address: '京都府京都市下京区四条通6-6-6',
    role: 'customer',
  },
  {
    id: 7,
    username: 'user7',
    email: '<EMAIL>',
    password: 'password7',
    fullName: '涼　渡辺',
    kana: 'リョウ　ワタナベ',
    snsLink: 'https://twitter.com/ryo_w',
    phoneNumber: '090-7890-1234',
    address: '愛知県名古屋市中区栄7-7-7',
    role: 'storeAdmin',
  },
  {
    id: 8,
    username: 'user8',
    email: '<EMAIL>',
    password: 'password8',
    fullName: '直美　小林',
    kana: 'ナオミ　コバヤシ',
    snsLink: 'https://facebook.com/naomi.k',
    phoneNumber: '080-8901-2345',
    address: '兵庫県神戸市中央区三宮町8-8-8',
    role: 'customer',
  },
  {
    id: 9,
    username: 'user9',
    email: '<EMAIL>',
    password: 'password9',
    fullName: '浩　加藤',
    kana: 'ヒロシ　カトウ',
    snsLink: 'https://linkedin.com/in/hiroshi',
    phoneNumber: '070-9012-3456',
    address: '宮城県仙台市青葉区中央9-9-9',
    role: 'storeAdmin',
  },
  {
    id: 10,
    username: 'admin',
    email: '<EMAIL>',
    password: 'admin',
    fullName: '管理者　山本',
    kana: 'カンリシャ　ヤマモト',
    snsLink: 'https://twitter.com/admin_yama',
    phoneNumber: '090-0123-4567',
    address: '東京都千代田区丸の内1-10-10',
    role: 'superAdmin',
  },
];

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<User[] | { message: string }>
) {
  if (req.method === 'GET') {
    return res.status(200).json(users);
  } else if (req.method === 'POST') {
    const { id, username, password } = req.body;

    if (!id || typeof id !== 'number') {
      return res.status(400).json({ message: 'User ID is required and must be a number' });
    }

    const userIndex = users.findIndex(user => user.id === id);

    if (userIndex === -1) {
      return res.status(404).json({ message: `User with ID ${id} not found` });
    }

    users[userIndex] = {
      ...users[userIndex],
      id,
      username: username || users[userIndex].username,
      password: password || users[userIndex].password,
    };

    return res.status(200).json(users);
  } else {
    return res.status(405).json({ message: 'Method not allowed' });
  }
}
