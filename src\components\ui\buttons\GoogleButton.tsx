import { Button, Image, Text, Flex } from '@chakra-ui/react';
import React from 'react';
import { useTranslation } from 'next-i18next';
import Link from 'next/link';

export const GoogleButton = () => {
  const { t } = useTranslation('auth');
  return (
    <Button py={'1.5rem'} variant={'rounded'} display={'flex'} justifyContent={'space-between'}>
      <Flex alignItems={'center'}>
        <Image src="/imgs/icons/google.png" alt="Google Logo" width={6} height={6} />
        <Text fontSize={'md'} ml={3}>
          Googleカレンダー
        </Text>
      </Flex>
      <Link href={'#'}>
        <Text fontSize={'sm'} color={'blue'} fontWeight={'normal'}>
          紐付ける
        </Text>
      </Link>
    </Button>
  );
};
