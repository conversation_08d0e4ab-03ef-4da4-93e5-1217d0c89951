export const text = {
  brand: {
    fontFamily: '"Noto Sans JP", sans-serif',
    fontWeight: '900',
    color: '#2b42ca',
    letterSpacing: '-.03em',
    fontSize: '32px',
    lineHeight: '32px',
    textTransform: 'uppercase',
  },
  underline: {
    textDecoration: 'underline',
  },
  header: {
    fontFamily: '"Noto Sans JP", sans-serif',
    fontWeight: '600',
    letterSpacing: '-.05em',
    fontSize: '32px',
    lineHeight: '32px',
    color: 'black',
  },
  subHeader: {
    fontWeight: '500',
    letterSpacing: '-.05em',
    fontSize: '.8rem',
    lineHeight: '32px',
  },

  subHeaderXL: {
    fontWeight: '700',
    letterSpacing: '-.05em',
    fontSize: '1.4rem',
    lineHeight: '32px',
  },

  subHeaderLarge: {
    fontWeight: '700',
    letterSpacing: '-.05em',
    fontSize: '1.2rem',
    lineHeight: '32px',
  },

  subHeaderNormal: {
    fontWeight: '700',
    letterSpacing: '-.05em',
    fontSize: '1rem',
    lineHeight: '32px',
  },

  subHeaderSmall: {
    fontWeight: '700',
    letterSpacing: '-.05em',
    fontSize: '.8rem',
    lineHeight: '32px',
  },

  subHeaderXSmall: {
    fontWeight: '700',
    letterSpacing: '-.05em',
    fontSize: '.6rem',
    lineHeight: '32px',
  },

  category: {
    fontWeight: '400',
    letterSpacing: '-.05em',
    fontSize: '.70rem',
    lineHeight: '32px',
    color: 'mainColor',
    backgroundColor: 'blue.100',
    padding: '0 .5rem',
    borderRadius: '5px',
    height: '1.5rem',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },

  categoryGray: {
    fontWeight: '400',
    letterSpacing: '-.05em',
    fontSize: '.70rem',
    lineHeight: '32px',
    color: 'gray.200',
    backgroundColor: 'gray.400',
    padding: '0 .5rem',
    borderRadius: '5px',
    height: '1.5rem',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  error: {
    letterSpacing: '-.05em',
    fontSize: '.75rem',
    lineHeight: '32px',
    backgroundColor: '#FEE2E2',
    color: 'red.600',
    padding: '.6rem 1rem',
    paddingLeft: '3rem',
    borderRadius: '10px',
    position: 'relative',
    _before: {
      content: '""',
      position: 'absolute',
      left: '1rem',
      top: '50%',
      transform: 'translateY(-50%)',
      width: '20px',
      height: '20px',
      backgroundImage: "url('/imgs/icons/error.png')",
      backgroundSize: 'contain',
      backgroundRepeat: 'no-repeat',
    },
  },
  errorText: {
    color: 'red.600',
    fontSize: '.8rem',
    marginTop: '.5rem',
    fontWeight: '500',
  },
};
