import CheckCircleIcon from '@/assets/icons/check-circle';
import FlagIcon from '@/assets/icons/flag-icon';
import TrashIcon from '@/assets/icons/trash';
import CardContainer from '@/components/ui/cards/card-container';
import {
  Box,
  Button,
  Checkbox,
  Flex,
  Grid,
  Link,
  Modal,
  ModalBody,
  ModalContent,
  ModalFooter,
  ModalHeader,
  ModalOverlay,
  Text,
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Router from 'next/router';

import { useDisclosure } from '@chakra-ui/react';

function AddGroup() {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { t } = useTranslation('profile');

  return (
    <Flex flexDirection={'column'}>
      <CardContainer gap={'1rem'}>
        <Flex w={'100%'}>
          <Text textAlign={'left'} variant={'header'}>
            {t('groups.memberInvitation')}
          </Text>
        </Flex>
        <Flex w={'100%'} justifyContent={'flex-end'}>
          <Flex>
            <Button variant={'rounded'} py={'1.3rem'} bg={'borderGray'}>
              <TrashIcon height={20} width={20} />
              <Text ml={'.5rem'} fontWeight={'normal'}>
                {t('groups.delete')}
              </Text>
            </Button>
          </Flex>
        </Flex>
        <CardContainer variant={'bordered'}>
          <Flex w={'100%'} gap={'.5rem'}>
            <Flex height={'20px'}>
              <Checkbox borderColor={'black'} size={'sm'} />
            </Flex>
            <Flex flexDirection={'column'}>
              <Text fontSize={'sm'} fontWeight={'bold'}>
                {t('groups.customerGroupMembers')}
              </Text>
              <Text fontSize={'sm'} fontWeight={'medium'}>
                {t('groups.membersCount', { count: 4, max: 5 })}
              </Text>
            </Flex>
          </Flex>

          <Grid w={'100%'} px={'1rem'} templateColumns="1fr 1fr" gap={2}>
            <Flex w={'100%'} justifyContent={'space-between'} alignItems={'center'}>
              <Text fontSize={'sm'}>斉藤翼 様 </Text>
              <FlagIcon width={15} height={15} />
            </Flex>
            <Flex w={'100%'} justifyContent={'space-between'} alignItems={'center'}>
              <Text fontSize={'sm'}>Yoyaku taro 様 様 </Text>
              <TrashIcon width={20} height={20} />
            </Flex>
            <Flex w={'100%'} justifyContent={'space-between'} alignItems={'center'}>
              <Text fontSize={'sm'}>山田太郎 様</Text>
              <TrashIcon width={20} height={20} />
            </Flex>
            <Flex w={'100%'} justifyContent={'space-between'} alignItems={'center'}>
              <Text fontSize={'sm'}>山田太郎 様 </Text>
              <TrashIcon width={20} height={20} />
            </Flex>
          </Grid>

          <Flex w={'100%'} gap={'1rem'}>
            <Button variant={'rounded'} py={'1.3rem'} bg={'borderGray'}>
              <Text fontWeight={'normal'}>{t('groups.changeMaxCapacity')}</Text>
            </Button>
            <Button variant={'roundedBlue'} py={'1.3rem'}>
              <Text color={'white'} fontWeight={'normal'}>
                {t('groups.addMember')}
              </Text>
            </Button>
          </Flex>
        </CardContainer>

        <Flex w={'100%'}>
          <Button
            variant={'rounded'}
            borderColor={'black'}
            py={'1.3rem'}
            onClick={() => {
              onOpen();
            }}
          >
            {t('groups.createNewGroup')}
          </Button>
        </Flex>
      </CardContainer>

      <Modal isOpen={isOpen} onClose={() => {}}>
        <form>
          <ModalOverlay />
          <ModalContent>
            <ModalHeader
              textAlign={'center'}
              display={'flex'}
              alignItems={'center'}
              justifyContent={'center'}
              flexDirection={'column'}
              gap={'1rem'}
            >
              <Text color={'mainColor'} variant={'header'}>
                JUSTYOYAKU
              </Text>
              <Text color={'black'} variant={'subHeaderXL'}>
                {t('groups.reservationComplete')}
              </Text>

              <CheckCircleIcon />

              <Flex flexDirection={'column'} justifyContent={'center'} gap={'.5rem'}>
                <Flex justifyContent={'center'} alignItems={'center'} gap={'1rem'}>
                  <Text fontSize={'xs'} variant={'category'}>
                    {t('groups.requestReservation')}
                  </Text>
                  <Text fontSize={'sm'} variant={'subHeaderXL'}>
                    000000000000
                  </Text>
                </Flex>
                <Flex justifyContent={'center'} alignItems={'center'}>
                  <Text fontSize={'sm'} fontWeight={'normal'}>
                    {t('groups.reservationCompleted')}
                  </Text>
                  <Link fontSize={'sm'} fontWeight={'normal'} variant={'underline'}>
                    {t('groups.checkReservation')}
                  </Link>
                </Flex>
              </Flex>
            </ModalHeader>
            <ModalBody>
              <CardContainer bg={'gray.50'} p={'1.5rem'}>
                <Flex
                  w={'100%'}
                  pb={'1rem'}
                  flexDirection={'column'}
                  gap={'.7rem'}
                  borderBottom={'1px solid rgb(214, 222, 233)'}
                >
                  <Text fontSize={'md'} fontWeight={'medium'}>
                    {t('groups.precautions')}
                  </Text>
                  <Text fontSize={'md'} fontWeight={'medium'}>
                    {t('groups.reservationPrecautions')}
                  </Text>
                  <Box>
                    <Text fontSize={'sm'} color={'black'}>
                      {t('groups.contactNotice')}
                    </Text>
                    <Text fontSize={'sm'} color={'black'}>
                      {t('groups.contactRequest')}
                    </Text>
                  </Box>
                </Flex>
                <Flex w={'100%'} pb={'1rem'} flexDirection={'column'} gap={'.7rem'}>
                  <Text fontSize={'md'} fontWeight={'medium'}>
                    {t('groups.cancellationPolicy')}
                  </Text>
                  <Box>
                    <Text fontSize={'xs'} color={'black'}>
                      {t('groups.cancellationDetails')}
                    </Text>
                  </Box>
                </Flex>
              </CardContainer>
            </ModalBody>

            <ModalFooter p={'1.5rem'}>
              <Flex w="100%" alignItems={'center'} gap={4}>
                <Button
                  variant="rounded"
                  bg={'gray.100'}
                  borderColor={'transparent'}
                  flex={1}
                  onClick={() => {
                    Router.push('/client/my-page');
                  }}
                >
                  {t('groups.close')}
                </Button>
              </Flex>
            </ModalFooter>
          </ModalContent>
        </form>
      </Modal>
    </Flex>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'auth', 'profile'])),
      layoutType: 'client',
    },
  };
}

export default AddGroup;
