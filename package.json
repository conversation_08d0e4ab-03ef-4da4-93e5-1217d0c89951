{"name": "just-yoyaku", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env APP_ENV=dev next dev", "local": "APP_ENV=local next dev", "local_win": "set APP_ENV=local&& next dev", "build": "next build", "start": "next start", "lint": "next lint --dir src", "lint:fix": "yarn lint --fix", "format": "prettier --write --ignore-path .gitignore './**/*.{js,jsx,ts,tsx,json}'", "codegen": "graphql-codegen --config codegen.yml", "prepare": "husky install", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write"], "*.{json,css,md}": ["prettier --write"]}, "dependencies": {"@apollo/client": "^3.7.17", "@chakra-ui/icons": "^2.0.19", "@chakra-ui/react": "2.10.7", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/inter": "^5.2.6", "@fontsource/noto-sans-jp": "^5.1.0", "@fontsource/roboto": "^5.1.0", "@graphcms/rich-text-react-renderer": "^0.6.1", "@react-input/mask": "^1.2.5", "@types/node": "20.3.2", "@types/react": "18.2.14", "@types/react-big-calendar": "^1.16.2", "@types/react-dom": "18.2.6", "apollo-upload-client": "^17.0.0", "axios": "^1.4.0", "clsx": "^2.0.0", "cross-env": "^7.0.3", "date-fns": "^3.6.0", "dayjs": "^1.11.9", "eslint": "8.43.0", "eslint-config-next": "13.4.7", "firebase": "^10.7.1", "framer-motion": "^12.5.0", "graphql": "^16.7.1", "graphql-request": "^6.1.0", "html-react-parser": "^5.0.6", "i18next": "^24.2.3", "immer": "^10.1.1", "lodash": "^4.17.21", "moment": "^2.30.1", "next": "13.4.7", "next-i18next": "^15.4.2", "next-themes": "^0.4.6", "quill": "^1.3.7", "quill-delta-to-html": "^0.12.1", "ra-data-graphql": "^4.12.1", "react": "18.2.0", "react-big-calendar": "^1.19.4", "react-datepicker": "^7.5.0", "react-dom": "18.2.0", "react-hook-form": "^7.51.0", "react-i18next": "^15.4.1", "react-icons": "^5.5.0", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^9.0.1", "react-mobile-picker": "^0.2.1", "react-paginate": "^8.2.0", "react-quill": "^2.0.0", "react-slick": "^0.29.0", "react-youtube": "^10.1.0", "remark-gfm": "^4.0.0", "slick-carousel": "^1.8.1", "typescript": "5.1.5", "universal-cookie": "^4.0.4", "zustand": "^5.0.4"}, "devDependencies": {"@chakra-ui/storybook-addon": "^5.0.1", "@graphql-codegen/cli": "^4.0.1", "@graphql-codegen/import-types-preset": "^2.2.6", "@graphql-codegen/typed-document-node": "^5.0.1", "@graphql-codegen/typescript": "^2.8.1", "@graphql-codegen/typescript-graphql-request": "^5.0.0", "@graphql-codegen/typescript-operations": "^4.0.1", "@graphql-codegen/typescript-react-apollo": "^3.3.6", "@storybook/addon-essentials": "^7.4.0", "@storybook/addon-interactions": "^7.4.0", "@storybook/addon-links": "^7.4.0", "@storybook/addon-onboarding": "^1.0.8", "@storybook/blocks": "^7.4.0", "@storybook/nextjs": "^7.4.0", "@storybook/react": "^7.4.0", "@storybook/testing-library": "^0.2.0", "@svgr/webpack": "^8.0.1", "@types/apollo-upload-client": "^17.0.3", "@types/gtag.js": "^0.0.16", "@types/react-datepicker": "^7.0.0", "@types/react-slick": "^0.23.10", "@typescript-eslint/eslint-plugin": "^5.60.1", "@typescript-eslint/parser": "^5.60.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-storybook": "^0.6.12", "husky": "^9.1.7", "lint-staged": "^15.5.1", "prettier": "^2.8.8", "storybook": "^7.4.0"}}