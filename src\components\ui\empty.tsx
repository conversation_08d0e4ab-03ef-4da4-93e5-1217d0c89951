import OptionsListIcon from '@/assets/icons/options-list';
import { AddIcon } from '@chakra-ui/icons';
import { Button, Flex, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import Router from 'next/router';
import React from 'react';

function Empty({
  setOptionList,
}: {
  setOptionList: React.Dispatch<React.SetStateAction<boolean>>;
}) {
  const { t } = useTranslation('common');
  return (
    <Flex
      minH={'90vh'}
      flexDirection={'column'}
      bg={'white'}
      justifyContent={'center'}
      alignItems={'center'}
      textAlign={'center'}
      gap={4}
      p={5}
      pt={'2rem'}
      borderRadius={'1rem'}
    >
      <OptionsListIcon width={25} height={25} />

      <Text fontSize={'lg'} fontWeight={'bold'}>
        {t('optionslist.empty.title')}
      </Text>
      <Text fontSize={'sm'} whiteSpace="pre-line">
        {t('optionslist.empty.description')}
      </Text>
      <Button
        w={'auto'}
        variant={'roundedBlue'}
        onClick={() => {
          Router.push('/admin/options-list/option-list-add');
        }}
      >
        <AddIcon width={3} height={3} color="white" mr={'0.5rem'} />{' '}
        {t('optionslist.empty.addButton')}
      </Button>
    </Flex>
  );
}

export default Empty;
