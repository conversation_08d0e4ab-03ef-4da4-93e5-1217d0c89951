import React from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>dal<PERSON>ooter,
  But<PERSON>,
  Text,
} from '@chakra-ui/react'
import AssignmentIcon from '@/assets/icons/assignment'

export default function GroupDetailsEditingCompletedModal({
    isModalOpen,
    onModalClose,
}: {
    isModalOpen: boolean;
    onModalClose: () => void;
}) {
  return (
    <Modal
    isOpen={isModalOpen}
    onClose={() => {
        onModalClose();
    }}
    >
    <ModalOverlay />
        <ModalContent>
            <ModalHeader
            display={'flex'}
            flexDirection={'column'}
            gap={'.5rem'}
            alignItems={'center'}
            >
            <AssignmentIcon width={30} height={30} color="black" />

            <Text textAlign={'center'} variant={'subHeaderNormal'}>
                グループ詳細編集が完了しました
            </Text>
            </ModalHeader>
            <ModalFooter justifyContent={'center'}>
            <Button
                w={'auto'}
                px={'3rem'}
                fontWeight={'normal'}
                variant={'roundedBlue'}
                onClick={() => {
                    onModalClose();
                }}
            >
                閉じる
            </Button>
            </ModalFooter>
        </ModalContent>
    </Modal>
  )
}
