import { sampleGroupMembers } from '@/.temp/sampleGroupMembers';
import ChangeChargeMemberDetails from '@/components/modals/ChangeChargeMemberDetails';
import DeleteConfirmationModal from '@/components/modals/delete-confirmation-modal';
import EditCompleteModal from '@/components/modals/EditCompleteModal';
import AdminPreviewsPage from '@/components/ui/AdminPreviewsPage';
import InputTextEditMode from '@/components/ui/formfields/InputTextEditMode';
import ProfileIcon from '@/components/ui/profile-icon';
import TextPair from '@/components/ui/TextPair';
import EditDeleteDesktopBtns from '@/pageContainer/admin/members-list/member-details-main/EditDeleteDesktopBtns';
import EditDeleteMobileBtns from '@/pageContainer/admin/members-list/member-details-main/EditDeleteMobileBtns';
import GroupMember from '@/pageContainer/admin/members-list/member-details-main/GroupMember';
import { Button, Flex, Grid, Text, useDisclosure } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useRouter } from 'next/router';
import { useState } from 'react';

export default function StoreDetailsPage() {
  const { t } = useTranslation('admin');
  const router = useRouter();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();
  const [modalStep, setModalStep] = useState(1);
  const [deleteStep, setDeleteStep] = useState(1);
  const {
    isOpen: isEditCompleteOpen,
    onOpen: onEditCompleteOpen,
    onClose: onEditCompleteClose,
  } = useDisclosure();

  const [editMode, setEditMode] = useState(false);

  const handleClose = () => {
    setModalStep(1);
    onClose();
  };

  const handleDelete = () => {
    console.log('Deleting member');
    setDeleteStep(2);
  };

  const handleDeleteClose = () => {
    setDeleteStep(1);
    onDeleteClose();
  };

  return (
    <Flex
      w={'100%'}
      flexDirection={'column'}
      pr={{ base: 0, md: 6 }}
      pl={{ base: 0, md: 6, lg: 0 }}
    >
      <Flex
        mb={{ base: '0' }}
        flexDirection={{ base: 'column', sm: 'row' }}
        bg={{ base: 'white', md: 'unset' }}
        px={{ base: 5, lg: 0 }}
        justifyContent={{ base: 'space-between' }}
        height={{ base: 'unset', sm: '5rem' }}
        w={'100%'}
      >
        <AdminPreviewsPage label={t('memberDetails.header.title')} />

        <EditDeleteMobileBtns
          editMode={editMode}
          setEditMode={setEditMode}
          onDeleteOpen={onDeleteOpen}
        />
      </Flex>

      <Flex flexDirection={'column'} bg={'white'} gap={4} p={5} borderRadius={'1rem'}>
        <EditDeleteDesktopBtns
          editMode={editMode}
          setEditMode={setEditMode}
          onDeleteOpen={onDeleteOpen}
        />

        <Flex px={'1rem'} flexDirection={'column'}>
          <Flex justifyContent={'center'}>
            <Flex w={'30rem'}>
              <ProfileIcon />
            </Flex>
          </Flex>

          <TextPair
            lineHeight={'1.5rem'}
            header={t('memberDetails.form.email')}
            subtext={['<EMAIL>']}
          />

          <InputTextEditMode
            editMode={editMode}
            label={t('memberDetails.form.contact')}
            value={'080-1234-1234'}
          />

          <Flex
            justifyContent={'space-between'}
            alignItems={'center'}
            borderBottom={'1px solid #E5EAF1'}
            mb={'1rem'}
          >
            <TextPair
              header={t('memberDetails.form.credit')}
              subtext={[t('memberDetails.form.creditRemaining')]}
              borderBottom={'none'}
            />
            <Button
              variant={'roundedBlue'}
              fontSize={'sm'}
              fontWeight={'normal'}
              gap={2}
              w={'auto'}
              onClick={onOpen}
              py={'.5rem'}
              px={'2rem'}
            >
              {t('memberDetails.buttons.charge')}
            </Button>
          </Flex>

          <InputTextEditMode
            editMode={editMode}
            label={t('memberDetails.form.email')}
            value={'<EMAIL>'}
          />
          <InputTextEditMode
            editMode={editMode}
            label={t('memberDetails.form.name')}
            value={'斉藤　翼'}
          />
          <InputTextEditMode
            editMode={editMode}
            label={t('memberDetails.form.nameKana')}
            value={'サイトウ ツバサ'}
          />

          <Flex
            flexDirection={'column'}
            gap={2}
            borderBottom={'1px solid #E5EAF1'}
            pb={'1rem'}
            mb={'1rem'}
          >
            <Text fontSize={'md'} fontWeight={'semibold'}>
              {t('memberDetails.form.groupMemberCount')}
            </Text>
            <Text fontSize={'sm'} color={'black600'}>
              {t('memberDetails.display.memberCount', { count: 4 })}
            </Text>
            <Grid templateColumns="repeat(2, 1fr)" gap={2}>
              {sampleGroupMembers.map(member => (
                <GroupMember key={member.id} editMode={editMode} memberName={member.name} />
              ))}
            </Grid>
          </Flex>

          <InputTextEditMode
            editMode={editMode}
            label={t('memberDetails.form.notes')}
            type={'textarea'}
            bottomText={t('memberDetails.form.notesPlaceholder')}
            value={t('memberDetails.display.content')}
          />

          <Flex justifyContent={'center'} gap={2} pb={'1rem'} mb={'1rem'}>
            {editMode ? (
              <Button
                variant={'roundedBlue'}
                fontSize={'sm'}
                fontWeight={'normal'}
                gap={2}
                py={'.5rem'}
                px={'3rem'}
                w={'auto'}
                onClick={() => {
                  onEditCompleteOpen();
                }}
              >
                {t('memberDetails.buttons.complete')}
              </Button>
            ) : null}
          </Flex>
        </Flex>
      </Flex>

      <ChangeChargeMemberDetails
        isOpen={isOpen}
        onClose={handleClose}
        modalStep={modalStep}
        setModalStep={setModalStep}
      />

      <DeleteConfirmationModal
        isOpen={isDeleteOpen}
        deleteStep={deleteStep}
        handleDelete={handleDelete}
        handleClose={handleDeleteClose}
        redirect={'/admin/members-list'}
      />

      <EditCompleteModal
        isOpen={isEditCompleteOpen}
        onClose={onEditCompleteClose}
        onEditCompleteClose={onEditCompleteClose}
        setEditMode={setEditMode}
      />
    </Flex>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'admin'])),
      layoutType: 'withSidebar',
    },
  };
}

export async function getStaticPaths() {
  return {
    paths: [],
    fallback: 'blocking',
  };
}
