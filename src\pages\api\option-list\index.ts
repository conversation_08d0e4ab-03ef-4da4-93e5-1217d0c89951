import type { NextApiRequest, NextApiResponse } from 'next';

type OptionList = {
  id: number;
  image: string;
  category: number;
  tag: number;
  menuTitle: string;
  price: number;
  numberOfPeople: number;
  menuDetails: string;
  reservationTimeSlot: string;
  reservationDate: string;
  maximumNumberOfReservations: number;
  reservationAcceptancePeriod: string;
};

let optionList: OptionList[] = [
  {
    id: 1,
    image:
      'https://images.unsplash.com/photo-1517694712202-14dd9538aa97?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    category: 1,
    tag: 1,
    menuTitle: 'コース料理A',
    price: 10000,
    numberOfPeople: 4,
    menuDetails: '旬の食材を使ったコース料理です。',
    reservationTimeSlot: '18:00-20:00',
    reservationDate: '2023-10-15',
    maximumNumberOfReservations: 10,
    reservationAcceptancePeriod: '10:00-18:00',
  },
  {
    id: 2,
    image:
      'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    category: 1,
    tag: 2,
    menuTitle: '特選和牛コース',
    price: 15000,
    numberOfPeople: 2,
    menuDetails: '厳選された和牛を使用した贅沢なコース料理です。',
    reservationTimeSlot: '19:00-21:00',
    reservationDate: '2023-11-20',
    maximumNumberOfReservations: 8,
    reservationAcceptancePeriod: '9:00-17:00',
  },
  {
    id: 3,
    image:
      'https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    category: 2,
    tag: 1,
    menuTitle: '季節の野菜サラダ',
    price: 1800,
    numberOfPeople: 1,
    menuDetails: '地元で採れた新鮮な野菜を使用したサラダです。',
    reservationTimeSlot: '11:30-13:30',
    reservationDate: '2023-12-01',
    maximumNumberOfReservations: 15,
    reservationAcceptancePeriod: '10:00-19:00',
  },
  {
    id: 4,
    image:
      'https://images.unsplash.com/photo-1519389950473-47ba0277781c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    category: 2,
    tag: 2,
    menuTitle: 'シェフの特製パスタ',
    price: 2500,
    numberOfPeople: 1,
    menuDetails: 'シェフが厳選した食材で作る特別なパスタです。',
    reservationTimeSlot: '12:00-14:00',
    reservationDate: '2023-12-10',
    maximumNumberOfReservations: 12,
    reservationAcceptancePeriod: '9:30-18:30',
  },
  {
    id: 5,
    image:
      'https://images.unsplash.com/photo-1497215842964-222b430dc094?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    category: 1,
    tag: 1,
    menuTitle: '冬の味覚コース',
    price: 12000,
    numberOfPeople: 3,
    menuDetails: '冬の味覚を楽しめる季節限定コースをご用意しました。',
    reservationTimeSlot: '18:30-20:30',
    reservationDate: '2023-12-15',
    maximumNumberOfReservations: 6,
    reservationAcceptancePeriod: '10:00-17:00',
  },
  {
    id: 6,
    image:
      'https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    category: 2,
    tag: 2,
    menuTitle: '特選デザート盛り合わせ',
    price: 1500,
    numberOfPeople: 2,
    menuDetails: '季節のフルーツを使った特製デザートの盛り合わせです。',
    reservationTimeSlot: '14:00-16:00',
    reservationDate: '2023-12-20',
    maximumNumberOfReservations: 20,
    reservationAcceptancePeriod: '10:30-19:30',
  },
];

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<OptionList[] | { message: string }>
) {
  if (req.method === 'GET') {
    return res.status(200).json(optionList);
  } else if (req.method === 'POST') {
    const {
      id,
      category,
      title,
      date,
      image,
      details,
      tags,
      reservationTimeSlot,
      reservationDate,
      maximumNumberOfReservations,
      reservationAcceptancePeriod,
    } = req.body;

    if (!id || typeof id !== 'number') {
      return res.status(400).json({ message: 'Option ID is required and must be a number' });
    }

    const optionIndex = optionList.findIndex(option => option.id === id);

    if (optionIndex === -1) {
      return res.status(404).json({ message: `Option with ID ${id} not found` });
    }

    optionList[optionIndex] = {
      ...optionList[optionIndex],
      id,
      category: category || optionList[optionIndex].category,
      menuTitle: title || optionList[optionIndex].menuTitle,
      reservationDate: date || optionList[optionIndex].reservationDate,
      image: image || optionList[optionIndex].image,
      menuDetails: details || optionList[optionIndex].menuDetails,
      tag: tags || optionList[optionIndex].tag,
      reservationTimeSlot: reservationTimeSlot || optionList[optionIndex].reservationTimeSlot,
      maximumNumberOfReservations:
        maximumNumberOfReservations || optionList[optionIndex].maximumNumberOfReservations,
      reservationAcceptancePeriod:
        reservationAcceptancePeriod || optionList[optionIndex].reservationAcceptancePeriod,
    };

    return res.status(200).json(optionList);
  } else {
    return res.status(405).json({ message: 'Method not allowed' });
  }
}
