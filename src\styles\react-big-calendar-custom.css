/* Custom styles for react-big-calendar to match the design */

/* General calendar styling */
.rbc-calendar {
  font-family: 'Noto Sans JP', sans-serif !important;
  font-size: 14px;
}

/* Header styling */
.rbc-header {
  padding: 12px 8px !important;
  font-weight: bold !important;
  /* background-color: #f7fafc !important; */
  border-bottom: 1px solid #e2e8f0 !important;
  text-align: center !important;
  color: #334155 !important;
}

.rbc-time-header-cell {
  border-bottom: none !important;
}

/* Today highlighting */
.rbc-today {
  background-color: #f9fcfd !important;
}

/* Off-range dates (previous/next month) */
.rbc-off-range-bg {
  background-color: #f7fafc !important;
  opacity: 0.5 !important;
}

/* Event styling */
.rbc-event {
  border-radius: 6px !important;
  font-size: 11px !important;
  font-weight: bold !important;
  padding: 2px 4px !important;
  border: none !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* Month view specific */
.rbc-month-view {
  border: none !important;
}

.rbc-month-row {
  border-bottom: 1px solid #e2e8f0 !important;
}

.rbc-allday-cell {
  display: none !important;
}

.rbc-day-bg {
  border-right: 1px solid #e2e8f0 !important;

  height: 100% !important;
}

.rbc-date-cell {
  padding: 8px !important;
  text-align: center !important;
  font-weight: 500 !important;
}

.rbc-date-cell > a {
  color: #334155 !important;
  text-decoration: none !important;
}

.rbc-date-cell.rbc-off-range > a {
  color: #94a3b8 !important;
}

/* Week and day view styling */
.rbc-time-view .rbc-time-gutter {
  border-right: 1px solid #e2e8f0 !important;
  background-color: #f7fafc !important;
}

/* .rbc-time-view .rbc-time-slot {
  border-top: 1px solid #e2e8f0 !important;
} */

.rbc-time-view .rbc-current-time-indicator {
  background-color: #2b42ca !important;
  height: 2px !important;
}

.rbc-timeslot-group {
  border-bottom: 1px solid #e2e8f0 !important;
}

.rbc-time-header {
  border-bottom: 1px solid #e2e8f0 !important;
}

.rbc-time-header-content {
  border-left: 1px solid #e2e8f0 !important;
}

/* Time labels */
.rbc-time-slot .rbc-label {
  font-size: 12px !important;
  color: #64748b !important;
  text-align: right !important;
  padding-right: 8px !important;
}

/* Event positioning and sizing */
.rbc-event-content {
  font-size: 11px !important;
  line-height: 1.2 !important;
}

/* Popup styling */
.rbc-overlay {
  background-color: white !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 8px !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
  padding: 8px !important;
}

.rbc-overlay-header {
  font-weight: bold !important;
  margin-bottom: 8px !important;
  color: #2b42ca !important;
}

/* Show more link */
.rbc-show-more {
  color: #2b42ca !important;
  font-size: 11px !important;
  font-weight: bold !important;
  text-decoration: none !important;
  padding: 2px 4px !important;
  border-radius: 4px !important;
  background-color: #ebf8ff !important;
}

.rbc-show-more:hover {
  background-color: #dbeafe !important;
}

/* Agenda view styling */
.rbc-agenda-view {
  border: 1px solid #e2e8f0 !important;
  border-radius: 8px !important;
}

.rbc-agenda-view table {
  border-collapse: collapse !important;
}

.rbc-agenda-view .rbc-agenda-date-cell {
  background-color: #f7fafc !important;
  border-right: 1px solid #e2e8f0 !important;
  padding: 12px !important;
  font-weight: bold !important;
}

.rbc-agenda-view .rbc-agenda-time-cell {
  border-right: 1px solid #e2e8f0 !important;
  padding: 8px 12px !important;
  color: #64748b !important;
}

.rbc-agenda-view .rbc-agenda-event-cell {
  padding: 8px 12px !important;
}

/* Custom room colors for events */
/* .rbc-event.room-01 {
  background-color: #2b42ca !important;
}

.rbc-event.room-02 {
  background-color: #4299e1 !important;
}

.rbc-event.room-03 {
  background-color: #845fe1 !important;
}

.rbc-event.room-04 {
  background-color: #78ca2b !important;
}

.rbc-event.room-05 {
  background-color: #f49751 !important;
} */

/* Cancelled events */
.rbc-event.status-cancelled {
  opacity: 0.6 !important;
  background-color: #f11717 !important;
  text-decoration: line-through !important;
}

/* Pending events */
/* .rbc-event.status-pending {
  border: 2px dashed rgba(255, 255, 255, 0.8) !important;
} */

/* Responsive adjustments */
@media (max-width: 768px) {
  .rbc-calendar {
    font-size: 12px !important;
  }

  .rbc-event {
    font-size: 10px !important;
    padding: 1px 2px !important;
  }

  .rbc-header {
    padding: 8px 4px !important;
    font-size: 12px !important;
  }

  .rbc-date-cell {
    padding: 4px !important;
  }
}

/* Hover effects */
.rbc-event:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.2s ease !important;
}

/* .rbc-day-bg:hover {
  background-color: #f1f5f9 !important;
  transition: background-color 0.2s ease !important;
} */

/* Selection styling */
.rbc-selected {
  background-color: #2b42ca !important;
  color: white !important;
}

/* Toolbar hiding (since we use custom navigation) */
.rbc-toolbar {
  display: none !important;
}
