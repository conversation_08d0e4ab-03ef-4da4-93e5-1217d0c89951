import { useState } from 'react';
import { FormDataType } from '@/types/FormDataType';

const useToggleTimeSelect = () => {
  const [formData, setFormData] = useState<FormDataType>({
    profileIcon: '',
    username: '',
    groupname: '',
    storeName: '',
    storeID: '',
    storeOverview: '',
    availability: {
      月曜日: false,
      火曜日: false,
      水曜日: false,
      木曜日: false,
      金曜日: false,
      土曜日: false,
      日曜日: false,
      timeSlots: {
        月曜日: [{}],
        火曜日: [{}],
        水曜日: [{}],
        木曜日: [{}],
        金曜日: [{}],
        土曜日: [{}],
        日曜日: [{}],
      },
    },
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    phoneNumber: '',
  });
  return [formData, setFormData] as const;
};

export default useToggleTimeSelect;
