# Just Yoyaku

A modern reservation system application built with Next.js and TypeScript.

## Features

- User authentication and management
- Reservation creation and management
- Admin dashboard for business owners
- Client interface for customers
- Multi-language support (English and Japanese)
- Responsive design for all devices

## Technology Stack

- **Frontend**: Next.js, TypeScript, Chakra UI
- **State Management**: Zustand
- **API**: REST API
- **Localization**: next-i18next
- **Styling**: Emotion (via Chakra UI)

## Installation and Execution

To run this project locally, please follow the steps below:

1. **Clone the repository:**

   ```bash
   git clone https://github.com/loftdev/just-yoyaku.git
   ```

2. **Go to project directory:**

   ```bash
   cd just-yoyaku
   ```

3. **Installing dependencies:**

   ```bash
   yarn install
   ```

4. **Start the development server:**

   ```bash
   yarn dev
   ```

5. **Open your browser and go to `localhost:3000` to view your application.**

## Development Documentation

For detailed development documentation, including:

- Project structure
- Component library
- Styling system
- State management with Zustand
- Localization
- Development workflow
- Best practices

Please see [DEVELOPMENT.md](./DEVELOPMENT.md).
