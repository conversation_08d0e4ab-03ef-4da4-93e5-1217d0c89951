import { useEffect } from 'react';
import { <PERSON>, Button, Flex, <PERSON>ing, Text, VStack, Spinner, Al<PERSON>, AlertIcon } from '@chakra-ui/react';
import { useReservationStore } from '@/store/reservationStore';
import { useRouter } from 'next/router';

export default function ReservationList() {
  const router = useRouter();
  const { 
    reservations, 
    isLoading, 
    error, 
    fetchReservations, 
    cancelReservation 
  } = useReservationStore();
  
  useEffect(() => {
    fetchReservations();
  }, [fetchReservations]);
  
  const handleViewDetails = (id: string) => {
    router.push(`/reservations/${id}`);
  };
  
  const handleCancelReservation = async (id: string) => {
    if (window.confirm('Are you sure you want to cancel this reservation?')) {
      await cancelReservation(id);
    }
  };
  
  if (isLoading && reservations.length === 0) {
    return (
      <Flex justify="center" align="center" height="200px">
        <Spinner size="xl" />
      </Flex>
    );
  }
  
  if (error) {
    return (
      <Alert status="error" borderRadius="md">
        <AlertIcon />
        {error}
      </Alert>
    );
  }
  
  if (reservations.length === 0) {
    return (
      <Box textAlign="center" py={10}>
        <Text fontSize="lg">No reservations found</Text>
      </Box>
    );
  }
  
  return (
    <VStack spacing={4} align="stretch">
      <Heading size="md">Your Reservations</Heading>
      
      {reservations.map((reservation) => (
        <Box 
          key={reservation.id} 
          p={4} 
          borderWidth="1px" 
          borderRadius="md" 
          boxShadow="sm"
        >
          <Flex justify="space-between" align="center">
            <Box>
              <Text fontWeight="bold">
                {new Date(reservation.date).toLocaleDateString()} at {reservation.time}
              </Text>
              <Text color={
                reservation.status === 'confirmed' ? 'green.500' : 
                reservation.status === 'cancelled' ? 'red.500' : 'orange.500'
              }>
                {reservation.status.charAt(0).toUpperCase() + reservation.status.slice(1)}
              </Text>
            </Box>
            
            <Flex gap={2}>
              <Button 
                size="sm" 
                onClick={() => handleViewDetails(reservation.id)}
              >
                View Details
              </Button>
              
              {reservation.status !== 'cancelled' && (
                <Button 
                  size="sm" 
                  colorScheme="red" 
                  variant="outline"
                  onClick={() => handleCancelReservation(reservation.id)}
                >
                  Cancel
                </Button>
              )}
            </Flex>
          </Flex>
        </Box>
      ))}
    </VStack>
  );
}
