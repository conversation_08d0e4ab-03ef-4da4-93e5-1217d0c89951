import React from 'react';

interface CircleIconProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

const CircleIcon: React.FC<CircleIconProps> = ({
  width = 18,
  height = 18,
  color = '#07945A',
  className,
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 25 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_5271_2024)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12.7148 19C16.5808 19 19.7148 15.866 19.7148 12C19.7148 8.13401 16.5808 5 12.7148 5C8.84885 5 5.71484 8.13401 5.71484 12C5.71484 15.866 8.84885 19 12.7148 19ZM12.7148 23C18.79 23 23.7148 18.0751 23.7148 12C23.7148 5.92487 18.79 1 12.7148 1C6.63971 1 1.71484 5.92487 1.71484 12C1.71484 18.0751 6.63971 23 12.7148 23Z"
          fill={color}
        />
      </g>
      <defs>
        <clipPath id="clip0_5271_2024">
          <rect width="24" height="24" fill="white" transform="translate(0.714844)" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default CircleIcon;
