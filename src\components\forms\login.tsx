import {
  Box,
  Button,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  IconButton,
  Input,
  InputGroup,
  InputRightElement,
  Link,
  Stack,
  Text,
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import Image from 'next/image';
import { useRouter } from 'next/router';
import React, { useState } from 'react';
import LanguageSwitcher from '../ui/language-switcher';
import Proto from '../ui/proto';
import { Error } from '../ui/Error';

interface LoginProps {
  context?: 'admin' | 'client';
}

export default function Login({ context = 'admin' }: LoginProps) {
  const router = useRouter();
  const { t } = useTranslation('auth', { useSuspense: false });

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [err, setErr] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const createAccountUrl =
    context === 'client' ? '/client/membership-registration' : '/create-account';

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Check for admin credentials
    if (email === 'admin' && password === 'admin') {
      // Redirect to admin page
      router.push('/admin');
    } else {
      // Show error for invalid credentials
      setErr(true);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <>
      <Flex
        borderRadius={'20px'}
        w={{ base: '100%', md: '30rem' }}
        justify={'center'}
        gap="4"
        direction="column"
        p="6"
        pb={{ base: 0, md: 6 }}
        bg="white"
      >
        <Flex className="text-center" justify={'center'} direction="column" gap={4}>
          <Flex justifyContent="flex-end" width="100%" mb={2}>
            <LanguageSwitcher />
          </Flex>

          <Text pt={'1rem'} variant={'brand'}>
            JUSTYOYAKU
          </Text>
          <Text fontWeight="bold" fontSize={'3xl'}>
            {t('login.title')}
          </Text>
          <Flex justify={'center'} fontSize={'xs'} fontWeight={'semibold'}>
            <Text>{t('account.dontHaveAccount')}</Text>
            <Link variant="underline" href={createAccountUrl} color="blue">
              {t('account.createAccount')}
            </Link>
          </Flex>
        </Flex>

        <Flex
          className="text-center"
          marginBottom={'2rem'}
          justify={'center'}
          direction="column"
          gap={4}
        >
          <Text textStyle="1xl" fontWeight="bold">
            {t('login.loginWithEmail')}
          </Text>

          <form onSubmit={handleSubmit}>
            <Stack gap="4" align="flex-start">
              <Error err={err ? t('errors.invalidCredentials') : ''} />
              <FormControl>
                <FormLabel>{t('form.emailAddress')}</FormLabel>
                <Input
                  p={'6'}
                  className="border-gray"
                  type="text"
                  value={email}
                  onChange={e => setEmail(e.target.value)}
                />
                <FormErrorMessage> err</FormErrorMessage>
              </FormControl>

              <FormControl>
                <FormLabel>{t('form.password')}</FormLabel>
                <InputGroup>
                  <Input
                    p={'6'}
                    placeholder={t('form.password')}
                    size="sm"
                    type={showPassword ? 'text' : 'password'}
                    id="password"
                    name="password"
                    value={password}
                    onChange={e => setPassword(e.target.value)}
                  />
                  <InputRightElement h="full" w={'4rem'}>
                    <IconButton
                      aria-label={showPassword ? t('form.hidePassword') : t('form.showPassword')}
                      icon={
                        <Image
                          src={
                            showPassword
                              ? '/imgs/icons/hide-password.svg'
                              : '/imgs/icons/show-password.svg'
                          }
                          alt={showPassword ? t('form.hidePassword') : t('form.showPassword')}
                          width={25}
                          height={25}
                        />
                      }
                      variant="ghost"
                      size="sm"
                      onClick={togglePasswordVisibility}
                    />
                  </InputRightElement>
                </InputGroup>
                <FormErrorMessage> err</FormErrorMessage>
              </FormControl>
            </Stack>
            <Button mt={'1.5rem'} variant={'roundedBlue'} type="submit">
              {t('login.title')}
            </Button>
            <Box pt={'1rem'}>
              <Link
                fontSize={'xs'}
                variant="underline"
                href="/reset-password"
                color="linkHighlight"
              >
                {t('account.forgotPassword')}
              </Link>
            </Box>
          </form>
        </Flex>

        <Flex className="text-center " justify={'center'} direction="column">
          <Flex justify={'center'} direction="column" gap={4}>
            <Text fontWeight={'bold'}>他のサービスでログイン</Text>
            {/* <Button py={'1.8rem'} variant={'rounded'}>
              <Image src="/imgs/icons/google.png" alt="Google Logo" width={20} height={20} />{' '}
              <Text fontSize={'md'} ml={3}>
                {t('login.loginWithGoogle')}
              </Text>
            </Button>
            <Button py={'1.8rem'} variant={'rounded'}>
              <Image src="/imgs/icons/Apple.png" alt="Apple Logo" width={20} height={20} />{' '}
              <Text fontSize={'md'} ml={3}>
                {t('login.loginWithApple')}
              </Text>
            </Button> */}
            <Button py={'1.8rem'} variant={'rounded'}>
              <Image src="/imgs/icons/Line.png" alt="LINE Logo" width={20} height={20} />{' '}
              <Text fontSize={'md'} ml={3}>
                {t('login.loginWithLine')}
              </Text>
            </Button>
          </Flex>
        </Flex>
        <Flex
          display={{ base: 'flex', md: 'none' }}
          justifyContent={'center'}
          alignItems={'bottom'}
        >
          <Proto />
        </Flex>
      </Flex>
    </>
  );
}
