import Layout from '@/components/layouts/Layout';
import { SIDEBAR_MENU } from '@/constants/sidebarMenu';
import '@/styles/globals.css';
import { theme } from '@/theme';
import { ChakraProvider } from '@chakra-ui/react';
import '@fontsource/noto-sans-jp';
import { appWithTranslation } from 'next-i18next';
import type { AppProps } from 'next/app';
import Head from 'next/head';
import { useState } from 'react';

type PageProps = {
  layoutType?: 'blank' | 'withSidebar';
};

function App({ Component, pageProps }: AppProps<PageProps>) {
  const [activePage, setActivePage] = useState('reservations');

  // Default to blank layout if not specified
  const layoutType = pageProps.layoutType || 'blank';

  return (
    <>
      <Head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>
      <ChakraProvider theme={theme}>
        <Layout
          type={layoutType}
          navConfig={SIDEBAR_MENU}
          activePage={activePage}
          setActivePage={setActivePage}
        >
          <Component {...pageProps} activePage={activePage} setActivePage={setActivePage} />
        </Layout>
      </ChakraProvider>
    </>
  );
}

export default appWithTranslation(App);
