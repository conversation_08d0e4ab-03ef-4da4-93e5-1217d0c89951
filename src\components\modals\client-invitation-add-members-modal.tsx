import CirclePlusIcon from '@/assets/icons/circle-plus';
import {
  Button,
  Flex,
  FormControl,
  FormLabel,
  Input,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  <PERSON>dal<PERSON>ooter,
  Modal<PERSON>eader,
  ModalOverlay,
  Text,
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { useRef, useState } from 'react';

export default function ClientInvitationAddMembersModal({
  variant,
  isInvitationOpen,
  onInvitationClose,
}: {
  variant?: string | 'single';
  isInvitationOpen: boolean;
  onInvitationClose: () => void;
}) {
  const { t } = useTranslation('profile');
  const [invitationStep, setInvitationStep] = useState(0);
  const [invites, setInvites] = useState(1);
  const maxInvites = 3;
  const [inviteDisabled, setInviteDisabled] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const emailInputsRef = useRef<(HTMLInputElement | null)[]>([]);

  // Email sending function
  const sendInvitationEmails = async (): Promise<boolean> => {
    try {
      setIsLoading(true);

      // Collect email addresses from input fields
      const emailAddresses = emailInputsRef.current
        .slice(0, invites)
        .map(input => input?.value?.trim())
        .filter(email => email && email.length > 0);

      // Basic email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const validEmails = emailAddresses.filter(
        (email): email is string => typeof email === 'string' && emailRegex.test(email)
      );

      if (validEmails.length === 0) {
        return false;
      }

      // API call to send invitation emails
      const response = await fetch('/api/invitations/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          emails: validEmails,
          inviterName: 'Current User',
        }),
      });

      return response.ok;
    } catch (error) {
      console.error('Error sending invitation emails:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isInvitationOpen}
      onClose={() => {
        setInvites(1);
        setInvitationStep(0);
        onInvitationClose();
      }}
    >
      <form>
        <ModalOverlay />
        <ModalContent borderRadius={'1rem'} pb={'.5rem'}>
          <ModalHeader>
            {invitationStep === 0 && (
              <Text variant={'header'} pt={'1rem'} pb={'.5rem'}>
                {t('navigation.inviteMembers')}
              </Text>
            )}
            {invitationStep > 0 && (
              <Text textAlign={'center'} variant={'brand'} pt={'1rem'} pb={'.5rem'}>
                JUSTYOYAKU
              </Text>
            )}
          </ModalHeader>
          <ModalCloseButton />
          {invitationStep === 0 && (
            <ModalBody display={'flex'} flexDirection={'column'} gap={'1rem'}>
              {inviteDisabled && <Text variant={'error'}>{t('members.noMoreAdditions')}</Text>}

              <FormControl display={'flex'} flexDirection={'column'} gap={'1rem'}>
                <FormLabel>
                  <Text fontWeight={'normal'}>{t('members.emailAddress')}</Text>
                </FormLabel>
                {Array.from({ length: invites }, (_, index) => (
                  <Input
                    key={index}
                    ref={el => (emailInputsRef.current[index] = el)}
                    p={'6'}
                    className="border-gray"
                    type="email"
                    placeholder="<EMAIL>"
                    isDisabled={isLoading}
                  />
                ))}
              </FormControl>

              {invitationStep === 0 && (
                <Button
                  w={'100%'}
                  variant={'normal'}
                  isDisabled={isLoading}
                  onClick={() => {
                    if (invites < maxInvites) {
                      setInvites(invites + 1);
                    } else {
                      setInviteDisabled(true);
                    }
                  }}
                >
                  <Flex py={'.6rem'} alignItems={'center'} justifyContent={'center'} gap={'1rem'}>
                    <Text
                      textDecoration={'underline'}
                      color={inviteDisabled ? 'disabledBlue' : 'mainColor'}
                      fontWeight={'bold'}
                    >
                      {t('members.addMember')}
                    </Text>
                    <CirclePlusIcon
                      width={25}
                      height={25}
                      color={inviteDisabled ? '#5C6ED3' : '#2B42CA'}
                    />
                  </Flex>
                </Button>
              )}
            </ModalBody>
          )}

          {invitationStep === 1 && (
            <ModalBody display={'flex'} flexDirection={'column'} gap={'1rem'}>
              <Text textAlign={'center'} fontWeight={'bold'} variant={'subHeaderMedium'}>
                {t('members.emailSent')}
              </Text>
            </ModalBody>
          )}

          {invitationStep === 2 && (
            <ModalBody display={'flex'} flexDirection={'column'}>
              <Text textAlign={'center'} fontWeight={'bold'} variant={'subHeaderMedium'}>
                {t('members.emailSendFailed')}
              </Text>
            </ModalBody>
          )}
          <ModalFooter gap={4} justifyContent={'center'}>
            {invitationStep === 0 && (
              <Button
                px={'3rem'}
                w={'100%'}
                variant="roundedBlue"
                isLoading={isLoading}
                loadingText={t('actions.send')}
                onClick={async () => {
                  const emailSent = await sendInvitationEmails();
                  if (emailSent) {
                    setInvitationStep(1); // Success state
                  } else {
                    setInvitationStep(2); // Error/failure state
                  }
                }}
              >
                <span>{t('actions.send')}</span>
              </Button>
            )}

            {invitationStep === 1 && (
              <Button
                px={'3rem'}
                w={'100%'}
                variant="rounded"
                bg={'gray.100'}
                borderColor={'transparent'}
                onClick={() => {
                  onInvitationClose();
                  setInvitationStep(0);
                  setInvites(1);
                }}
              >
                <span>{t('actions.close')}</span>
              </Button>
            )}

            {invitationStep === 2 && (
              <Button
                px={'3rem'}
                w={'100%'}
                variant="roundedBlue"
                onClick={() => {
                  onInvitationClose();
                  setInvitationStep(0);
                  setInvites(1);
                }}
              >
                <span>{t('actions.back')}</span>
              </Button>
            )}
          </ModalFooter>
        </ModalContent>
      </form>
    </Modal>
  );
}
