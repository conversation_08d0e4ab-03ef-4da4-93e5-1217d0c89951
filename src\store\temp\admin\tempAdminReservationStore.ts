import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { sampleAdminReservationsList } from '@/.temp/sampleAdminReservationsList';
import { AdminReservationType } from '@/types/adminReservationsTypes';

type Reservations = {
  reservations: AdminReservationType[];
};

export const useTempReservationStore = create<Reservations>(set => ({
  reservations: sampleAdminReservationsList,
}));
