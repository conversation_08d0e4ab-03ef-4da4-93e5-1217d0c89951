import RadioSelectWithTwoOptions from '@/components/ui/formfields/radio-select-two-options';
import TimeSelect from '@/components/ui/formfields/time-select';
import { ToggleButton } from '@/components/ui/formfields/toggle-button';
import StoreBanner from '@/components/ui/store-banner';
import { Button, Flex, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Image from 'next/image';
import React from 'react';

export default function StoreOptionPage() {
  const { t } = useTranslation('admin');

  // Day name mapping for translation
  const getDayTranslationKey = (dayKey: string): string => {
    const mapping: Record<string, string> = {
      monday: 'monday',
      tuesday: 'tuesday',
      wednesday: 'wednesday',
      thursday: 'thursday',
      friday: 'friday',
      saturday: 'saturday',
      sunday: 'sunday',
    };
    return mapping[dayKey] || dayKey;
  };

  const [formData, setFormData] = React.useState({
    availability: {
      days: [
        {
          monday: false,
        },
        {
          tuesday: false,
        },
        {
          wednesday: false,
        },
        {
          thursday: false,
        },
        {
          friday: false,
        },
        {
          saturday: false,
        },
        {
          sunday: false,
        },
      ],
      timeSlots: {
        monday: [{}],
        tuesday: [{}],
        wednesday: [{}],
        thursday: [{}],
        friday: [{}],
        saturday: [{}],
        sunday: [{}],
      },
    },
  });

  const addTimeSlot = (day: keyof typeof formData.availability.timeSlots) => {
    setFormData({
      ...formData,
      availability: {
        ...formData.availability,
        timeSlots: {
          ...formData.availability.timeSlots,
          [day]: [...formData.availability.timeSlots[day], {}],
        },
      },
    });
  };

  const removeTimeSlot = (day: keyof typeof formData.availability.timeSlots, index: number) => {
    setFormData({
      ...formData,
      availability: {
        ...formData.availability,
        timeSlots: {
          ...formData.availability.timeSlots,
          [day]: formData.availability.timeSlots[day].filter((_, i) => i !== index),
        },
      },
    });
  };

  return (
    <Flex
      w={'100%'}
      flexDirection={'column'}
      pr={{ base: 0, md: 6 }}
      pl={{ base: 0, md: 6, lg: 0 }}
    >
      <Flex
        bg={{ base: 'white', md: 'unset' }}
        px={{ base: 5, md: 0 }}
        justifyContent={'space-between'}
        alignItems={'center'}
        height={'5rem'}
        w={'100%'}
      >
        <Text fontSize={'lg'} fontWeight={'bold'}>
          {t('storeOption.pageTitle')}
        </Text>
      </Flex>

      <Flex flexDirection={'column'} bg={'white'} gap={4} p={5} borderRadius={'1rem'}>
        <Flex justifyContent={{ base: 'center', sm: 'space-between' }}>
          <StoreBanner />
        </Flex>

        <Flex px={'1rem'} py={'2rem'} flexDirection={'column'}>
          <Flex
            flexDirection={'column'}
            gap={2}
            borderBottom={'1px solid #E5EAF1'}
            pb={'1rem'}
            mb={'1rem'}
          >
            <Text fontSize={'md'} fontWeight={'semibold'}>
              {t('storeOption.businessHours.title')}
            </Text>

            {formData.availability.days.map((dayObj, dayIndex) => {
              const dayKey = Object.keys(dayObj)[0] as keyof typeof dayObj;
              const isActive = dayObj[dayKey] ?? false;

              return (
                <Flex flexWrap={'wrap'} key={`day-${dayIndex}`} alignItems={'center'} mb={4}>
                  <Flex>
                    <ToggleButton
                      isActive={isActive}
                      onChange={() =>
                        setFormData({
                          ...formData,
                          availability: {
                            ...formData.availability,
                            days: formData.availability.days.map((d, i) => {
                              if (i === dayIndex) {
                                const updatedDay = { ...d };

                                updatedDay[dayKey] = !d[dayKey];
                                return updatedDay;
                              }
                              return d;
                            }),
                          },
                        })
                      }
                    />
                    <Text ml={'.6rem'} mr={'3rem'} fontSize={'lg'} fontWeight={'semibold'}>
                      {t(`storeOption.businessHours.dayNames.${getDayTranslationKey(dayKey)}`)}
                    </Text>
                  </Flex>

                  <Flex w={{ base: '100%', sm: 'auto' }} flexDirection={'column'} gap={'.5rem'}>
                    {isActive ? (
                      formData.availability.timeSlots[dayKey].map((_, slotIndex) => (
                        <Flex
                          mt={{ base: '1rem', md: 0 }}
                          w={'100%'}
                          key={`${dayKey}-${slotIndex}`}
                          gap={4}
                          alignItems="center"
                        >
                          <TimeSelect width={{ base: '100%', sm: '12rem' }} />

                          {slotIndex === 0 ? (
                            <Button
                              size="sm"
                              ml={{ base: 0, sm: 2 }}
                              p={1}
                              variant="ghost"
                              onClick={() => addTimeSlot(dayKey)}
                            >
                              <Image
                                src="/imgs/icons/plus.svg"
                                alt={t('storeOption.businessHours.buttons.addTimeSlot')}
                                width={18}
                                height={18}
                              />
                            </Button>
                          ) : (
                            <Button
                              size="sm"
                              ml={{ base: 0, sm: 2 }}
                              p={1}
                              variant="ghost"
                              onClick={() => removeTimeSlot(dayKey, slotIndex)}
                            >
                              <Image
                                src="/imgs/icons/trash.svg"
                                alt={t('storeOption.businessHours.buttons.removeTimeSlot')}
                                width={18}
                                height={18}
                              />
                            </Button>
                          )}
                        </Flex>
                      ))
                    ) : (
                      <Text>{t('storeOption.businessHours.closedDay')}</Text>
                    )}
                  </Flex>
                </Flex>
              );
            })}
          </Flex>

          <RadioSelectWithTwoOptions
            head={t('storeOption.operationMethod.title')}
            subHead={t('storeOption.operationMethod.description')}
            option1={t('storeOption.operationMethod.staffed')}
            option2={t('storeOption.operationMethod.unstaffed')}
          />
          <RadioSelectWithTwoOptions
            head={t('storeOption.paymentMethod.title')}
            subHead={t('storeOption.paymentMethod.description')}
            option1={t('storeOption.paymentMethod.creditCharge')}
            option2={t('storeOption.paymentMethod.onlinePayment')}
          />
          <RadioSelectWithTwoOptions
            head={t('storeOption.selectionMethod.title')}
            subHead={t('storeOption.selectionMethod.description')}
            option1={t('storeOption.selectionMethod.optionFirst')}
            option2={t('storeOption.selectionMethod.calendarFirst')}
          />
          <RadioSelectWithTwoOptions
            head={t('storeOption.reservationAcceptance.title')}
            subHead={t('storeOption.reservationAcceptance.description')}
            option1={t('storeOption.reservationAcceptance.manual')}
            option2={t('storeOption.reservationAcceptance.automatic')}
          />

          <Flex justifyContent={'center'}>
            <Button variant={'roundedBlue'} px={'3rem'} w={'auto'}>
              {t('storeOption.buttons.complete')}
            </Button>
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'admin'])),
      layoutType: 'withSidebar',
    },
  };
}
