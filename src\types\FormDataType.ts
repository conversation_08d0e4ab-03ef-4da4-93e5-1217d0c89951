import { DayKeyType } from './dayType';
import { TimeSlot } from './TimeSlot';

export type FormDataType = {
  profileIcon: string;
  username: string;
  groupname: string;
  storeID: string;
  storeOverview: string;
  availability: {
    [key in DayKeyType]: boolean;
  } & {
    timeSlots: {
      [key in DayKeyType]: TimeSlot[];
    };
  };
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
}