/**
 * Maps Japanese day names to translation keys for internationalization
 */
export const DAY_NAME_MAPPING: Record<string, string> = {
  '月曜日': 'monday',
  '火曜日': 'tuesday', 
  '水曜日': 'wednesday',
  '木曜日': 'thursday',
  '金曜日': 'friday',
  '土曜日': 'saturday',
  '日曜日': 'sunday'
};

/**
 * Gets the translation key for a Japanese day name
 * @param japaneseDayName - The Japanese day name (e.g., '月曜日')
 * @returns The translation key (e.g., 'monday')
 */
export const getDayTranslationKey = (japaneseDayName: string): string => {
  return DAY_NAME_MAPPING[japaneseDayName] || japaneseDayName;
};
