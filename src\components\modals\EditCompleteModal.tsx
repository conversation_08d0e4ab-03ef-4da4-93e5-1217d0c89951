import AssignmentIcon from '@/assets/icons/assignment';
import {
  Button,
  Flex,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  Modal<PERSON>ooter,
  ModalOverlay,
  Text,
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';

type EditCompleteModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onEditCompleteClose: () => void;
  setEditMode: (value: boolean) => void;
};

export default function EditCompleteModal({
  isOpen,
  onClose,
  onEditCompleteClose,
  setEditMode,
}: EditCompleteModalProps) {
  const { t } = useTranslation('admin');
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent>
        <Flex justifyContent={'center'} alignItems={'center'} h={'5rem'}>
          <AssignmentIcon width={30} height={30} color="black" />
        </Flex>
        <ModalCloseButton />
        <ModalBody fontSize={'sm'} textAlign={'center'}>
          <Text mb={4} fontSize={'md'} fontWeight={'bold'}>
            {t('memberDetails.modal.editComplete')}
          </Text>
        </ModalBody>
        <ModalFooter gap={4} justifyContent={'center'}>
          <Button
            px={'3rem'}
            w={'auto'}
            variant="roundedBlue"
            onClick={() => {
              onEditCompleteClose();
              setEditMode(false);
            }}
          >
            {t('memberDetails.buttons.close')}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
