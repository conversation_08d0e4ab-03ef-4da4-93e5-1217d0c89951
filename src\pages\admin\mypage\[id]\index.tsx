import BackIcon from '@/assets/icons/back';
import LineIcon from '@/assets/icons/lineIcon';
import PenIcon from '@/assets/icons/pen';
import InputTextEditMode from '@/components/ui/formfields/InputTextEditMode';
import ProfileIcon from '@/components/ui/profile-icon';
import { Button, Flex, Text } from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import router from 'next/router';
import React from 'react';

function Mypage() {
  const { t } = useTranslation('admin');
  const [editMode, setEditMode] = React.useState(false);
  // TODO: Replace with actual user account linking status from API/store
  // These values should come from user's profile data
  const accountLinkingStatus = {
    line: false,
  };

  return (
    <Flex w={'100%'} flexDirection={'column'} pr={6}>
      <Flex alignItems={'center'} height={'5rem'} w={'100%'}>
        <Button
          variant={'ghost'}
          w={'auto'}
          p={0}
          m={0}
          onClick={() => {
            router.back();
          }}
        >
          <BackIcon width={30} height={30} color="black" className="my-icon-class" />
        </Button>
        <Text fontSize={'lg'} fontWeight={'bold'}>
          {t('memberDetails.header.title')}
        </Text>
      </Flex>

      <Flex flexDirection={'column'} bg={'white'} gap={4} p={5} borderRadius={'1rem'}>
        {editMode === false && (
          <Flex justifyContent={'flex-end'} alignItems={'center'} gap={'1rem'}>
            <Button
              variant={'roundedBlue'}
              fontSize={'sm'}
              fontWeight={'normal'}
              gap={2}
              p={'1.2rem'}
              w={'auto'}
              onClick={() => {
                setEditMode(!editMode);
              }}
            >
              <PenIcon width={14} height={14} color="white" className="my-icon-class" />
              {t('memberDetails.buttons.edit')}
            </Button>
          </Flex>
        )}

        <Flex px={'1rem'} flexDirection={'column'}>
          <Flex justifyContent={'center'}>
            <Flex w={'30rem'}>
              <ProfileIcon />
            </Flex>
          </Flex>

          <InputTextEditMode
            editMode={editMode}
            label={t('memberDetails.form.email')}
            value={'<EMAIL>'}
          />
          <InputTextEditMode
            editMode={editMode}
            label={t('memberDetails.form.password')}
            value={'●●●●●●'}
          />
          <InputTextEditMode
            editMode={editMode}
            label={t('memberDetails.form.contact')}
            value={'080-1234-1234'}
          />
          <InputTextEditMode
            editMode={editMode}
            label={t('memberDetails.form.name')}
            value={'斉藤　翼'}
          />
          <InputTextEditMode
            editMode={editMode}
            label={t('memberDetails.form.nameKana')}
            value={'サイトウ ツバサ'}
          />

          <Flex
            flexDirection={'column'}
            gap={2}
            borderBottom={'1px solid #E5EAF1'}
            pb={'1rem'}
            mb={'1rem'}
          >
            <Text fontSize={'md'} fontWeight={'semibold'}>
              {t('memberDetails.sns.title')}
            </Text>
            <Flex gap={'1rem'} alignItems={'center'}>
              <Button
                variant={'rounded'}
                fontSize={'sm'}
                fontWeight={'normal'}
                gap={2}
                display={'flex'}
                justifyContent={'space-between'}
                py={'1.5rem'}
              >
                <Flex fontWeight={'bold'} gap={'1rem'} fontSize={'md'} alignItems={'center'}>
                  <LineIcon width={20} height={20} />
                  {t('memberDetails.sns.lineRegister')}
                </Flex>
                <Text color={'mainColor'}>
                  {accountLinkingStatus.line
                    ? t('memberDetails.sns.completed')
                    : t('memberDetails.sns.link')}
                </Text>
              </Button>
            </Flex>
          </Flex>
        </Flex>

        {editMode && (
          <Flex justifyContent={'center'} alignItems={'center'} gap={'1rem'}>
            <Button
              variant={'roundedBlue'}
              fontSize={'sm'}
              fontWeight={'normal'}
              gap={2}
              p={'1.2rem'}
              w={'auto'}
              onClick={() => {
                setEditMode(!editMode);
              }}
            >
              <PenIcon width={14} height={14} color="white" className="my-icon-class" />
              完了
            </Button>
          </Flex>
        )}
      </Flex>
    </Flex>
  );
}

export async function getStaticProps({ locale }: { locale: string }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common', 'auth', 'admin'])),
      layoutType: 'withSidebar',
    },
  };
}

export async function getStaticPaths() {
  return {
    paths: [],
    fallback: 'blocking',
  };
}

export default Mypage;
