import AssignmentIcon from '@/assets/icons/assignment';
import DropDownIcon from '@/assets/icons/dropdown';
import {
  Button,
  Flex,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  Modal<PERSON>ooter,
  ModalOverlay,
  Text,
} from '@chakra-ui/react';
import { useTranslation } from 'next-i18next';

type ChangeChargeMemberDetailsProps = {
  isOpen: boolean;
  onClose: () => void;
  modalStep: number;
  setModalStep: (value: number) => void;
};

export default function ChangeChargeMemberDetails({
  isOpen,
  onClose,
  modalStep,
  setModalStep,
}: ChangeChargeMemberDetailsProps) {
  const { t } = useTranslation('admin');
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      {modalStep === 1 ? (
        <ModalContent>
          <Flex justifyContent={'center'} alignItems={'center'} h={'3rem'}></Flex>
          <ModalCloseButton />
          <ModalBody fontSize={'sm'}>
            <Text textAlign={'center'} mb={4} fontSize={'md'} fontWeight={'bold'}>
              {t('memberDetails.charge.modal.title')}
            </Text>
            <Flex flexDirection={'column'}>
              <Text fontSize={'sm'} color={'black'} fontWeight={'semibold'}>
                {t('memberDetails.charge.form.remaining')}
              </Text>
              <Text fontSize={'md'} color={'black'} fontWeight={'semibold'}>
                num credit
              </Text>
            </Flex>
            <Flex flexDirection={'column'} mt={'2rem'}>
              <Text fontSize={'sm'} color={'black'}>
                {t('memberDetails.charge.form.chargeAmount')}
              </Text>
              <Menu>
                <MenuButton
                  as={Button}
                  fontSize={'2xs'}
                  textAlign={'left'}
                  border={'1px solid #E5EAF1'}
                  bg={'white'}
                  _hover={{ bg: 'white' }}
                >
                  <Flex
                    alignItems={'center'}
                    justifyContent={'space-between'}
                    fontSize={'md'}
                    fontWeight={'normal'}
                  >
                    num credit
                    <DropDownIcon />
                  </Flex>
                </MenuButton>
                <MenuList w={'25rem'}>
                  <MenuItem fontSize="xs">1000</MenuItem>
                  <MenuItem fontSize="xs">3000</MenuItem>
                  <MenuItem fontSize="xs">5000</MenuItem>
                  <MenuItem fontSize="xs">10000</MenuItem>
                </MenuList>
              </Menu>
            </Flex>
          </ModalBody>
          <ModalFooter gap={4} justifyContent={'center'}>
            <Button px={'3rem'} w={'auto'} variant="roundedBlue" onClick={onClose}>
              {t('memberDetails.charge.buttons.charge')}
            </Button>
          </ModalFooter>
        </ModalContent>
      ) : (
        <ModalContent>
          <Flex justifyContent={'center'} alignItems={'center'} h={'5rem'}>
            <AssignmentIcon width={30} height={30} color="black" />
          </Flex>
          <ModalCloseButton />
          <ModalBody fontSize={'sm'} textAlign={'center'}>
            <Text mb={4} fontSize={'md'} fontWeight={'bold'}>
              {t('memberDetails.charge.modal.chargeComplete')}
            </Text>
          </ModalBody>
          <ModalFooter gap={4} justifyContent={'center'}>
            <Button px={'3rem'} w={'auto'} variant="roundedBlue" onClick={onClose}>
              {t('memberDetails.buttons.close')}
            </Button>
          </ModalFooter>
        </ModalContent>
      )}
    </Modal>
  );
}
