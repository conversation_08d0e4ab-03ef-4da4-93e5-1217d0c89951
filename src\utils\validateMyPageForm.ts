import { ClientDataType } from '@/types/clientDataType';

type MyPageData = {
  clientData: ClientDataType;
};

export const validateForm = ( 
  data: MyPageData,
  setErrors: (value: any) => void,
  
) => {
    const newErrors: Record<string, string> = {};
    let isValid = true;

    if (!data.clientData.fullName.trim()) {
      newErrors.fullName = '氏名を入力してください';
      isValid = false;
    }

    if (!data.clientData.kana.trim()) {
      newErrors.kana = 'カナを入力してください';
      isValid = false;
    }

    if (!data.clientData.phoneNumber.trim()) {
      newErrors.phoneNumber = '電話番号を入力してください';
      isValid = false;
    } else if (!/^[0-9]{10,11}$/.test(data.clientData.phoneNumber.replace(/[-\s]/g, ''))) {
      newErrors.phoneNumber = '有効な電話番号を入力してください';
      isValid = false;
    }

    if (!data.clientData.email.trim()) {
      newErrors.email = 'メールアドレスを入力してください';
      isValid = false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.clientData.email)) {
      newErrors.email = '有効なメールアドレスを入力してください';
      isValid = false;
    }
    
    setErrors(newErrors);
    return isValid;
  };
