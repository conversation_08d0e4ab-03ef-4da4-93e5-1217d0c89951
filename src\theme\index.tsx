import { colors } from '@/theme/colors';
import { extendTheme } from '@chakra-ui/react';
import { buttons } from './buttons';
import { text } from './text';
import { inputs } from './inputs';
import { links } from './links';

// Custom breakpoints
const breakpoints = {
  xs: '320px',
  sm: '480px',
  md: '768px',
  lg: '992px',
  xl: '1200px',
  '2xl': '1536px',
};

export const theme = extendTheme({
  breakpoints,
  fonts: {
    heading: '"Noto Sans JP", sans-serif',
    body: '"Noto Sans JP", sans-serif',
  },
  colors,

  components: {
    Button: {
      variants: {
        rounded: buttons.rounded,
        roundedNoBorder: buttons.roundedNoBorder,
        roundedBlue: buttons.roundedBlue,
        RoundedOutline: buttons.RoundedOutline,
        SquaredHighlight: buttons.SquaredHighlight,
        card: buttons.card,
        blue: buttons.blue,
      }
    },
    Text: {
      baseStyle: {
        color: '#334155',
        letterSpacing: '-.06em',
      },
      variants: {
        brand: text.brand,
        underline: text.underline,
        header: text.header,
        subHeader: text.subHeader,
        subHeaderLarge: text.subHeaderLarge,
        subHeaderXL: text.subHeaderXL,
        subHeaderNormal: text.subHeaderNormal,
        subHeaderSmall: text.subHeaderSmall,
        subHeaderXSmall: text.subHeaderXSmall,
        error: text.error,
        errorText: text.errorText,
        category: text.category,
        categoryGray: text.categoryGray,
      },
    },
    Link: {
      baseStyle: {
        color: 'blue',
        textDecoration: 'underline',
      },
      variants: {
        default: links.default,
        underlined: links.underlined,
        subtle: links.subtle,
        button: links.button,
        nav: links.nav,
        blue: links.blue,
      },
    },
    Input: {
      variants: {
        default: inputs.default,
        outline: inputs.outline,
        filled: inputs.filled,
        rounded: inputs.rounded,
      },
    },
    FormLabel: {
      baseStyle: {
        fontSize: 'sm',
        fontWeight: 'medium',
        mb: 2,
      },
      variants: {
        required: {
          position: 'relative',
          _after: {
            content: '"*"',
            color: 'alert',
            position: 'absolute',
            marginLeft: '1',
          },
        },
      },
    },
  },
});

export default theme;
