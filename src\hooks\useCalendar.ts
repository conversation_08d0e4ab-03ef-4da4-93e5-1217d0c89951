import { useState, useCallback, useMemo } from 'react';
import {
  normalizeDate,
  isDateInList,
  isDateBefore,
  isDateAfter,
  getPreviousMonthDate,
  getNextMonthDate,
} from '@/utils/dateUtils';

interface UseCalendarProps {
  onDateSelect?: (date: Date) => void;
  highlightedDates?: Date[];
  disabledDates?: Date[];
  minDate?: Date;
  maxDate?: Date;
  initialDate?: Date;
}

interface UseCalendarReturn {
  currentDate: Date;
  selectedDate: Date | null;
  prevMonth: () => void;
  nextMonth: () => void;
  handleDateClick: (date: Date) => void;
  isHighlighted: (date: Date) => boolean;
  isDisabled: (date: Date) => boolean;
  isToday: (date: Date) => boolean;
  isSelected: (date: Date) => boolean;
}

/**
 * Custom hook for calendar functionality
 */
export const useCalendar = ({
  onDateSelect,
  highlightedDates = [],
  disabledDates = [],
  minDate,
  maxDate,
  initialDate,
}: UseCalendarProps = {}): UseCalendarReturn => {
  // Initialize with today's date if no initialDate is provided
  const [currentDate, setCurrentDate] = useState<Date>(initialDate || new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(initialDate || new Date());

  // Memoize today's date to avoid recreating it on every render
  const today = useMemo(() => new Date(), []);

  /**
   * Navigate to the previous month
   */
  const prevMonth = useCallback(() => {
    setCurrentDate(getPreviousMonthDate(currentDate));
  }, [currentDate]);

  /**
   * Navigate to the next month
   */
  const nextMonth = useCallback(() => {
    setCurrentDate(getNextMonthDate(currentDate));
  }, [currentDate]);

  /**
   * Check if a date is highlighted
   */
  const isHighlighted = useCallback(
    (date: Date): boolean => {
      return isDateInList(date, highlightedDates);
    },
    [highlightedDates]
  );

  /**
   * Check if a date is disabled
   */
  const isDisabled = useCallback(
    (date: Date): boolean => {
      // Check if date is in disabled dates list
      if (isDateInList(date, disabledDates)) {
        return true;
      }

      // Check if date is before min date
      if (minDate && isDateBefore(date, minDate)) {
        return true;
      }

      // Check if date is after max date
      if (maxDate && isDateAfter(date, maxDate)) {
        return true;
      }

      return false;
    },
    [disabledDates, minDate, maxDate]
  );

  /**
   * Check if a date is today
   */
  const isToday = useCallback(
    (date: Date): boolean => {
      return normalizeDate(date).getTime() === normalizeDate(today).getTime();
    },
    [today]
  );

  /**
   * Check if a date is selected
   */
  const isSelected = useCallback(
    (date: Date): boolean => {
      if (selectedDate === null) return false;
      return normalizeDate(date).getTime() === normalizeDate(selectedDate).getTime();
    },
    [selectedDate]
  );

  /**
   * Handle date click
   */
  const handleDateClick = useCallback(
    (date: Date) => {
      if (!isDisabled(date)) {
        setSelectedDate(date);
        if (onDateSelect) {
          onDateSelect(date);
        }
      }
    },
    [isDisabled, onDateSelect]
  );

  return {
    currentDate,
    selectedDate,
    prevMonth,
    nextMonth,
    handleDateClick,
    isHighlighted,
    isDisabled,
    isToday,
    isSelected,
  };
};
