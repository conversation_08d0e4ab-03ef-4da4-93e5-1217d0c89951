/**
 * Store data types for dynamic store management
 */

export interface Store {
  id: string;
  name: string;
  description?: string;
  imageUrl?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface StoreListProps {
  stores: Store[];
  currentStoreId?: string;
  onStoreSelect: (storeId: string) => void;
  onAddStore: () => void;
}

export interface StoreMenuProps {
  stores: Store[];
  currentStore?: Store;
  onStoreChange: (store: Store) => void;
  onAddStore: () => void;
}

// Mock data for development - this would come from API/database in production
export const mockStores: Store[] = [
  {
    id: '1',
    name: 'やきにくや YAKINIKU-YA 麻布十番店',
    description: '本格焼肉レストラン',
    imageUrl: '/imgs/icons/tempProfile.png',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: '2', 
    name: 'シェアスペース大手町',
    description: 'コワーキングスペース',
    imageUrl: '/imgs/icons/tempProfile.png',
    isActive: true,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15'),
  },
  {
    id: '3',
    name: 'カフェ・ド・プロト',
    description: 'フレンチカフェ',
    imageUrl: '/imgs/icons/tempProfile.png',
    isActive: true,
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date('2024-02-01'),
  },
];
