export const inputs = {
  default: {
    field: {
      borderRadius: '8px',
      padding: '1.5rem',
      fontSize: '1rem',
      border: '1px solid red',
      transition: 'all 0.3s ease',
      _hover: {
        borderColor: '#2B42CA',
      },
      _focus: {
        borderColor: '#2B42CA',
        boxShadow: '0 0 0 1px #2B42CA',
      },
      _disabled: {
        backgroundColor: '#f8f9fa',
        opacity: 0.7,
      },
      _invalid: {
        borderColor: '#ba1b1d',
        boxShadow: '0 0 0 1px #ba1b1d',
      },
    },
  },

  outline: {
    field: {
      borderRadius: '8px',
      padding: '1.5rem',
      fontSize: '1rem',
      border: '1px solid #e2e8f0',
      transition: 'all 0.3s ease',
      _hover: {
        borderColor: '#2B42CA',
      },
      _focus: {
        borderColor: '#2B42CA',
        boxShadow: '0 0 0 1px #2B42CA',
      },
      _disabled: {
        backgroundColor: '#f8f9fa',
        opacity: 0.7,
      },
      _invalid: {
        borderColor: '#ba1b1d',
        boxShadow: '0 0 0 1px #ba1b1d',
      },
    },
  },
  filled: {
    field: {
      borderRadius: '8px',
      padding: '1.5rem',
      fontSize: '1rem',
      backgroundColor: '#f0f4fe',
      border: '1px solid transparent',
      transition: 'all 0.3s ease',
      _hover: {
        backgroundColor: '#e6ebfd',
      },
      _focus: {
        backgroundColor: '#ffffff',
        borderColor: '#2B42CA',
        boxShadow: '0 0 0 1px #2B42CA',
      },
      _disabled: {
        backgroundColor: '#f8f9fa',
        opacity: 0.7,
      },
      _invalid: {
        borderColor: '#ba1b1d',
        boxShadow: '0 0 0 1px #ba1b1d',
      },
    },
  },
  rounded: {
    field: {
      borderRadius: '50px',
      padding: '1.5rem',
      fontSize: '1rem',
      border: '1px solid #e2e8f0',
      transition: 'all 0.3s ease',
      _hover: {
        borderColor: '#2B42CA',
      },
      _focus: {
        borderColor: '#2B42CA',
        boxShadow: '0 0 0 1px #2B42CA',
      },
    },
  },
};
