import React from 'react';

interface addImageIconIconProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

const AddImage: React.FC<addImageIconIconProps> = ({
  width = 18,
  height = 18,
  color = '#1E293B',
  className,
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask
        id="mask0_4001_17911"
        style={{ maskType: 'alpha' }}
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="24"
        height="24"
      >
        <rect width="24" height="24" fill="#D9D9D9" />
      </mask>
      <g mask="url(#mask0_4001_17911)">
        <path
          d="M5 21C4.45 21 3.97917 20.8042 3.5875 20.4125C3.19583 20.0208 3 19.55 3 19V5C3 4.45 3.19583 3.97917 3.5875 3.5875C3.97917 3.19583 4.45 3 5 3H12C12.2833 3 12.5208 3.09583 12.7125 3.2875C12.9042 3.47917 13 3.71667 13 4C13 4.28333 12.9042 4.52083 12.7125 4.7125C12.5208 4.90417 12.2833 5 12 5H5V19H19V12C19 11.7167 19.0958 11.4792 19.2875 11.2875C19.4792 11.0958 19.7167 11 20 11C20.2833 11 20.5208 11.0958 20.7125 11.2875C20.9042 11.4792 21 11.7167 21 12V19C21 19.55 20.8042 20.0208 20.4125 20.4125C20.0208 20.8042 19.55 21 19 21H5ZM6 17H18L14.25 12L11.25 16L9 13L6 17ZM17 7H16C15.7167 7 15.4792 6.90417 15.2875 6.7125C15.0958 6.52083 15 6.28333 15 6C15 5.71667 15.0958 5.47917 15.2875 5.2875C15.4792 5.09583 15.7167 5 16 5H17V4C17 3.71667 17.0958 3.47917 17.2875 3.2875C17.4792 3.09583 17.7167 3 18 3C18.2833 3 18.5208 3.09583 18.7125 3.2875C18.9042 3.47917 19 3.71667 19 4V5H20C20.2833 5 20.5208 5.09583 20.7125 5.2875C20.9042 5.47917 21 5.71667 21 6C21 6.28333 20.9042 6.52083 20.7125 6.7125C20.5208 6.90417 20.2833 7 20 7H19V8C19 8.28333 18.9042 8.52083 18.7125 8.7125C18.5208 8.90417 18.2833 9 18 9C17.7167 9 17.4792 8.90417 17.2875 8.7125C17.0958 8.52083 17 8.28333 17 8V7Z"
          fill="#1E293B"
        />
      </g>
    </svg>
  );
};

export default AddImage;
