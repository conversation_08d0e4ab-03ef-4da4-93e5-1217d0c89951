import React from 'react';

interface checkCircleIconProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

const CheckCircleIcon: React.FC<checkCircleIconProps> = ({
  width = 72,
  height = 72,
  color = '#2B42CA',
  className,
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 72 72"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask
        id="mask0_4071_25189"
        style={{ maskType: 'alpha' }}
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width={width}
        height={height}
      >
        <rect width={width} height={height} fill={color} />
      </mask>
      <g mask="url(#mask0_4071_25189)">
        <path
          d="M31.8 41.4L25.35 34.95C24.8 34.4 24.1 34.125 23.25 34.125C22.4 34.125 21.7 34.4 21.15 34.95C20.6 35.5 20.325 36.2 20.325 37.05C20.325 37.9 20.6 38.6 21.15 39.15L29.7 47.7C30.3 48.3 31 48.6 31.8 48.6C32.6 48.6 33.3 48.3 33.9 47.7L50.85 30.75C51.4 30.2 51.675 29.5 51.675 28.65C51.675 27.8 51.4 27.1 50.85 26.55C50.3 26 49.6 25.725 48.75 25.725C47.9 25.725 47.2 26 46.65 26.55L31.8 41.4ZM36 66C31.85 66 27.95 65.2125 24.3 63.6375C20.65 62.0625 17.475 59.925 14.775 57.225C12.075 54.525 9.9375 51.35 8.3625 47.7C6.7875 44.05 6 40.15 6 36C6 31.85 6.7875 27.95 8.3625 24.3C9.9375 20.65 12.075 17.475 14.775 14.775C17.475 12.075 20.65 9.9375 24.3 8.3625C27.95 6.7875 31.85 6 36 6C40.15 6 44.05 6.7875 47.7 8.3625C51.35 9.9375 54.525 12.075 57.225 14.775C59.925 17.475 62.0625 20.65 63.6375 24.3C65.2125 27.95 66 31.85 66 36C66 40.15 65.2125 44.05 63.6375 47.7C62.0625 51.35 59.925 54.525 57.225 57.225C54.525 59.925 51.35 62.0625 47.7 63.6375C44.05 65.2125 40.15 66 36 66ZM36 60C42.7 60 48.375 57.675 53.025 53.025C57.675 48.375 60 42.7 60 36C60 29.3 57.675 23.625 53.025 18.975C48.375 14.325 42.7 12 36 12C29.3 12 23.625 14.325 18.975 18.975C14.325 23.625 12 29.3 12 36C12 42.7 14.325 48.375 18.975 53.025C23.625 57.675 29.3 60 36 60Z"
          fill={color}
        />
      </g>
    </svg>
  );
};

export default CheckCircleIcon;
